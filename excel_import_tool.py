import tkinter as tk
from tkinter import filedialog, messagebox
import pandas as pd
import pyodbc

# Constants
DB_PATH = r"\\ant\dept-in\Digiflex\DF-Cataloghealth\Quality Products\Tickets\TT Details Database.accdb"
TABLE_NAME = "Task Allocation"
COLUMNS = [
    "QC Date", "User ID", "AHT", "Series GTI", "Series Name", "Line of business", 
    "Territory", "Partner", "Impressions", "Season/Episode", "Input Date", 
    "Product Episode Missing", "Product Season Missing", "Offer Episode Missing", 
    "Offer Season Missing", "Last Episode Missing", "Wrong Content Playing"
]

def import_excel():
    # Select Excel file
    file_path = filedialog.askopenfilename(
        title="Select Excel File",
        filetypes=[("Excel files", "*.xlsx *.xls")]
    )
    
    if not file_path:
        return
    
    try:
        # Read Excel file
        df = pd.read_excel(file_path)
        
        # Validate headers
        missing = [col for col in COLUMNS if col not in df.columns]
        if missing:
            messagebox.showerror("Error", f"Missing columns: {', '.join(missing)}")
            return
        
        # Clean data
        df = df[COLUMNS].fillna("")
        
        # Show progress indicator
        progress = tk.Toplevel(root)
        progress.title("Importing")
        tk.Label(progress, text="Importing data...").pack(pady=20)
        progress.update()
        
        # Connect to database
        conn = pyodbc.connect(f"DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={DB_PATH};")
        cursor = conn.cursor()
        
        # Prepare SQL statement
        columns_str = ','.join([f'[{col}]' for col in COLUMNS])
        placeholders = ','.join(['?'] * len(COLUMNS))
        sql = f"INSERT INTO [{TABLE_NAME}] ({columns_str}) VALUES ({placeholders})"
        
        # Batch processing
        cursor.fast_executemany = True
        data = [tuple(row[col] for col in COLUMNS) for _, row in df.iterrows()]
        cursor.executemany(sql, data)
        
        # Commit and close
        conn.commit()
        conn.close()
        progress.destroy()
        
        messagebox.showinfo("Success", f"{len(data)} rows imported successfully!")
        
    except Exception as e:
        messagebox.showerror("Error", str(e))

# Create GUI
root = tk.Tk()
root.title("Excel to Access Importer")
root.geometry("300x150")

frame = tk.Frame(root)
frame.pack(expand=True)

import_btn = tk.Button(
    frame, 
    text="Import Task", 
    command=import_excel,
    font=("Arial", 12),
    width=15, 
    height=2
)
import_btn.pack(pady=20)

root.mainloop()