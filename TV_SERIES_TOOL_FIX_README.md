# TV Series Task Tool - Fixed Version

This is a fixed version of the TV Series Task Tool that addresses stability issues and prevents crashes.

## What's Fixed

1. **Improved Error Handling**: The tool now catches and logs errors instead of crashing
2. **Memory Management**: Better resource cleanup to prevent memory leaks
3. **Unicode Text Handling**: Enhanced handling of international characters
4. **WebDriver Management**: Proper cleanup of browser instances
5. **Clipboard Operations**: More robust clipboard handling with fallbacks

## How to Use

### Option 1: Use the Launcher Script (Recommended)

1. Double-click on `launch_fixed_tv_series_tool.py` to start the tool
2. If any errors occur, they will be logged to files for troubleshooting

### Option 2: Run Directly

1. Open Command Prompt
2. Navigate to the tool directory
3. Run: `python "TV Series TT Tool_fixed.py"`

## Troubleshooting

If the tool crashes or closes unexpectedly:

1. Check the error log files:
   - `error_log.txt` - General application errors
   - `import_error_log.txt` - Library import errors
   - `row_error_log.txt` - Errors processing specific rows
   - `auto_fill_error_log.txt` - Errors during auto-fill operation

2. Common issues:
   - Database connection problems: Verify network access to the database
   - WebDriver issues: Make sure Chrome is installed and updated
   - Memory issues: Restart your computer if the tool becomes slow

## Reporting Issues

If you continue to experience problems:

1. Collect the error log files
2. Note the steps that led to the issue
3. Contact the tool maintainer with this information