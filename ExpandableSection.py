from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.card import MDCard
from kivymd.uix.button import MDIconButton
from kivymd.uix.label import MDLabel
from kivy.metrics import dp
from kivy.properties import StringProperty, BooleanProperty, ObjectProperty
from kivy.animation import Animation

class ExpandableSection(MDCard):
    """
    A custom expandable section widget with arrow indicator
    
    Properties:
        title: The section title
        expanded: Whether the section is expanded or collapsed
        content: The content widget to be shown/hidden
    """
    title = StringProperty("Section")
    expanded = BooleanProperty(False)
    content = ObjectProperty(None)
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.orientation = "vertical"
        self.padding = dp(10)
        self.spacing = dp(5)
        self.adaptive_height = True
        
        # Create header layout
        self.header = MDBoxLayout(
            orientation="horizontal",
            size_hint_y=None,
            height=dp(40),
            padding=[dp(10), 0, dp(10), 0]
        )
        
        # Create title label
        self.title_label = MDLabel(
            text=self.title,
            theme_text_color="Primary",
            font_style="H6",
            size_hint_x=0.9
        )
        
        # Create toggle button with arrow icon
        self.toggle_button = MDIconButton(
            icon="chevron-down",
            size_hint_x=0.1,
            on_release=self.toggle_expansion
        )
        
        # Add widgets to header
        self.header.add_widget(self.title_label)
        self.header.add_widget(self.toggle_button)
        
        # Add header to card
        self.add_widget(self.header)
        
        # Content container
        self.content_container = MDBoxLayout(
            orientation="vertical",
            size_hint_y=None,
            height=0,
            opacity=0
        )
        
        # Add content container to card
        self.add_widget(self.content_container)
        
        # Bind properties
        self.bind(title=self._update_title)
        self.bind(expanded=self._update_expanded)
        self.bind(content=self._update_content)
    
    def _update_title(self, instance, value):
        """Update title when property changes"""
        self.title_label.text = value
    
    def _update_expanded(self, instance, value):
        """Update expanded state when property changes"""
        if value:
            self.expand()
        else:
            self.collapse()
    
    def _update_content(self, instance, value):
        """Update content when property changes"""
        self.content_container.clear_widgets()
        if value:
            self.content_container.add_widget(value)
            # Store original height for animations
            value.bind(height=self._update_content_height)
            self._original_height = value.height
            
            # Update initial state
            if self.expanded:
                self.content_container.height = self._original_height
                self.content_container.opacity = 1
            else:
                self.content_container.height = 0
                self.content_container.opacity = 0
    
    def _update_content_height(self, instance, value):
        """Update container height when content height changes"""
        if self.expanded:
            self.content_container.height = value
    
    def toggle_expansion(self, instance):
        """Toggle between expanded and collapsed states"""
        self.expanded = not self.expanded
    
    def expand(self):
        """Expand the section"""
        if self.content:
            # Update arrow icon
            self.toggle_button.icon = "chevron-up"
            
            # Animate expansion
            anim = Animation(height=self.content.height, opacity=1, duration=0.3)
            anim.start(self.content_container)
    
    def collapse(self):
        """Collapse the section"""
        # Update arrow icon
        self.toggle_button.icon = "chevron-down"
        
        # Animate collapse
        anim = Animation(height=0, opacity=0, duration=0.3)
        anim.start(self.content_container)