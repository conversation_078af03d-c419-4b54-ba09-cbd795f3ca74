import pyodbc
import os
import time
import threading
from contextlib import contextmanager

class DatabaseConnectionFix:
    def __init__(self):
        self.db_path = r"\\ant\dept-in\Digiflex\DF-Cataloghealth\Quality Products\Audits\Workflow\Workflow database.accdb"
        self.conn_str = f"DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={self.db_path};"
        
    def test_and_fix_connection(self):
        """Test connection and apply fixes"""
        print("Testing database connection...")
        
        # Test 1: Basic path check
        if not os.path.exists(self.db_path):
            print("ERROR: Database file not accessible")
            print("Solutions:")
            print("1. Check VPN connection")
            print("2. Verify network drive mapping")
            print("3. Check file permissions")
            return False
            
        # Test 2: Driver check
        drivers = [x for x in pyodbc.drivers() if 'Access' in x]
        if not drivers:
            print("ERROR: No Access drivers found")
            print("Install Microsoft Access Database Engine")
            return False
            
        # Test 3: Connection with retry logic
        return self.test_connection_with_retry()
    
    def test_connection_with_retry(self, max_retries=3):
        """Test connection with retry mechanism"""
        for attempt in range(max_retries):
            try:
                print(f"Connection attempt {attempt + 1}...")
                
                # Try different connection string variations
                conn_strings = [
                    f"DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={self.db_path};",
                    f"DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={self.db_path};ReadOnly=0;",
                    f"DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={self.db_path};Exclusive=0;",
                ]
                
                for i, conn_str in enumerate(conn_strings):
                    try:
                        print(f"  Trying connection string variant {i+1}...")
                        conn = pyodbc.connect(conn_str, timeout=10)
                        
                        # Test basic query
                        cursor = conn.cursor()
                        cursor.execute("SELECT COUNT(*) FROM Workflow")
                        count = cursor.fetchone()[0]
                        
                        cursor.close()
                        conn.close()
                        
                        print(f"SUCCESS: Connected! Found {count} records in Workflow table")
                        print(f"Working connection string: {conn_str}")
                        return True
                        
                    except pyodbc.Error as e:
                        print(f"  Variant {i+1} failed: {str(e)[:100]}...")
                        continue
                
                # If all variants failed, wait before retry
                if attempt < max_retries - 1:
                    print(f"All variants failed. Waiting 2 seconds before retry...")
                    time.sleep(2)
                    
            except Exception as e:
                print(f"Attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)
        
        print("All connection attempts failed")
        return False
    
    def get_working_connection_string(self):
        """Return a working connection string after testing"""
        conn_strings = [
            f"DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={self.db_path};",
            f"DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={self.db_path};ReadOnly=0;",
            f"DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={self.db_path};Exclusive=0;",
        ]
        
        for conn_str in conn_strings:
            try:
                conn = pyodbc.connect(conn_str, timeout=5)
                conn.close()
                return conn_str
            except:
                continue
        return None

def fix_quality_app_connection():
    """Apply fix to the QualityApp connection string"""
    
    # Test the connection first
    db_fix = DatabaseConnectionFix()
    if not db_fix.test_and_fix_connection():
        print("Cannot establish database connection. Please check:")
        print("1. VPN connection")
        print("2. Network permissions") 
        print("3. Database file accessibility")
        return False
    
    # Get working connection string
    working_conn_str = db_fix.get_working_connection_string()
    if not working_conn_str:
        print("No working connection string found")
        return False
    
    print(f"\nWorking connection string found:")
    print(f"'{working_conn_str}'")
    
    # Update the QualityApp file
    try:
        quality_app_file = r"c:\Users\<USER>\Desktop\Python\Tools\QMT(Quality Products).py"
        
        with open(quality_app_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find and replace the connection string
        old_conn_str = 'r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};"\r\n            r"DBQ=\\\\ant\\dept-in\\Digiflex\\DF-Cataloghealth\\Quality Products\\Audits\\Workflow\\Workflow database.accdb;"'
        
        new_conn_str = f'r"{working_conn_str}"'
        
        if old_conn_str in content:
            content = content.replace(old_conn_str, new_conn_str)
            
            with open(quality_app_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"\nUpdated connection string in {quality_app_file}")
            print("The application should now work properly.")
            return True
        else:
            print("Could not find the exact connection string pattern to replace")
            print("Manual update required")
            return False
            
    except Exception as e:
        print(f"Error updating file: {e}")
        return False

if __name__ == "__main__":
    print("=== Database Connection Fix Tool ===\n")
    fix_quality_app_connection()
    input("\nPress Enter to exit...")