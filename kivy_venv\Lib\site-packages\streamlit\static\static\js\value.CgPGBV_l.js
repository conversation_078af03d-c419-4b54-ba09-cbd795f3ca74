function qn(n){return Math.abs(n=Math.round(n))>=1e21?n.toLocaleString("en").replace(/,/g,""):n.toString(10)}function T(n,e){if((r=(n=e?n.toExponential(e-1):n.toExponential()).indexOf("e"))<0)return null;var r,t=n.slice(0,r);return[t.length>1?t[0]+t.slice(2):t,+n.slice(r+1)]}function zn(n){return n=T(Math.abs(n)),n?n[1]:NaN}function Cn(n,e){return function(r,t){for(var i=r.length,a=[],f=0,o=n[0],u=0;i>0&&o>0&&(u+o+1>t&&(o=Math.max(1,t-u)),a.push(r.substring(i-=o,i+o)),!((u+=o+1)>t));)o=n[f=(f+1)%n.length];return a.reverse().join(e)}}function In(n){return function(e){return e.replace(/[0-9]/g,function(r){return n[+r]})}}var _n=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Z(n){if(!(e=_n.exec(n)))throw new Error("invalid format: "+n);var e;return new Q({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}Z.prototype=Q.prototype;function Q(n){this.fill=n.fill===void 0?" ":n.fill+"",this.align=n.align===void 0?">":n.align+"",this.sign=n.sign===void 0?"-":n.sign+"",this.symbol=n.symbol===void 0?"":n.symbol+"",this.zero=!!n.zero,this.width=n.width===void 0?void 0:+n.width,this.comma=!!n.comma,this.precision=n.precision===void 0?void 0:+n.precision,this.trim=!!n.trim,this.type=n.type===void 0?"":n.type+""}Q.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function Dn(n){n:for(var e=n.length,r=1,t=-1,i;r<e;++r)switch(n[r]){case".":t=i=r;break;case"0":t===0&&(t=r),i=r;break;default:if(!+n[r])break n;t>0&&(t=0);break}return t>0?n.slice(0,t)+n.slice(i+1):n}var yn;function Ln(n,e){var r=T(n,e);if(!r)return n+"";var t=r[0],i=r[1],a=i-(yn=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,f=t.length;return a===f?t:a>f?t+new Array(a-f+1).join("0"):a>0?t.slice(0,a)+"."+t.slice(a):"0."+new Array(1-a).join("0")+T(n,Math.max(0,e+a-1))[0]}function fn(n,e){var r=T(n,e);if(!r)return n+"";var t=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+t:t.length>i+1?t.slice(0,i+1)+"."+t.slice(i+1):t+new Array(i-t.length+2).join("0")}const an={"%":(n,e)=>(n*100).toFixed(e),b:n=>Math.round(n).toString(2),c:n=>n+"",d:qn,e:(n,e)=>n.toExponential(e),f:(n,e)=>n.toFixed(e),g:(n,e)=>n.toPrecision(e),o:n=>Math.round(n).toString(8),p:(n,e)=>fn(n*100,e),r:fn,s:Ln,X:n=>Math.round(n).toString(16).toUpperCase(),x:n=>Math.round(n).toString(16)};function on(n){return n}var sn=Array.prototype.map,un=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function On(n){var e=n.grouping===void 0||n.thousands===void 0?on:Cn(sn.call(n.grouping,Number),n.thousands+""),r=n.currency===void 0?"":n.currency[0]+"",t=n.currency===void 0?"":n.currency[1]+"",i=n.decimal===void 0?".":n.decimal+"",a=n.numerals===void 0?on:In(sn.call(n.numerals,String)),f=n.percent===void 0?"%":n.percent+"",o=n.minus===void 0?"−":n.minus+"",u=n.nan===void 0?"NaN":n.nan+"";function N(c){c=Z(c);var R=c.fill,S=c.align,m=c.sign,H=c.symbol,k=c.zero,E=c.width,X=c.comma,y=c.precision,nn=c.trim,x=c.type;x==="n"?(X=!0,x="g"):an[x]||(y===void 0&&(y=12),nn=!0,x="g"),(k||R==="0"&&S==="=")&&(k=!0,R="0",S="=");var En=H==="$"?r:H==="#"&&/[boxX]/.test(x)?"0"+x.toLowerCase():"",Pn=H==="$"?t:/[%p]/.test(x)?f:"",en=an[x],jn=/[defgprs%]/.test(x);y=y===void 0?6:/[gprs]/.test(x)?Math.max(1,Math.min(21,y)):Math.max(0,Math.min(20,y));function rn(s){var w=En,d=Pn,v,tn,C;if(x==="c")d=en(s)+d,s="";else{s=+s;var I=s<0||1/s<0;if(s=isNaN(s)?u:en(Math.abs(s),y),nn&&(s=Dn(s)),I&&+s==0&&m!=="+"&&(I=!1),w=(I?m==="("?m:o:m==="-"||m==="("?"":m)+w,d=(x==="s"?un[8+yn/3]:"")+d+(I&&m==="("?")":""),jn){for(v=-1,tn=s.length;++v<tn;)if(C=s.charCodeAt(v),48>C||C>57){d=(C===46?i+s.slice(v+1):s.slice(v))+d,s=s.slice(0,v);break}}}X&&!k&&(s=e(s,1/0));var _=w.length+s.length+d.length,p=_<E?new Array(E-_+1).join(R):"";switch(X&&k&&(s=e(p+s,p.length?E-d.length:1/0),p=""),S){case"<":s=w+s+d+p;break;case"=":s=w+p+s+d;break;case"^":s=p.slice(0,_=p.length>>1)+w+s+d+p.slice(_);break;default:s=p+w+s+d;break}return a(s)}return rn.toString=function(){return c+""},rn}function g(c,R){var S=N((c=Z(c),c.type="f",c)),m=Math.max(-8,Math.min(8,Math.floor(zn(R)/3)))*3,H=Math.pow(10,-m),k=un[8+m/3];return function(E){return S(H*E)+k}}return{format:N,formatPrefix:g}}var D,Tn,Bn;Fn({thousands:",",grouping:[3],currency:["$",""]});function Fn(n){return D=On(n),Tn=D.format,Bn=D.formatPrefix,D}function W(n,e,r){n.prototype=e.prototype=r,r.constructor=n}function wn(n,e){var r=Object.create(n.prototype);for(var t in e)r[t]=e[t];return r}function z(){}var P=.7,B=1/P,A="\\s*([+-]?\\d+)\\s*",j="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",b="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Gn=/^#([0-9a-f]{3,8})$/,Vn=new RegExp(`^rgb\\(${A},${A},${A}\\)$`),Xn=new RegExp(`^rgb\\(${b},${b},${b}\\)$`),Un=new RegExp(`^rgba\\(${A},${A},${A},${j}\\)$`),Yn=new RegExp(`^rgba\\(${b},${b},${b},${j}\\)$`),Zn=new RegExp(`^hsl\\(${j},${b},${b}\\)$`),Jn=new RegExp(`^hsla\\(${j},${b},${b},${j}\\)$`),cn={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};W(z,q,{copy(n){return Object.assign(new this.constructor,this,n)},displayable(){return this.rgb().displayable()},hex:hn,formatHex:hn,formatHex8:Kn,formatHsl:Qn,formatRgb:xn,toString:xn});function hn(){return this.rgb().formatHex()}function Kn(){return this.rgb().formatHex8()}function Qn(){return $n(this).formatHsl()}function xn(){return this.rgb().formatRgb()}function q(n){var e,r;return n=(n+"").trim().toLowerCase(),(e=Gn.exec(n))?(r=e[1].length,e=parseInt(e[1],16),r===6?dn(e):r===3?new h(e>>8&15|e>>4&240,e>>4&15|e&240,(e&15)<<4|e&15,1):r===8?L(e>>24&255,e>>16&255,e>>8&255,(e&255)/255):r===4?L(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|e&240,((e&15)<<4|e&15)/255):null):(e=Vn.exec(n))?new h(e[1],e[2],e[3],1):(e=Xn.exec(n))?new h(e[1]*255/100,e[2]*255/100,e[3]*255/100,1):(e=Un.exec(n))?L(e[1],e[2],e[3],e[4]):(e=Yn.exec(n))?L(e[1]*255/100,e[2]*255/100,e[3]*255/100,e[4]):(e=Zn.exec(n))?mn(e[1],e[2]/100,e[3]/100,1):(e=Jn.exec(n))?mn(e[1],e[2]/100,e[3]/100,e[4]):cn.hasOwnProperty(n)?dn(cn[n]):n==="transparent"?new h(NaN,NaN,NaN,0):null}function dn(n){return new h(n>>16&255,n>>8&255,n&255,1)}function L(n,e,r,t){return t<=0&&(n=e=r=NaN),new h(n,e,r,t)}function Wn(n){return n instanceof z||(n=q(n)),n?(n=n.rgb(),new h(n.r,n.g,n.b,n.opacity)):new h}function F(n,e,r,t){return arguments.length===1?Wn(n):new h(n,e,r,t??1)}function h(n,e,r,t){this.r=+n,this.g=+e,this.b=+r,this.opacity=+t}W(h,F,wn(z,{brighter(n){return n=n==null?B:Math.pow(B,n),new h(this.r*n,this.g*n,this.b*n,this.opacity)},darker(n){return n=n==null?P:Math.pow(P,n),new h(this.r*n,this.g*n,this.b*n,this.opacity)},rgb(){return this},clamp(){return new h(M(this.r),M(this.g),M(this.b),G(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:ln,formatHex:ln,formatHex8:ne,formatRgb:gn,toString:gn}));function ln(){return`#${$(this.r)}${$(this.g)}${$(this.b)}`}function ne(){return`#${$(this.r)}${$(this.g)}${$(this.b)}${$((isNaN(this.opacity)?1:this.opacity)*255)}`}function gn(){const n=G(this.opacity);return`${n===1?"rgb(":"rgba("}${M(this.r)}, ${M(this.g)}, ${M(this.b)}${n===1?")":`, ${n})`}`}function G(n){return isNaN(n)?1:Math.max(0,Math.min(1,n))}function M(n){return Math.max(0,Math.min(255,Math.round(n)||0))}function $(n){return n=M(n),(n<16?"0":"")+n.toString(16)}function mn(n,e,r,t){return t<=0?n=e=r=NaN:r<=0||r>=1?n=e=NaN:e<=0&&(n=NaN),new l(n,e,r,t)}function $n(n){if(n instanceof l)return new l(n.h,n.s,n.l,n.opacity);if(n instanceof z||(n=q(n)),!n)return new l;if(n instanceof l)return n;n=n.rgb();var e=n.r/255,r=n.g/255,t=n.b/255,i=Math.min(e,r,t),a=Math.max(e,r,t),f=NaN,o=a-i,u=(a+i)/2;return o?(e===a?f=(r-t)/o+(r<t)*6:r===a?f=(t-e)/o+2:f=(e-r)/o+4,o/=u<.5?a+i:2-a-i,f*=60):o=u>0&&u<1?0:f,new l(f,o,u,n.opacity)}function ee(n,e,r,t){return arguments.length===1?$n(n):new l(n,e,r,t??1)}function l(n,e,r,t){this.h=+n,this.s=+e,this.l=+r,this.opacity=+t}W(l,ee,wn(z,{brighter(n){return n=n==null?B:Math.pow(B,n),new l(this.h,this.s,this.l*n,this.opacity)},darker(n){return n=n==null?P:Math.pow(P,n),new l(this.h,this.s,this.l*n,this.opacity)},rgb(){var n=this.h%360+(this.h<0)*360,e=isNaN(n)||isNaN(this.s)?0:this.s,r=this.l,t=r+(r<.5?r:1-r)*e,i=2*r-t;return new h(U(n>=240?n-240:n+120,i,t),U(n,i,t),U(n<120?n+240:n-120,i,t),this.opacity)},clamp(){return new l(bn(this.h),O(this.s),O(this.l),G(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const n=G(this.opacity);return`${n===1?"hsl(":"hsla("}${bn(this.h)}, ${O(this.s)*100}%, ${O(this.l)*100}%${n===1?")":`, ${n})`}`}}));function bn(n){return n=(n||0)%360,n<0?n+360:n}function O(n){return Math.max(0,Math.min(1,n||0))}function U(n,e,r){return(n<60?e+(r-e)*n/60:n<180?r:n<240?e+(r-e)*(240-n)/60:e)*255}function Mn(n,e,r,t,i){var a=n*n,f=a*n;return((1-3*n+3*a-f)*e+(4-6*a+3*f)*r+(1+3*n+3*a-3*f)*t+f*i)/6}function re(n){var e=n.length-1;return function(r){var t=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),i=n[t],a=n[t+1],f=t>0?n[t-1]:2*i-a,o=t<e-1?n[t+2]:2*a-i;return Mn((r-t/e)*e,f,i,a,o)}}function te(n){var e=n.length;return function(r){var t=Math.floor(((r%=1)<0?++r:r)*e),i=n[(t+e-1)%e],a=n[t%e],f=n[(t+1)%e],o=n[(t+2)%e];return Mn((r-t/e)*e,i,a,f,o)}}const V=n=>()=>n;function Nn(n,e){return function(r){return n+r*e}}function ie(n,e,r){return n=Math.pow(n,r),e=Math.pow(e,r)-n,r=1/r,function(t){return Math.pow(n+t*e,r)}}function he(n,e){var r=e-n;return r?Nn(n,r>180||r<-180?r-360*Math.round(r/360):r):V(isNaN(n)?e:n)}function fe(n){return(n=+n)==1?kn:function(e,r){return r-e?ie(e,r,n):V(isNaN(e)?r:e)}}function kn(n,e){var r=e-n;return r?Nn(n,r):V(isNaN(n)?e:n)}const pn=function n(e){var r=fe(e);function t(i,a){var f=r((i=F(i)).r,(a=F(a)).r),o=r(i.g,a.g),u=r(i.b,a.b),N=kn(i.opacity,a.opacity);return function(g){return i.r=f(g),i.g=o(g),i.b=u(g),i.opacity=N(g),i+""}}return t.gamma=n,t}(1);function vn(n){return function(e){var r=e.length,t=new Array(r),i=new Array(r),a=new Array(r),f,o;for(f=0;f<r;++f)o=F(e[f]),t[f]=o.r||0,i[f]=o.g||0,a[f]=o.b||0;return t=n(t),i=n(i),a=n(a),o.opacity=1,function(u){return o.r=t(u),o.g=i(u),o.b=a(u),o+""}}}var xe=vn(re),de=vn(te);function An(n,e){e||(e=[]);var r=n?Math.min(e.length,n.length):0,t=e.slice(),i;return function(a){for(i=0;i<r;++i)t[i]=n[i]*(1-a)+e[i]*a;return t}}function Rn(n){return ArrayBuffer.isView(n)&&!(n instanceof DataView)}function le(n,e){return(Rn(e)?An:Sn)(n,e)}function Sn(n,e){var r=e?e.length:0,t=n?Math.min(r,n.length):0,i=new Array(t),a=new Array(r),f;for(f=0;f<t;++f)i[f]=Hn(n[f],e[f]);for(;f<r;++f)a[f]=e[f];return function(o){for(f=0;f<t;++f)a[f]=i[f](o);return a}}function ae(n,e){var r=new Date;return n=+n,e=+e,function(t){return r.setTime(n*(1-t)+e*t),r}}function J(n,e){return n=+n,e=+e,function(r){return n*(1-r)+e*r}}function oe(n,e){var r={},t={},i;(n===null||typeof n!="object")&&(n={}),(e===null||typeof e!="object")&&(e={});for(i in e)i in n?r[i]=Hn(n[i],e[i]):t[i]=e[i];return function(a){for(i in r)t[i]=r[i](a);return t}}var K=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Y=new RegExp(K.source,"g");function se(n){return function(){return n}}function ue(n){return function(e){return n(e)+""}}function ce(n,e){var r=K.lastIndex=Y.lastIndex=0,t,i,a,f=-1,o=[],u=[];for(n=n+"",e=e+"";(t=K.exec(n))&&(i=Y.exec(e));)(a=i.index)>r&&(a=e.slice(r,a),o[f]?o[f]+=a:o[++f]=a),(t=t[0])===(i=i[0])?o[f]?o[f]+=i:o[++f]=i:(o[++f]=null,u.push({i:f,x:J(t,i)})),r=Y.lastIndex;return r<e.length&&(a=e.slice(r),o[f]?o[f]+=a:o[++f]=a),o.length<2?u[0]?ue(u[0].x):se(e):(e=u.length,function(N){for(var g=0,c;g<e;++g)o[(c=u[g]).i]=c.x(N);return o.join("")})}function Hn(n,e){var r=typeof e,t;return e==null||r==="boolean"?V(e):(r==="number"?J:r==="string"?(t=q(e))?(e=t,pn):ce:e instanceof q?pn:e instanceof Date?ae:Rn(e)?An:Array.isArray(e)?Sn:typeof e.valueOf!="function"&&typeof e.toString!="function"||isNaN(e)?oe:J)(n,e)}export{q as A,z as C,h as R,Z as a,Tn as b,Bn as c,W as d,wn as e,On as f,P as g,B as h,he as i,ee as j,Hn as k,le as l,re as m,kn as n,te as o,ae as p,J as q,Wn as r,An as s,oe as t,pn as u,xe as v,de as w,ce as x,F as y,zn as z};
