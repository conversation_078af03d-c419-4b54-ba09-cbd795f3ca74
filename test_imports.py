# Test imports
try:
    import langchain_community
    print("langchain_community imported successfully")
    
    from langchain_community.chat_models import ChatOpenAI
    print("ChatOpenAI imported successfully")
    
    from langchain.chains import RetrievalQA  # Changed to langchain
    print("RetrievalQA imported successfully")
    
    from langchain_community.vectorstores import FAISS
    print("FAISS imported successfully")
    
    from langchain_community.embeddings.openai import OpenAIEmbeddings
    print("OpenAIEmbeddings imported successfully")
    
    from langchain_text_splitters import CharacterTextSplitter
    print("CharacterTextSplitter imported successfully")
    
    from langchain_core.documents import Document
    print("Document imported successfully")
    
    print("All imports successful!")
except ImportError as e:
    print(f"Import error: {e}")