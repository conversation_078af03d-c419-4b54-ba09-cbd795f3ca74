import sys
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ._width import Width<PERSON>alidator
    from ._dash import <PERSON>V<PERSON>dator
    from ._color import ColorValidator
else:
    from _plotly_utils.importers import relative_import

    __all__, __getattr__, __dir__ = relative_import(
        __name__,
        [],
        ["._width.WidthValidator", "._dash.DashValidator", "._color.ColorValidator"],
    )
