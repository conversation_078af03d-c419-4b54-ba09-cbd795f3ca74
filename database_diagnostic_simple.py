import pyodbc
import os

def test_database_connection():
    """Test database connection and diagnose issues."""
    
    # Database path
    db_path = r"\\ant\dept-in\Digiflex\DF-Cataloghealth\Quality Products\Audits\Workflow\Workflow database.accdb"
    
    print("=== Database Connection Diagnostic Tool ===")
    print()
    
    # Test 1: Check if network path is accessible
    print("1. Testing network path accessibility...")
    try:
        if os.path.exists(db_path):
            print("   [OK] Network path is accessible")
            
            # Check file permissions
            if os.access(db_path, os.R_OK):
                print("   [OK] File is readable")
            else:
                print("   [ERROR] File is not readable - permission issue")
                
            if os.access(db_path, os.W_OK):
                print("   [OK] File is writable")
            else:
                print("   [WARNING] File is not writable - may cause issues")
                
        else:
            print("   [ERROR] Network path is NOT accessible")
            print("   Possible solutions:")
            print("     - Check if the network drive is mapped")
            print("     - Verify VPN connection if required")
            print("     - Check network permissions")
            return False
            
    except Exception as e:
        print(f"   [ERROR] Error accessing path: {e}")
        return False
    
    # Test 2: Check Access driver availability
    print("\n2. Testing Access driver availability...")
    try:
        drivers = [x for x in pyodbc.drivers() if 'Access' in x]
        if drivers:
            print(f"   [OK] Access drivers found: {drivers}")
        else:
            print("   [ERROR] No Access drivers found")
            print("   Solution: Install Microsoft Access Database Engine")
            return False
    except Exception as e:
        print(f"   [ERROR] Error checking drivers: {e}")
        return False
    
    # Test 3: Test basic connection
    print("\n3. Testing database connection...")
    conn_str = f"DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={db_path};"
    
    try:
        conn = pyodbc.connect(conn_str)
        print("   [OK] Basic connection successful")
        
        # Test 4: Test query execution
        print("\n4. Testing query execution...")
        cursor = conn.cursor()
        
        # Get table list
        tables = [table[2] for table in cursor.tables(tableType='TABLE')]
        print(f"   [OK] Found {len(tables)} tables")
        
        # Test Workflow table specifically
        if 'Workflow' in tables:
            print("\n5. Testing Workflow table access...")
            cursor.execute("SELECT COUNT(*) FROM Workflow")
            count = cursor.fetchone()[0]
            print(f"   [OK] Workflow table has {count} records")
            
            # Test process retrieval
            cursor.execute("SELECT DISTINCT Process FROM Workflow")
            processes = cursor.fetchall()
            print(f"   [OK] Found {len(processes)} distinct processes")
            
            # Test subprocess retrieval for first process
            if processes:
                first_process = processes[0][0]
                cursor.execute("SELECT DISTINCT [Sub Process] FROM Workflow WHERE Process = ?", (first_process,))
                subprocesses = cursor.fetchall()
                print(f"   [OK] Process '{first_process}' has {len(subprocesses)} subprocesses")
        else:
            print("\n5. [ERROR] Workflow table not found!")
            print(f"   Available tables: {tables}")
        
        cursor.close()
        conn.close()
        print("\n[SUCCESS] All tests passed! Database connection is working properly.")
        return True
        
    except pyodbc.Error as e:
        print(f"   [ERROR] Database connection failed: {e}")
        print("\n   Possible solutions:")
        print("     - Database may be locked by another user")
        print("     - Check if database file is corrupted")
        print("     - Verify database permissions")
        print("     - Try closing any Access applications")
        return False
    except Exception as e:
        print(f"   [ERROR] Unexpected error: {e}")
        return False

def suggest_fixes():
    """Suggest common fixes for database connection issues."""
    print("\n=== Common Solutions ===")
    print("1. Database Locking Issues:")
    print("   - Close Microsoft Access if it's open")
    print("   - Check for .ldb or .laccdb lock files and delete them")
    print("   - Restart the application")
    
    print("\n2. Network Issues:")
    print("   - Verify VPN connection")
    print("   - Map the network drive manually")
    print("   - Check network permissions with IT")
    
    print("\n3. Driver Issues:")
    print("   - Install Microsoft Access Database Engine 2016 Redistributable")
    print("   - Ensure 64-bit Python matches 64-bit Access driver")
    
    print("\n4. Alternative Connection String:")
    print("   Try using UNC path format or mapped drive letter")

if __name__ == "__main__":
    success = test_database_connection()
    if not success:
        suggest_fixes()
    
    input("\nPress Enter to exit...")