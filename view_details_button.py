def view_season_episode_details(self, instance):
    """Show Season/Episode details when View Details button is clicked"""
    # Get the Season/Episode data from the task entries
    season_episode_data = self.task_entries.get("Season/Episode", MDTextField()).text
    territory_data = self.task_entries.get("Territory (T)", MDTextField()).text
    
    if season_episode_data:
        # Show the dialog with the data
        self.show_season_episode_dialog(season_episode_data, territory_data)
    else:
        # Show a message if no data is available
        dialog = MDDialog(title="No Data", text="No Season/Episode data available to display.")
        dialog.open()