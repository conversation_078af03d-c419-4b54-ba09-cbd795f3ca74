import pyodbc

# Test the exact queries used in your application
db_path = r"\\ant\dept-in\Digiflex\DF-Cataloghealth\Quality Products\Audits\Workflow\Workflow database.accdb"
conn_str = f"DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={db_path};"

print("Testing application-specific queries...")

try:
    conn = pyodbc.connect(conn_str)
    cursor = conn.cursor()
    
    # Test 1: Get all processes (same as preload_processes)
    print("\n1. Testing process loading...")
    cursor.execute("SELECT DISTINCT Process FROM Workflow")
    processes = cursor.fetchall()
    print(f"Found {len(processes)} processes:")
    for i, process in enumerate(processes[:5]):  # Show first 5
        print(f"  {i+1}. {process[0]}")
    if len(processes) > 5:
        print(f"  ... and {len(processes)-5} more")
    
    # Test 2: Get sub-processes for first process
    if processes:
        first_process = processes[0][0]
        print(f"\n2. Testing sub-process loading for '{first_process}'...")
        cursor.execute("SELECT DISTINCT [Sub Process] FROM Workflow WHERE Process = ?", (first_process,))
        sub_processes = cursor.fetchall()
        print(f"Found {len(sub_processes)} sub-processes:")
        for i, sub_proc in enumerate(sub_processes[:5]):  # Show first 5
            print(f"  {i+1}. {sub_proc[0]}")
        if len(sub_processes) > 5:
            print(f"  ... and {len(sub_processes)-5} more")
        
        # Test 3: Get quality checks for first sub-process
        if sub_processes:
            first_subprocess = sub_processes[0][0]
            print(f"\n3. Testing quality checks for '{first_process}' - '{first_subprocess}'...")
            query = "SELECT [Checks Category], [Audit Checklist], [Field], [Error Category], [Score] FROM Workflow WHERE [Process] = ? AND [Sub Process] = ?"
            cursor.execute(query, (first_process, first_subprocess))
            quality_checks = cursor.fetchall()
            print(f"Found {len(quality_checks)} quality checks:")
            for i, check in enumerate(quality_checks[:3]):  # Show first 3
                print(f"  {i+1}. Category: {check[0]}, Checklist: {check[1][:50]}...")
            if len(quality_checks) > 3:
                print(f"  ... and {len(quality_checks)-3} more")
    
    cursor.close()
    conn.close()
    print("\n[SUCCESS] All queries executed successfully!")
    
except Exception as e:
    print(f"\n[ERROR] Query failed: {e}")
    import traceback
    traceback.print_exc()

print("\nTest complete.")