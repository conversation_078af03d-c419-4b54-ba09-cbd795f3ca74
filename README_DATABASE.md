# TV Series Task Tool - Database Integration

This application now uses direct database integration instead of Excel file imports. The tool connects to an Access database to fetch tasks and update their status.

## Features

- Connects directly to the Access database at `\\ant\dept-in\Digiflex\DF-Cataloghealth\Quality Products\Tickets\TT Details Database.accdb`
- Automatically creates the "Task Allocation" table if it doesn't exist
- Populates sample data for testing purposes
- Fetches tasks assigned to the current user when clicking "Get Task" button
- Updates task status to "In Progress" when a task is loaded
- Updates task status to "Completed" when a task is submitted
- Maintains all previous functionality for task processing

## Task Allocation Table Structure

The database table "Task Allocation" contains the following fields:
- ID (AutoIncrement Primary Key)
- QC Date
- User ID
- AHT
- Series GTI
- Series Name
- Line of business
- Territory
- Partner
- Impressions
- Season/Episode
- Input Date
- Product Episode Missing
- Product Season Missing
- Offer Episode Missing
- Offer Season Missing
- Last Episode Missing
- Wrong Content Playing
- Status (Pending, In Progress, Completed)
- EDP Link

## Usage

1. Launch the application
2. Click the "Task" button to view all tasks in the database
3. Select your User ID to see tasks assigned to you
4. Click "Get Task" to load the next pending task
5. Process the task as usual
6. Click "Submit" to complete the task and mark it as "Completed" in the database

## Notes

- The "Import" button still works for importing Excel files for ticket creation
- The database connection is established automatically when the application starts
- Sample data is added to the database if the Task Allocation table is empty