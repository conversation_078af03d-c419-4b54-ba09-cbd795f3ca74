# -*- coding: utf-8 -*-
import sys
import os
import json
import pickle
import atexit
import traceback  # Added for better error logging

# Ensure UTF-8 encoding for all text operations
if sys.version_info[0] >= 3:
    try:
        import io
        if hasattr(sys.stdout, 'buffer') and sys.stdout.buffer:
            sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        if hasattr(sys.stderr, 'buffer') and sys.stderr.buffer:
            sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
    except (AttributeError, OSError):
        pass  # Skip if already configured or not available

# Add global exception handler to prevent crashes
def global_exception_handler(exctype, value, tb):
    error_msg = ''.join(traceback.format_exception(exctype, value, tb))
    print(f"UNCAUGHT EXCEPTION: {error_msg}")
    # Log to file for debugging
    with open("error_log.txt", "a", encoding="utf-8") as f:
        f.write(f"{'-'*50}\n")
        f.write(f"UNCAUGHT EXCEPTION: {error_msg}\n")
    # Don't exit the application
    return True

# Install the global exception handler
sys.excepthook = global_exception_handler

try:
    from kivymd.app import MDApp
    from kivymd.uix.boxlayout import MDBoxLayout
    from kivymd.uix.gridlayout import MDGridLayout
    from kivymd.uix.scrollview import MDScrollView
    from kivymd.uix.label import MDLabel
    from kivymd.uix.textfield import MDTextField
    from kivymd.uix.button import MDRaisedButton, MDFlatButton
    from kivymd.uix.menu import MDDropdownMenu
    from kivymd.uix.dialog import MDDialog
    from kivymd.uix.card import MDCard
    from kivy.metrics import dp
    from kivy.core.window import Window
    from kivy.core.text import LabelBase
    from kivy.effects.scroll import ScrollEffect
    import pyodbc
    import datetime
    import getpass
    import pandas as pd
    from tkinter import filedialog
    import webbrowser
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.common.keys import Keys
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.common.action_chains import ActionChains
    from webdriver_manager.chrome import ChromeDriverManager
    import time
    import unicodedata
    import pyperclip
except Exception as e:
    print(f"Error importing libraries: {e}")
    # Log to file for debugging
    with open("import_error_log.txt", "a", encoding="utf-8") as f:
        f.write(f"{'-'*50}\n")
        f.write(f"IMPORT ERROR: {e}\n")
        f.write(traceback.format_exc())
    sys.exit(1)

# Hardcoded blurb-to-issue mapping
blurbs_dict = {
    "Season Missing": "Season is missing on the EDP/Detail Page",
    "Missing Offers/Avails": "Offers/Avails are missing in EDP/CRMS",
    "Episode Missing": "Episode is missing in EDP/Detail Page",
    "Expired Offers/Avails": "Offers/Avails have expired in EDP/CRMS",
    "Merge Issue": "Title requires merging in the catalog",
    "Unmerge Issue": "Incorrectly merged across vendors or Seasons merged incorrectly, need separate metadata pages",
    "Tech Issue": "Technical issue observed, further investigation needed",
    "Offers Takendown": "Offers have been taken down in EDP",
    "VCID Error": "Episode is missing on the site due to a VCID error",
    "Pricing Issue": "Play/Buy button is missing on the detail page due to a pricing issue",
    "Orphaned Season/Episode": "The Season/Episode are not aligned within the Season/Episode hierarchy",
    "Offer Creations": "Live avails exist, but the offer hasn't been created in EDP",
    "Play Button Missing": "Play/Buy button is missing on the Detail Page",
}

# RCA options mapping
rca_dict = {
    "Future Start Date (Prime)" : "Episode X is missing/unavailable (play button missing) due to a future avail start-date. Please confirm whether this is intended. If not, please request the partner for corrected avail re-delivery.",
    "Expired Prime Avails & Offers" : "Episode X is missing/unavailable due to expired avail. Please confirm whether this is intended. If not, please request a partner for corrected avail re-delivery.",
    "Missing Prime Avails & Offers (No Deal ID)" : "Episode X is missing/unavailable due to no or missing avail. Please confirm whether this is intended. If not, please request the partner for corrected avail re-delivery.",
    "Missing Prime Avails & Offers (With Deal ID)" : " The title is missing prime avails. Looked at Acquire to verify the <dealID> and found that, i.  is still in window, ii.  has the missing title and impacted territories as part of the deal",
    "Future Start Date (TVOD or Channel)" : "Episode X is missing/unavailable due to a future avail start-date. Please confirm whether this is intended. If not, please request partner for corrected avail re-delivery.",
    "Expired TVOD/Channel Avails & Offers" : "Episode X is missing/unavailable due to expired avail. Please confirm whether this is intended. If not, please request partner for corrected avail re-delivery.",
    "Missing TVOD/Channel Avails & Offers" : "Episode X is missing/unavailable due to no or missing avail. Please confirm whether this is intended. If not, please request a partner for corrected avail re-delivery.",
    "Season Missing (Prime)" : "Season X is missing from EDP due to no avail. Please confirm whether this is intended. If not, please request partner for avail re-delivery.",
    "Episode Missing (Prime)" : "Episode X is missing from EDP due to no avail. Please confirm whether this is intended. If not, please request partner for avail re-delivery.",
    "Season Missing (TVOD or Channel)" : "Season X is missing from EDP due to no avail. Please confirm whether this is intended. If not, please request partner for avail re-delivery.",
    "Episode Missing (TVOD or Channel)" : "Episode X is missing from EDP due to no avail. Please confirm whether this is intended. If not, please request partner for avail re-delivery.",
    "Incorrect Sequence" : "Episode X appears to be missing because the episode sequence in avails does not match with episode sequence in metadata. Please confirm which episode sequence is correct and update accordingly.",
    "Merge Issue (1C issue)" : "There are two versions of this title suggesting likely a 1C issue. Version 1 with (insert URN from EDP page 1) and Version 2 with (insert URN from EDP page2). Version 2 is not live and is garnering customer impressions meaning customers are exposed to the wrong version. Requesting 1C team to execute a merge.",
    "Non-Actionable Right Scope" : "Episode X is missing/unavailable due to a non-actionable right scope. Please review if the scope status needs to be updated to make the title live to customers.",
    "Offer Issue (PVD Workflow)" : "This title has in-window avails however offers are not created. We've validated that 'contributing user' is 'pvd-workflow'. Please review whether offers can be created so that this title is made available to customers.",
    "Offer Issue (Non - PVD Workflow)" : "This title has in-window avails however offers are not created. We've validated that this offer is not tied to split-licensing or PVD or season-offer/pass issues. Please review whether offers need to be created so that this title is made available to customers.",
    "Offer Issue (Split License)" : "This title has in-window avails, however offers are not created. We've validated that this offer is tied to split-licensing. Please review whether offers need to be created so that this title is made available to customers.",
    "Offer Issue (Season Page)" : "Season has in-window avails however season-offers / season pass is not live. Please review whether offers need to be created so that season pass/offer is made available to customers.",
    "Offer Issue (Season Pass )" : "This season has in-window avails however season-offers / season pass is not approved. Please review whether offer can be approved so that season pass/offer is made available to customers.",
    "Offer Issue ('Not Approved' or 'Taken Down')" : "This title has in-window avails however offers are not approved. We've validated that 'contributing user' is not 'pvd-workflow' and release_status = 'released' on CST. Please review whether offers need to be approved so that this title is made available to customers.",
    "Mezz Missing" : "This title has in-window avails however offers are not approved. This is due to following failing rules <insert failing rules>. Please take actions to fulfill failing rules so that this title is published to customers.",
    "Offer Takedown (No Duplicate)" : "This title has in-window avails, however offers are taken-down. Please validate if the take-down can be removed so that title is made available to customers.",
    "Offer Takedown (Duplicate)" : "This title has offers taken-down. However, there is another duplicate episode with live offers <insert episode details>. Since there is a live duplicate, requesting team to confirm if they can perform a tombstone or title take-down on the taken-down title to remove the unavailable duplicate from detail page. If take-down/tombstone is advised please route the ticket to appropriate CTI.",
    "TVOD Pricing Issue" : "This title has in-window avails and offers are created/approved. However, this is a TVOD offer and pricing is 0$ or n/a. Please review there is an issue due to pricing that is resulting in the title not being live/available to customers on storefront.",
    "Merge Issue (Duplicate Episode/Season)" : "There are multiple duplicate episodes <note duplicate episode sequence> in the season <note season sequence>. Merge team to review whether these duplicates can be merged.",
    "Page Not Found Error" : "When accessing Season X <insert season sequence> detail page via EDP we observe a page not found error. Please review if this can be corrected so that Season X is made available to customers.",
    "ASIN Leads To Different Page" : "When accessing Season X <insert season sequence> detail page via EDP it takes us to a detail page for season Y <insert season sequence>. Please review if this can be corrected so that Season X is made available to customers.",
    "Episode Missing Play Button" : "Episode X is missing play button on PV site. While checking on EDP and CST we have live offers and avails. Please check why this episode missing play button on site and helps us to resolve this issue.",
    "Episode Missing In PV Site" : "Episode X is missing on PV site. While checking on EDP and CST we have live offers and avails. Please check why this episode missing on site and helps us to resolve this issue.",
    "Episode Missing In EDP" : "Episode X is missing on EDP and PV site due to no metadata and avails. Please confirm whether this is intended. If not, please request partner for metadata and avail delivery.",
    "Season Missing Play Button" : "Season X is missing play button on PV site. While checking on EDP and CST we have live offers and avails. Please check why this season missing play button on site and helps us to resolve this issue.",
    "Season Missing In PV site" : "Season X is missing on PV site. While checking on EDP and CST we have live offers and avails. Please check why this season missing on site and helps us to resolve this issue.",
    "Season Missing In EDP" : "Season X is missing on EDP and PV site due to no metadata and avails. Please confirm whether this is intended. If not, please request partner for metadata and avail delivery.",
    "Episode/Season Missing Due To Repurposing" : "Episode/Season X is missing on PV site. While checking on EDP  we have live offers and avails but the publishing status is 'not ready' in CST due to <insert failing rules>. Please take actions to fulfill failing rules so that this title is published to customers."
}

# Access DB path
access_db_path = r"\\ant\dept-in\Digiflex\DF-Cataloghealth\Quality Products\Tickets\TT Details Database.accdb"

# Field headers
fields = [
    "RCA", "Territory", "Offer Type", "Partner", "Season/Episode Number", 
    "EDP Link", "EDP Link 2", "D2C Link", "D2C Link 2",
    "Detail Page Link", "Detail Page Link 2", "Avails Link", "Avails Link 2", "DEAL Link", "WMV Link", "Screenshot", "Issue"
]

class TVSeriesForm(MDApp):
    def __init__(self):
        super().__init__()
        self.entries = {}
        self.selected_issues = []
        self.selected_rca_items = []
        self.offer_type_menu = None
        self.cti_menus = []
        self.cti_buttons = []
        self.cti_values = ["", "", ""]  # Category, Type, Item
        self.excel_data = []
        self.task_data = []
        self.task_dialog = None
        self.user_dropdown = None
        self.task_entries = {}
        self.edp_link_url = ""
        self.current_gti_index = 0
        self.grouped_gti_data = []
        self.current_gti_raw_data = []
        self.lob_menu = None
        self.code_expansion_menu = None
        self.action_menu = None
        self.resolver_menu = None
        self.start_time = None
        self.end_time = None
        self.break_start_time = None  # datetime.time object for tracking breaks internally
        self.break_end_time = None    # datetime.time object for tracking breaks internally
        self.total_break_time = 0.0   # Total break time in minutes (float)
        self.break_duration_db = 0.0   # Break duration saved to database
        self.issue_name = ""  # Store the button name separately
        self.form_counter = 1  # Track number of forms
        self.additional_forms = []  # Store additional form data
        self.active_form = 'main'  # Track which form is currently active
        self.cumulative_time = 0.0  # Store the cumulative total time for display
        self.titles_processed = 0  # Counter for completed/submitted titles
        self.autosave_path = os.path.join(os.path.expanduser("~"), ".tv_series_tool_autosave.pkl")
        self.driver = None  # Store WebDriver instance
        
        # Configure font for Unicode support
        self.setup_unicode_support()
        
        # Register auto-save on exit
        atexit.register(self.auto_save_data)
        
        # Try to load previous session data
        self.try_load_autosave()
    
    def setup_unicode_support(self):
        """Setup Unicode font support for international characters"""
        try:
            # Register fonts that support Japanese characters
            unicode_fonts = [
                'C:/Windows/Fonts/msgothic.ttc',  # MS Gothic - supports Japanese
                'C:/Windows/Fonts/msmincho.ttc',  # MS Mincho - supports Japanese
                'C:/Windows/Fonts/meiryo.ttc',    # Meiryo - supports Japanese
                'C:/Windows/Fonts/arial.ttf',     # Arial Unicode
                'C:/Windows/Fonts/calibri.ttf',   # Calibri
                'C:/Windows/Fonts/segoeui.ttf'    # Segoe UI
            ]
            
            font_registered = False
            for font_path in unicode_fonts:
                if os.path.exists(font_path):
                    LabelBase.register(name='UnicodeFont', fn_regular=font_path)
                    print(f"Registered Unicode font: {font_path}")
                    font_registered = True
                    break
            
            if not font_registered:
                print("Warning: No Unicode-compatible font found")
                        
        except Exception as e:
            print(f"Font registration warning: {e}")
    
    def normalize_unicode_text(self, text):
        """Normalize Unicode text to handle international characters properly"""
        if not text:
            return ""
        try:
            # Convert to string first
            text_str = str(text)
            # Normalize Unicode text to NFC form for consistent character representation
            normalized = unicodedata.normalize('NFC', text_str)
            # Ensure proper encoding for web forms
            encoded = normalized.encode('utf-8').decode('utf-8')
            return encoded
        except Exception as e:
            print(f"Unicode normalization error: {e}")
            # Fallback to basic string conversion
            try:
                return str(text).encode('utf-8', errors='replace').decode('utf-8')
            except:
                return str(text)

    def keyboard_handler(self, window, key, *args):
        # Prevent Escape key from closing the application
        if key == 27:  # 27 is the keycode for Escape key
            return True  # Return True to indicate the key was handled
        return False  # Let other keys pass through
    def auto_fill_tickets(self):
        """Improved auto-fill tickets function with better error handling and resource management"""
        if not self.excel_data:
            return
        
        driver = None
        try:
            # Setup Chrome with improved options
            service = Service(ChromeDriverManager().install())
            chrome_options = webdriver.ChromeOptions()
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            # Add options to improve stability
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-infobars")
            
            driver = webdriver.Chrome(service=service, options=chrome_options)
            driver.set_page_load_timeout(60)  # Increased timeout for Maxis API
            driver.implicitly_wait(10)  # Increased implicit wait

            # Execute script to hide automation indicators
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # Store driver instance for cleanup in case of errors
            self.driver = driver
            
            # Wait after browser is opened
            time.sleep(3)
            
            for i, row in enumerate(self.excel_data):
                try:
                    print(f"Processing row {i+1}...")
                    
                    # Retry logic for URL loading to handle Maxis API errors
                    url_loaded = False
                    for url_attempt in range(3):
                        try:
                            print(f"Loading URL (attempt {url_attempt+1})...")
                            
                            # Clear any existing cookies/cache that might cause Maxis issues
                            if url_attempt > 0:
                                driver.delete_all_cookies()
                                driver.execute_script("window.localStorage.clear();")
                                driver.execute_script("window.sessionStorage.clear();")
                            
                            driver.get("https://t.corp.amazon.com/create/templates/54f1b84d-9ede-4987-8de1-c23b35601ec2")
                            
                            # Wait for page to load completely with error checking
                            WebDriverWait(driver, 30).until(
                                lambda d: d.execute_script("return document.readyState") == "complete"
                            )
                            
                            # Check if page loaded without Maxis error
                            page_source = driver.page_source.lower()
                            if "maxis" in page_source and "error" in page_source:
                                raise Exception("Maxis API error detected in page")
                            
                            time.sleep(5)  # Additional wait for full page load
                            url_loaded = True
                            print("Page loaded successfully")
                            break
                        except Exception as url_error:
                            print(f"URL load attempt {url_attempt+1} failed: {url_error}")
                            if url_attempt < 2:
                                print("Retrying URL load...")
                                time.sleep(5)
                            else:
                                print(f"Failed to load URL after 3 attempts for row {i+1}")
                    
                    if not url_loaded:
                        print(f"Skipping row {i+1} due to URL load failure")
                        continue
                    
                    print("Finding title field...")
                    
                    # Wait longer for title field to handle slow loading
                    title_field = WebDriverWait(driver, 30).until(
                        EC.element_to_be_clickable((By.XPATH, '//*[@id="ticket-title"]'))
                    )
                    print("Title field found")
                    
                    def get_clean_value(key):
                        import pandas as pd
                        value = row.get(key)
                        if pd.isna(value) or str(value).strip() in ['NA', 'nan', '']:
                            return ''
                        # Normalize Unicode first with enhanced handling
                        clean_value = self.normalize_unicode_text(str(value))
                        # Clean the value by removing unwanted characters and duplicates
                        clean_value = clean_value.replace('\r', '').replace('\n', ' ').replace('_x000D_', '')
                        # Remove everything after # including duplicates
                        if '#' in clean_value:
                            clean_value = clean_value.split('#')[0]
                        # Aggressive cleanup - remove all instances of the key label
                        while True:
                            if clean_value.startswith(key + ': '):
                                clean_value = clean_value[len(key)+2:].strip()
                            elif clean_value.startswith(key + ':'):
                                clean_value = clean_value[len(key)+1:].strip()
                            elif clean_value.startswith(key + ' : '):
                                clean_value = clean_value[len(key)+3:].strip()
                            else:
                                break
                        return clean_value.strip()
                    
                    issue_name = get_clean_value('Issue Name') or get_clean_value('Issue') or 'Issue'
                    series_name = get_clean_value('Series Name') or 'Series'
                    partner = get_clean_value('Partner') or 'Partner'
                    title_text = f"TV Series Integrity | {issue_name} | {series_name} | {partner}"
                    
                    # Multiple attempts to fill title field
                    print(f"Title text to fill: {title_text}")
                    
                    title_filled = False
                    for attempt in range(5):  # Increased attempts
                        try:
                            print(f"Title fill attempt {attempt+1}...")
                            
                            # Method 1: Enhanced clipboard method with UTF-8 encoding
                            if attempt == 0:
                                unicode_text = self.normalize_unicode_text(title_text)
                                # Set clipboard with explicit UTF-8 encoding
                                try:
                                    import win32clipboard
                                    win32clipboard.OpenClipboard()
                                    win32clipboard.EmptyClipboard()
                                    win32clipboard.SetClipboardData(win32clipboard.CF_UNICODETEXT, unicode_text)
                                    win32clipboard.CloseClipboard()
                                except Exception as clipboard_error:
                                    print(f"Clipboard error: {clipboard_error}")
                                    # Fallback to pyperclip
                                    pyperclip.copy(unicode_text)
                                
                                title_field.click()
                                time.sleep(0.5)
                                title_field.send_keys(Keys.CONTROL + 'a')
                                time.sleep(0.3)
                                title_field.send_keys(Keys.CONTROL + 'v')
                                time.sleep(0.5)
                            
                            # Method 2: Direct send_keys with clear
                            elif attempt == 1:
                                title_field.click()
                                time.sleep(0.3)
                                title_field.clear()
                                time.sleep(0.3)
                                unicode_text = self.normalize_unicode_text(title_text)
                                title_field.send_keys(unicode_text)
                                time.sleep(0.5)
                            
                            # Method 3: Enhanced JavaScript approach with UTF-8 handling
                            else:
                                unicode_text = self.normalize_unicode_text(title_text)
                                # Use JavaScript to properly handle Unicode characters
                                js_script = f"""
                                var field = arguments[0];
                                var text = arguments[1];
                                field.focus();
                                field.value = '';
                                field.value = text;
                                field.dispatchEvent(new Event('input', {{ bubbles: true }}));
                                field.dispatchEvent(new Event('change', {{ bubbles: true }}));
                                """
                                driver.execute_script(js_script, title_field, unicode_text)
                                time.sleep(0.5)
                            
                            # Verify title was filled
                            current_value = title_field.get_attribute('value')
                            if current_value and current_value.strip():
                                title_filled = True
                                print(f"Title filled: {current_value[:30]}...")
                                break
                                
                        except Exception as e:
                            print(f"Title attempt {attempt+1} failed: {e}")
                    
                    if not title_filled:
                        print(f"Skipping row {i+1} - title not filled")
                        continue
                    
                    print("Title filled successfully")
                    
                    # Find description field
                    print("Finding description field...")
                    description_field = WebDriverWait(driver, 15).until(
                        EC.element_to_be_clickable((By.XPATH, '//*[@id="markdown-editor"]'))
                    )
                    print("Description field found")
                    
                    # Build detailed description
                    description_parts = []
                    description_parts.append("Hi Team,")
                    
                    description_parts.append("")  # Empty line
                    
                    if get_clean_value('Issue'): description_parts.append(f"Issue Description : {get_clean_value('Issue')}")
                    if get_clean_value('RCA'): description_parts.append(f"Root cause (RCA) : {get_clean_value('RCA')}")
                    if get_clean_value('Series Name'): description_parts.append(f"Title Name : {get_clean_value('Series Name')}")
                    if get_clean_value('Series GTI'): description_parts.append(f"Series GTI : {get_clean_value('Series GTI')}")
                    if get_clean_value('Season/Episode Number'): description_parts.append(f"Season/Episode Number : {get_clean_value('Season/Episode Number')}")
                    if get_clean_value('Territory'): description_parts.append(f"Territory : {get_clean_value('Territory')}")
                    if get_clean_value('Offer Type'): description_parts.append(f"Offer Type : {get_clean_value('Offer Type')}")
                    
                    description_parts.append("")  # Empty line
                    
                    if get_clean_value('EDP Link'): description_parts.append(f"EDP Link: {get_clean_value('EDP Link')}")
                    if get_clean_value('EDP Link 2'): description_parts.append(f"EDP Link 2: {get_clean_value('EDP Link 2')}")
                    if get_clean_value('Detail Page Link'): description_parts.append(f"Detail Page Link: {get_clean_value('Detail Page Link')}")
                    if get_clean_value('Detail Page Link 2'): description_parts.append(f"Detail Page Link 2: {get_clean_value('Detail Page Link 2')}")
                    if get_clean_value('D2C Link'): description_parts.append(f"D2C Link: {get_clean_value('D2C Link')}")
                    if get_clean_value('D2C Link 2'): description_parts.append(f"D2C Link 2: {get_clean_value('D2C Link 2')}")
                    if get_clean_value('Avails Link'): description_parts.append(f"Avails Link: {get_clean_value('Avails Link')}")
                    if get_clean_value('Avails Link 2'): description_parts.append(f"Avails Link 2: {get_clean_value('Avails Link 2')}")
                    if get_clean_value('DEAL Link'): description_parts.append(f"DEAL Link: {get_clean_value('DEAL Link')}")
                    if get_clean_value('WMV Link'): description_parts.append(f"WMV Link: {get_clean_value('WMV Link')}")
                    
                    description_parts.append("")  # Empty line
                    
                    if get_clean_value('Screenshot'): description_parts.append(f"Screenshot/Attachment: {get_clean_value('Screenshot')}")
                    
                    description_text = "\n".join(description_parts)
                    
                    # Fill description using enhanced clipboard for better Unicode support
                    print("Filling description field...")
                    unicode_description = self.normalize_unicode_text(description_text)
                    
                    # Enhanced clipboard handling for Unicode with better error handling
                    try:
                        import win32clipboard
                        win32clipboard.OpenClipboard()
                        win32clipboard.EmptyClipboard()
                        win32clipboard.SetClipboardData(win32clipboard.CF_UNICODETEXT, unicode_description)
                        win32clipboard.CloseClipboard()
                        print("Used Windows clipboard API for Unicode text")
                    except Exception as clipboard_error:
                        print(f"Clipboard error: {clipboard_error}")
                        # Fallback to pyperclip
                        try:
                            pyperclip.copy(unicode_description)
                            print("Used pyperclip for text copy")
                        except Exception as pyperclip_error:
                            print(f"Pyperclip error: {pyperclip_error}")
                            # Last resort: direct send_keys
                            print("Using direct send_keys as fallback")
                    
                    try:
                        description_field.click()
                        time.sleep(0.3)
                        description_field.send_keys(Keys.CONTROL + 'a')
                        time.sleep(0.2)
                        description_field.send_keys(Keys.CONTROL + 'v')
                        time.sleep(0.5)
                        print("Description field filled successfully")
                    except Exception as desc_error:
                        print(f"Error filling description: {desc_error}")
                        # Try JavaScript as fallback
                        try:
                            js_script = f"""
                            var field = arguments[0];
                            var text = arguments[1];
                            field.focus();
                            field.value = '';
                            field.value = text;
                            field.dispatchEvent(new Event('input', {{ bubbles: true }}));
                            field.dispatchEvent(new Event('change', {{ bubbles: true }}));
                            """
                            driver.execute_script(js_script, description_field, unicode_description)
                            print("Description filled using JavaScript")
                        except Exception as js_error:
                            print(f"JavaScript fill error: {js_error}")
                            continue  # Skip this row if we can't fill the description
                    
                    # Wait before clicking create button
                    print("Waiting 5 seconds before clicking create button...")
                    time.sleep(5)
                    
                    # Click create button using specific XPath
                    button_clicked = False
                    try:
                        print("Searching for Create button...")
                        create_button = WebDriverWait(driver, 10).until(
                            EC.element_to_be_clickable((By.XPATH, '//*[@id="sim-create"]/div/div[3]/div/div/div/div/div[2]/button'))
                        )
                        driver.execute_script("arguments[0].scrollIntoView(true);", create_button)
                        time.sleep(1)
                        driver.execute_script("arguments[0].click();", create_button)
                        button_clicked = True
                        print(f"Create button clicked successfully for row {i+1}")
                    except Exception as e:
                        print(f"Failed to click create button: {e}")
                    
                    if button_clicked:
                        print(f"Waiting for ticket {i+1} creation to complete...")
                        time.sleep(10)
                    else:
                        print(f"WARNING: Failed to click create button for row {i+1}")
                        time.sleep(3)
                    
                    print(f"Row {i+1} processed successfully")
                    
                except Exception as row_error:
                    print(f"Error processing row {i+1}: {row_error}")
                    # Log detailed error for debugging
                    with open("row_error_log.txt", "a", encoding="utf-8") as f:
                        f.write(f"{'-'*50}\n")
                        f.write(f"Error processing row {i+1}: {row_error}\n")
                        f.write(traceback.format_exc())
                    continue
            
            # Clean up driver
            if driver:
                try:
                    driver.quit()
                    self.driver = None
                except Exception as quit_error:
                    print(f"Error quitting driver: {quit_error}")
            
            print("All rows processed")
            dialog = MDDialog(title="Success", text="All tickets auto-filled successfully.")
            dialog.open()
            
        except Exception as e:
            # Clean up driver in case of error
            if driver:
                try:
                    driver.quit()
                    self.driver = None
                except:
                    pass
            
            # Log detailed error for debugging
            with open("auto_fill_error_log.txt", "a", encoding="utf-8") as f:
                f.write(f"{'-'*50}\n")
                f.write(f"Auto-fill error: {e}\n")
                f.write(traceback.format_exc())
            
            dialog = MDDialog(title="Automation Error", text=f"Failed to auto-fill tickets.\n{str(e)}")
            dialog.open()
    
    def on_stop(self):
        """Clean up resources when app is closed"""
        # Close any open WebDriver instances
        if hasattr(self, 'driver') and self.driver:
            try:
                self.driver.quit()
                self.driver = None
            except:
                pass
        
        # Save data before exit
        self.auto_save_data()
        
        return super().on_stop()