import streamlit as st
from langchain_community.vectorstores import FAISS
from langchain_text_splitters import CharacterTextSplitter
from langchain_core.documents import Document
from langchain_community.retrievers.contextual_compression import ContextualCompressionRetriever
from langchain_community.retrievers.document_compressors import DocumentCompressorPipeline
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain.chains import RetrievalQA
from langchain_huggingface import HuggingFaceEndpoint
import os
import docx2txt
import tempfile
import PyPDF2

# Set page configuration
st.set_page_config(page_title="Document AI Assistant", page_icon="💬")

# Load document content based on file type
def load_document(uploaded_file):
    # Create a temporary file
    with tempfile.NamedTemporaryFile(delete=False, suffix=f".{uploaded_file.name.split('.')[-1]}") as temp_file:
        temp_file.write(uploaded_file.getvalue())
        temp_path = temp_file.name
    
    # Extract text based on file type
    if uploaded_file.name.endswith('.docx'):
        text = docx2txt.process(temp_path)
    elif uploaded_file.name.endswith('.pdf'):
        with open(temp_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
    else:  # Assume it's a text file
        with open(temp_path, 'r', encoding='utf-8', errors='ignore') as file:
            text = file.read()
    
    # Clean up the temporary file
    os.unlink(temp_path)
    return text

# Create vectorstore
def create_vectorstore(text):
    # Split text into chunks
    splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
    docs = [Document(page_content=chunk) for chunk in splitter.split_text(text)]
    
    # Use HuggingFace embeddings (local)
    embeddings = HuggingFaceEmbeddings(model_name="all-MiniLM-L6-v2")
    
    # Create vector store
    vectorstore = FAISS.from_documents(docs, embeddings)
    return vectorstore

# Setup Streamlit UI
st.title("💬 Document AI Assistant")
st.subheader("Upload a document and ask questions about it")

# File uploader
uploaded_file = st.file_uploader("Choose a document file", type=["txt", "pdf", "docx"])

# Initialize session state for document text
if "document_text" not in st.session_state:
    st.session_state.document_text = None
    
# Initialize chat history
if "messages" not in st.session_state:
    st.session_state.messages = []

# Process uploaded file
if uploaded_file is not None:
    with st.spinner("Processing document..."):
        # Extract text from document
        document_text = load_document(uploaded_file)
        st.session_state.document_text = document_text
        
        # Create vector store
        vectorstore = create_vectorstore(document_text)
        st.session_state.vectorstore = vectorstore
        
        st.success(f"Document '{uploaded_file.name}' processed successfully!")

# Display chat messages from history
for message in st.session_state.messages:
    with st.chat_message(message["role"]):
        st.markdown(message["content"])

# Accept user input if document is uploaded
if st.session_state.document_text is not None:
    if prompt := st.chat_input("What would you like to know about the document?"):
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": prompt})
        
        # Display user message
        with st.chat_message("user"):
            st.markdown(prompt)
        
        # Generate response
        with st.chat_message("assistant"):
            with st.spinner("Searching the document for answers..."):
                # Get retriever from vectorstore
                retriever = st.session_state.vectorstore.as_retriever(
                    search_kwargs={"k": 5}
                )
                
                # Generate response based on relevant document sections
                context_docs = retriever.get_relevant_documents(prompt)
                
                if context_docs:
                    # Extract and format the context
                    context = "\n\n".join([doc.page_content for doc in context_docs])
                    
                    # Generate response based on context
                    response = f"Based on the document, I found this information:\n\n{context}"
                else:
                    response = "I couldn't find relevant information in the document to answer your question."
                
                st.markdown(response)
        
        # Add assistant response to chat history
        st.session_state.messages.append({"role": "assistant", "content": response})
else:
    st.info("Please upload a document to start asking questions.")