('C:\\Users\\<USER>\\Desktop\\Python\\Tools\\build\\QMT(Linear '
 'Ops)\\PYZ-00.pyz',
 [('PIL',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_osx_support',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\_osx_support.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils._win32',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\_win32.py',
   'PYMODULE'),
  ('_pyi_rth_utils.tempfile',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\tempfile.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('attr',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\__init__.py',
   'PYMODULE'),
  ('attr._cmp',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\_cmp.py',
   'PYMODULE'),
  ('attr._compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._config',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('attr._funcs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._make',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr._next_gen',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._version_info',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr.converters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.filters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.setters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr.validators',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('attrs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attrs\\__init__.py',
   'PYMODULE'),
  ('attrs.converters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attrs\\converters.py',
   'PYMODULE'),
  ('attrs.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attrs\\exceptions.py',
   'PYMODULE'),
  ('attrs.filters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attrs\\filters.py',
   'PYMODULE'),
  ('attrs.setters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attrs\\setters.py',
   'PYMODULE'),
  ('attrs.validators',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\attrs\\validators.py',
   'PYMODULE'),
  ('base64',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\base64.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\bdb.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\bisect.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cgi',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\cgi.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\cmd.py',
   'PYMODULE'),
  ('code',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\codeop.py',
   'PYMODULE'),
  ('colorama',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\colorsys.py',
   'PYMODULE'),
  ('commctrl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\contextvars.py',
   'PYMODULE'),
  ('contourpy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\contourpy\\__init__.py',
   'PYMODULE'),
  ('contourpy._version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\contourpy\\_version.py',
   'PYMODULE'),
  ('contourpy.array',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\contourpy\\array.py',
   'PYMODULE'),
  ('contourpy.chunk',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\contourpy\\chunk.py',
   'PYMODULE'),
  ('contourpy.convert',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\contourpy\\convert.py',
   'PYMODULE'),
  ('contourpy.dechunk',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\contourpy\\dechunk.py',
   'PYMODULE'),
  ('contourpy.enum_util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\contourpy\\enum_util.py',
   'PYMODULE'),
  ('contourpy.typecheck',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\contourpy\\typecheck.py',
   'PYMODULE'),
  ('contourpy.types',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\contourpy\\types.py',
   'PYMODULE'),
  ('copy',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\copy.py',
   'PYMODULE'),
  ('csv',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('curses',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\curses\\__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\curses\\has_key.py',
   'PYMODULE'),
  ('cycler',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\cycler\\__init__.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\datetime.py',
   'PYMODULE'),
  ('dateutil',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\difflib.py',
   'PYMODULE'),
  ('dis',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\dis.py',
   'PYMODULE'),
  ('distutils',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.command',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.debug',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.dist',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.errors',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.extension',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.spawn',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.unixccompiler',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\unixccompiler.py',
   'PYMODULE'),
  ('distutils.util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.version',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('doctest',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\doctest.py',
   'PYMODULE'),
  ('docutils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\__init__.py',
   'PYMODULE'),
  ('docutils.frontend',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\frontend.py',
   'PYMODULE'),
  ('docutils.io',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\io.py',
   'PYMODULE'),
  ('docutils.languages',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\__init__.py',
   'PYMODULE'),
  ('docutils.languages.af',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\af.py',
   'PYMODULE'),
  ('docutils.languages.ar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\ar.py',
   'PYMODULE'),
  ('docutils.languages.ca',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\ca.py',
   'PYMODULE'),
  ('docutils.languages.cs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\cs.py',
   'PYMODULE'),
  ('docutils.languages.da',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\da.py',
   'PYMODULE'),
  ('docutils.languages.de',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\de.py',
   'PYMODULE'),
  ('docutils.languages.en',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\en.py',
   'PYMODULE'),
  ('docutils.languages.eo',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\eo.py',
   'PYMODULE'),
  ('docutils.languages.es',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\es.py',
   'PYMODULE'),
  ('docutils.languages.fa',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\fa.py',
   'PYMODULE'),
  ('docutils.languages.fi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\fi.py',
   'PYMODULE'),
  ('docutils.languages.fr',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\fr.py',
   'PYMODULE'),
  ('docutils.languages.gl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\gl.py',
   'PYMODULE'),
  ('docutils.languages.he',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\he.py',
   'PYMODULE'),
  ('docutils.languages.it',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\it.py',
   'PYMODULE'),
  ('docutils.languages.ja',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\ja.py',
   'PYMODULE'),
  ('docutils.languages.ka',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\ka.py',
   'PYMODULE'),
  ('docutils.languages.ko',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\ko.py',
   'PYMODULE'),
  ('docutils.languages.lt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\lt.py',
   'PYMODULE'),
  ('docutils.languages.lv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\lv.py',
   'PYMODULE'),
  ('docutils.languages.nl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\nl.py',
   'PYMODULE'),
  ('docutils.languages.pl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\pl.py',
   'PYMODULE'),
  ('docutils.languages.pt_br',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\pt_br.py',
   'PYMODULE'),
  ('docutils.languages.ru',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\ru.py',
   'PYMODULE'),
  ('docutils.languages.sk',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\sk.py',
   'PYMODULE'),
  ('docutils.languages.sv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\sv.py',
   'PYMODULE'),
  ('docutils.languages.uk',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\uk.py',
   'PYMODULE'),
  ('docutils.languages.zh_cn',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\zh_cn.py',
   'PYMODULE'),
  ('docutils.languages.zh_tw',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\languages\\zh_tw.py',
   'PYMODULE'),
  ('docutils.nodes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\nodes.py',
   'PYMODULE'),
  ('docutils.parsers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\__init__.py',
   'PYMODULE'),
  ('docutils.parsers.rst',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\__init__.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\directives\\__init__.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives.admonitions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\directives\\admonitions.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives.body',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\directives\\body.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives.html',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\directives\\html.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives.images',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\directives\\images.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives.misc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\directives\\misc.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives.parts',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\directives\\parts.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives.references',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\directives\\references.py',
   'PYMODULE'),
  ('docutils.parsers.rst.directives.tables',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\directives\\tables.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\__init__.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.af',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\af.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.ar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\ar.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.ca',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\ca.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.cs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\cs.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.da',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\da.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.de',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\de.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.en',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\en.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.eo',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\eo.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.es',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\es.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.fa',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\fa.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.fi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\fi.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.fr',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\fr.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.gl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\gl.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.he',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\he.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.it',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\it.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.ja',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\ja.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.ka',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\ka.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.ko',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\ko.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.lt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\lt.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.lv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\lv.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.nl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\nl.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.pl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\pl.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.pt_br',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\pt_br.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.ru',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\ru.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.sk',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\sk.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.sv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\sv.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.uk',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\uk.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.zh_cn',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\zh_cn.py',
   'PYMODULE'),
  ('docutils.parsers.rst.languages.zh_tw',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\languages\\zh_tw.py',
   'PYMODULE'),
  ('docutils.parsers.rst.roles',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\roles.py',
   'PYMODULE'),
  ('docutils.parsers.rst.states',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\states.py',
   'PYMODULE'),
  ('docutils.parsers.rst.tableparser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\parsers\\rst\\tableparser.py',
   'PYMODULE'),
  ('docutils.readers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\readers\\__init__.py',
   'PYMODULE'),
  ('docutils.readers.standalone',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\readers\\standalone.py',
   'PYMODULE'),
  ('docutils.statemachine',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\statemachine.py',
   'PYMODULE'),
  ('docutils.transforms',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\transforms\\__init__.py',
   'PYMODULE'),
  ('docutils.transforms.frontmatter',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\transforms\\frontmatter.py',
   'PYMODULE'),
  ('docutils.transforms.misc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\transforms\\misc.py',
   'PYMODULE'),
  ('docutils.transforms.parts',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\transforms\\parts.py',
   'PYMODULE'),
  ('docutils.transforms.references',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\transforms\\references.py',
   'PYMODULE'),
  ('docutils.transforms.universal',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\transforms\\universal.py',
   'PYMODULE'),
  ('docutils.transforms.writer_aux',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\transforms\\writer_aux.py',
   'PYMODULE'),
  ('docutils.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\__init__.py',
   'PYMODULE'),
  ('docutils.utils.code_analyzer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\code_analyzer.py',
   'PYMODULE'),
  ('docutils.utils.math',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\math\\__init__.py',
   'PYMODULE'),
  ('docutils.utils.math.latex2mathml',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\math\\latex2mathml.py',
   'PYMODULE'),
  ('docutils.utils.math.math2html',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\math\\math2html.py',
   'PYMODULE'),
  ('docutils.utils.math.mathalphabet2unichar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\math\\mathalphabet2unichar.py',
   'PYMODULE'),
  ('docutils.utils.math.mathml_elements',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\math\\mathml_elements.py',
   'PYMODULE'),
  ('docutils.utils.math.tex2mathml_extern',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\math\\tex2mathml_extern.py',
   'PYMODULE'),
  ('docutils.utils.math.tex2unichar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\math\\tex2unichar.py',
   'PYMODULE'),
  ('docutils.utils.math.unichar2tex',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\math\\unichar2tex.py',
   'PYMODULE'),
  ('docutils.utils.punctuation_chars',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\punctuation_chars.py',
   'PYMODULE'),
  ('docutils.utils.roman',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\roman.py',
   'PYMODULE'),
  ('docutils.utils.smartquotes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\smartquotes.py',
   'PYMODULE'),
  ('docutils.utils.urischemes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\utils\\urischemes.py',
   'PYMODULE'),
  ('docutils.writers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\__init__.py',
   'PYMODULE'),
  ('docutils.writers._html_base',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\_html_base.py',
   'PYMODULE'),
  ('docutils.writers.docutils_xml',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\docutils_xml.py',
   'PYMODULE'),
  ('docutils.writers.html4css1',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\html4css1\\__init__.py',
   'PYMODULE'),
  ('docutils.writers.html5_polyglot',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\html5_polyglot\\__init__.py',
   'PYMODULE'),
  ('docutils.writers.latex2e',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\latex2e\\__init__.py',
   'PYMODULE'),
  ('docutils.writers.manpage',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\manpage.py',
   'PYMODULE'),
  ('docutils.writers.null',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\null.py',
   'PYMODULE'),
  ('docutils.writers.odf_odt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\odf_odt\\__init__.py',
   'PYMODULE'),
  ('docutils.writers.odf_odt.prepstyles',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\odf_odt\\prepstyles.py',
   'PYMODULE'),
  ('docutils.writers.odf_odt.pygmentsformatter',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\odf_odt\\pygmentsformatter.py',
   'PYMODULE'),
  ('docutils.writers.pep_html',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\pep_html\\__init__.py',
   'PYMODULE'),
  ('docutils.writers.pseudoxml',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\pseudoxml.py',
   'PYMODULE'),
  ('docutils.writers.s5_html',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\s5_html\\__init__.py',
   'PYMODULE'),
  ('docutils.writers.xetex',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\docutils\\writers\\xetex\\__init__.py',
   'PYMODULE'),
  ('email',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('et_xmlfile',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fileinput',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\fileinput.py',
   'PYMODULE'),
  ('filetype',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\__init__.py',
   'PYMODULE'),
  ('filetype.filetype',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\filetype.py',
   'PYMODULE'),
  ('filetype.helpers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\helpers.py',
   'PYMODULE'),
  ('filetype.match',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\match.py',
   'PYMODULE'),
  ('filetype.types',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\types\\__init__.py',
   'PYMODULE'),
  ('filetype.types.application',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\types\\application.py',
   'PYMODULE'),
  ('filetype.types.archive',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\types\\archive.py',
   'PYMODULE'),
  ('filetype.types.audio',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\types\\audio.py',
   'PYMODULE'),
  ('filetype.types.base',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\types\\base.py',
   'PYMODULE'),
  ('filetype.types.document',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\types\\document.py',
   'PYMODULE'),
  ('filetype.types.font',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\types\\font.py',
   'PYMODULE'),
  ('filetype.types.image',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\types\\image.py',
   'PYMODULE'),
  ('filetype.types.isobmff',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\types\\isobmff.py',
   'PYMODULE'),
  ('filetype.types.video',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\types\\video.py',
   'PYMODULE'),
  ('filetype.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\filetype\\utils.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\glob.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('imp',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\imp.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('jinja2',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('kivy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\__init__.py',
   'PYMODULE'),
  ('kivy._version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\_version.py',
   'PYMODULE'),
  ('kivy.animation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\animation.py',
   'PYMODULE'),
  ('kivy.app',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\app.py',
   'PYMODULE'),
  ('kivy.atlas',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\atlas.py',
   'PYMODULE'),
  ('kivy.base',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\base.py',
   'PYMODULE'),
  ('kivy.cache',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\cache.py',
   'PYMODULE'),
  ('kivy.clock',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\clock.py',
   'PYMODULE'),
  ('kivy.compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\compat.py',
   'PYMODULE'),
  ('kivy.config',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\config.py',
   'PYMODULE'),
  ('kivy.context',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\context.py',
   'PYMODULE'),
  ('kivy.core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\__init__.py',
   'PYMODULE'),
  ('kivy.core.audio',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\audio\\__init__.py',
   'PYMODULE'),
  ('kivy.core.audio.audio_android',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\audio\\audio_android.py',
   'PYMODULE'),
  ('kivy.core.audio.audio_avplayer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\audio\\audio_avplayer.py',
   'PYMODULE'),
  ('kivy.core.audio.audio_ffpyplayer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\audio\\audio_ffpyplayer.py',
   'PYMODULE'),
  ('kivy.core.audio.audio_gstplayer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\audio\\audio_gstplayer.py',
   'PYMODULE'),
  ('kivy.core.audio.audio_pygame',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\audio\\audio_pygame.py',
   'PYMODULE'),
  ('kivy.core.camera',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\camera\\__init__.py',
   'PYMODULE'),
  ('kivy.core.camera.camera_android',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\camera\\camera_android.py',
   'PYMODULE'),
  ('kivy.core.camera.camera_gi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\camera\\camera_gi.py',
   'PYMODULE'),
  ('kivy.core.camera.camera_opencv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\camera\\camera_opencv.py',
   'PYMODULE'),
  ('kivy.core.camera.camera_picamera',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\camera\\camera_picamera.py',
   'PYMODULE'),
  ('kivy.core.clipboard',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\clipboard\\__init__.py',
   'PYMODULE'),
  ('kivy.core.clipboard._clipboard_ext',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\clipboard\\_clipboard_ext.py',
   'PYMODULE'),
  ('kivy.core.clipboard.clipboard_android',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\clipboard\\clipboard_android.py',
   'PYMODULE'),
  ('kivy.core.clipboard.clipboard_dbusklipper',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\clipboard\\clipboard_dbusklipper.py',
   'PYMODULE'),
  ('kivy.core.clipboard.clipboard_dummy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\clipboard\\clipboard_dummy.py',
   'PYMODULE'),
  ('kivy.core.clipboard.clipboard_gtk3',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\clipboard\\clipboard_gtk3.py',
   'PYMODULE'),
  ('kivy.core.clipboard.clipboard_nspaste',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\clipboard\\clipboard_nspaste.py',
   'PYMODULE'),
  ('kivy.core.clipboard.clipboard_pygame',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\clipboard\\clipboard_pygame.py',
   'PYMODULE'),
  ('kivy.core.clipboard.clipboard_sdl2',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\clipboard\\clipboard_sdl2.py',
   'PYMODULE'),
  ('kivy.core.clipboard.clipboard_winctypes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\clipboard\\clipboard_winctypes.py',
   'PYMODULE'),
  ('kivy.core.clipboard.clipboard_xclip',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\clipboard\\clipboard_xclip.py',
   'PYMODULE'),
  ('kivy.core.clipboard.clipboard_xsel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\clipboard\\clipboard_xsel.py',
   'PYMODULE'),
  ('kivy.core.gl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\gl\\__init__.py',
   'PYMODULE'),
  ('kivy.core.image',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\image\\__init__.py',
   'PYMODULE'),
  ('kivy.core.image.img_dds',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\image\\img_dds.py',
   'PYMODULE'),
  ('kivy.core.image.img_ffpyplayer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\image\\img_ffpyplayer.py',
   'PYMODULE'),
  ('kivy.core.image.img_pil',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\image\\img_pil.py',
   'PYMODULE'),
  ('kivy.core.image.img_pygame',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\image\\img_pygame.py',
   'PYMODULE'),
  ('kivy.core.image.img_sdl2',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\image\\img_sdl2.py',
   'PYMODULE'),
  ('kivy.core.image.img_tex',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\image\\img_tex.py',
   'PYMODULE'),
  ('kivy.core.spelling',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\spelling\\__init__.py',
   'PYMODULE'),
  ('kivy.core.spelling.spelling_enchant',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\spelling\\spelling_enchant.py',
   'PYMODULE'),
  ('kivy.core.spelling.spelling_osxappkit',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\spelling\\spelling_osxappkit.py',
   'PYMODULE'),
  ('kivy.core.text',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\text\\__init__.py',
   'PYMODULE'),
  ('kivy.core.text.markup',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\text\\markup.py',
   'PYMODULE'),
  ('kivy.core.text.text_pango',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\text\\text_pango.py',
   'PYMODULE'),
  ('kivy.core.text.text_pil',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\text\\text_pil.py',
   'PYMODULE'),
  ('kivy.core.text.text_pygame',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\text\\text_pygame.py',
   'PYMODULE'),
  ('kivy.core.text.text_sdl2',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\text\\text_sdl2.py',
   'PYMODULE'),
  ('kivy.core.video',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\video\\__init__.py',
   'PYMODULE'),
  ('kivy.core.video.video_ffmpeg',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\video\\video_ffmpeg.py',
   'PYMODULE'),
  ('kivy.core.video.video_ffpyplayer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\video\\video_ffpyplayer.py',
   'PYMODULE'),
  ('kivy.core.video.video_gstplayer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\video\\video_gstplayer.py',
   'PYMODULE'),
  ('kivy.core.video.video_null',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\video\\video_null.py',
   'PYMODULE'),
  ('kivy.core.window',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\window\\__init__.py',
   'PYMODULE'),
  ('kivy.core.window.window_egl_rpi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\window\\window_egl_rpi.py',
   'PYMODULE'),
  ('kivy.core.window.window_pygame',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\window\\window_pygame.py',
   'PYMODULE'),
  ('kivy.core.window.window_sdl2',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\core\\window\\window_sdl2.py',
   'PYMODULE'),
  ('kivy.deps',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\deps\\__init__.py',
   'PYMODULE'),
  ('kivy.effects',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\effects\\__init__.py',
   'PYMODULE'),
  ('kivy.effects.dampedscroll',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\effects\\dampedscroll.py',
   'PYMODULE'),
  ('kivy.effects.kinetic',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\effects\\kinetic.py',
   'PYMODULE'),
  ('kivy.effects.opacityscroll',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\effects\\opacityscroll.py',
   'PYMODULE'),
  ('kivy.effects.scroll',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\effects\\scroll.py',
   'PYMODULE'),
  ('kivy.event',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\event.py',
   'PYMODULE'),
  ('kivy.eventmanager',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\eventmanager\\__init__.py',
   'PYMODULE'),
  ('kivy.extras',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\extras\\__init__.py',
   'PYMODULE'),
  ('kivy.extras.highlight',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\extras\\highlight.py',
   'PYMODULE'),
  ('kivy.factory',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\factory.py',
   'PYMODULE'),
  ('kivy.factory_registers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\factory_registers.py',
   'PYMODULE'),
  ('kivy.gesture',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\gesture.py',
   'PYMODULE'),
  ('kivy.graphics',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\__init__.py',
   'PYMODULE'),
  ('kivy.graphics.cgl_backend',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\graphics\\cgl_backend\\__init__.py',
   'PYMODULE'),
  ('kivy.input',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\__init__.py',
   'PYMODULE'),
  ('kivy.input.factory',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\factory.py',
   'PYMODULE'),
  ('kivy.input.motionevent',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\motionevent.py',
   'PYMODULE'),
  ('kivy.input.postproc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\postproc\\__init__.py',
   'PYMODULE'),
  ('kivy.input.postproc.calibration',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\postproc\\calibration.py',
   'PYMODULE'),
  ('kivy.input.postproc.dejitter',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\postproc\\dejitter.py',
   'PYMODULE'),
  ('kivy.input.postproc.doubletap',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\postproc\\doubletap.py',
   'PYMODULE'),
  ('kivy.input.postproc.ignorelist',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\postproc\\ignorelist.py',
   'PYMODULE'),
  ('kivy.input.postproc.retaintouch',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\postproc\\retaintouch.py',
   'PYMODULE'),
  ('kivy.input.postproc.tripletap',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\postproc\\tripletap.py',
   'PYMODULE'),
  ('kivy.input.provider',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\provider.py',
   'PYMODULE'),
  ('kivy.input.providers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\__init__.py',
   'PYMODULE'),
  ('kivy.input.providers.androidjoystick',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\androidjoystick.py',
   'PYMODULE'),
  ('kivy.input.providers.hidinput',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\hidinput.py',
   'PYMODULE'),
  ('kivy.input.providers.leapfinger',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\leapfinger.py',
   'PYMODULE'),
  ('kivy.input.providers.linuxwacom',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\linuxwacom.py',
   'PYMODULE'),
  ('kivy.input.providers.mactouch',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\mactouch.py',
   'PYMODULE'),
  ('kivy.input.providers.mouse',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\mouse.py',
   'PYMODULE'),
  ('kivy.input.providers.mtdev',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\mtdev.py',
   'PYMODULE'),
  ('kivy.input.providers.probesysfs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\probesysfs.py',
   'PYMODULE'),
  ('kivy.input.providers.tuio',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\tuio.py',
   'PYMODULE'),
  ('kivy.input.providers.wm_common',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\wm_common.py',
   'PYMODULE'),
  ('kivy.input.providers.wm_pen',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\wm_pen.py',
   'PYMODULE'),
  ('kivy.input.providers.wm_touch',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\providers\\wm_touch.py',
   'PYMODULE'),
  ('kivy.input.shape',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\input\\shape.py',
   'PYMODULE'),
  ('kivy.lang',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\lang\\__init__.py',
   'PYMODULE'),
  ('kivy.lang.builder',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\lang\\builder.py',
   'PYMODULE'),
  ('kivy.lang.parser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\lang\\parser.py',
   'PYMODULE'),
  ('kivy.lib',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\lib\\__init__.py',
   'PYMODULE'),
  ('kivy.lib.ddsfile',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\lib\\ddsfile.py',
   'PYMODULE'),
  ('kivy.lib.gstplayer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\lib\\gstplayer\\__init__.py',
   'PYMODULE'),
  ('kivy.lib.mtdev',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\lib\\mtdev.py',
   'PYMODULE'),
  ('kivy.lib.vidcore_lite',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\lib\\vidcore_lite\\__init__.py',
   'PYMODULE'),
  ('kivy.loader',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\loader.py',
   'PYMODULE'),
  ('kivy.logger',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\logger.py',
   'PYMODULE'),
  ('kivy.metrics',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\metrics.py',
   'PYMODULE'),
  ('kivy.modules',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\modules\\__init__.py',
   'PYMODULE'),
  ('kivy.multistroke',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\multistroke.py',
   'PYMODULE'),
  ('kivy.parser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\parser.py',
   'PYMODULE'),
  ('kivy.resources',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\resources.py',
   'PYMODULE'),
  ('kivy.setupconfig',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\setupconfig.py',
   'PYMODULE'),
  ('kivy.support',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\support.py',
   'PYMODULE'),
  ('kivy.uix',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\__init__.py',
   'PYMODULE'),
  ('kivy.uix.accordion',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\accordion.py',
   'PYMODULE'),
  ('kivy.uix.actionbar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\actionbar.py',
   'PYMODULE'),
  ('kivy.uix.anchorlayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\anchorlayout.py',
   'PYMODULE'),
  ('kivy.uix.behaviors',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\behaviors\\__init__.py',
   'PYMODULE'),
  ('kivy.uix.behaviors.button',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\behaviors\\button.py',
   'PYMODULE'),
  ('kivy.uix.behaviors.codenavigation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\behaviors\\codenavigation.py',
   'PYMODULE'),
  ('kivy.uix.behaviors.compoundselection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\behaviors\\compoundselection.py',
   'PYMODULE'),
  ('kivy.uix.behaviors.cover',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\behaviors\\cover.py',
   'PYMODULE'),
  ('kivy.uix.behaviors.drag',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\behaviors\\drag.py',
   'PYMODULE'),
  ('kivy.uix.behaviors.emacs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\behaviors\\emacs.py',
   'PYMODULE'),
  ('kivy.uix.behaviors.focus',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\behaviors\\focus.py',
   'PYMODULE'),
  ('kivy.uix.behaviors.knspace',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\behaviors\\knspace.py',
   'PYMODULE'),
  ('kivy.uix.behaviors.togglebutton',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\behaviors\\togglebutton.py',
   'PYMODULE'),
  ('kivy.uix.behaviors.touchripple',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\behaviors\\touchripple.py',
   'PYMODULE'),
  ('kivy.uix.boxlayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\boxlayout.py',
   'PYMODULE'),
  ('kivy.uix.bubble',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\bubble.py',
   'PYMODULE'),
  ('kivy.uix.button',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\button.py',
   'PYMODULE'),
  ('kivy.uix.camera',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\camera.py',
   'PYMODULE'),
  ('kivy.uix.carousel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\carousel.py',
   'PYMODULE'),
  ('kivy.uix.checkbox',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\checkbox.py',
   'PYMODULE'),
  ('kivy.uix.codeinput',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\codeinput.py',
   'PYMODULE'),
  ('kivy.uix.colorpicker',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\colorpicker.py',
   'PYMODULE'),
  ('kivy.uix.dropdown',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\dropdown.py',
   'PYMODULE'),
  ('kivy.uix.effectwidget',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\effectwidget.py',
   'PYMODULE'),
  ('kivy.uix.filechooser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\filechooser.py',
   'PYMODULE'),
  ('kivy.uix.floatlayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\floatlayout.py',
   'PYMODULE'),
  ('kivy.uix.gesturesurface',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\gesturesurface.py',
   'PYMODULE'),
  ('kivy.uix.gridlayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\gridlayout.py',
   'PYMODULE'),
  ('kivy.uix.image',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\image.py',
   'PYMODULE'),
  ('kivy.uix.label',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\label.py',
   'PYMODULE'),
  ('kivy.uix.layout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\layout.py',
   'PYMODULE'),
  ('kivy.uix.modalview',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\modalview.py',
   'PYMODULE'),
  ('kivy.uix.pagelayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\pagelayout.py',
   'PYMODULE'),
  ('kivy.uix.popup',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\popup.py',
   'PYMODULE'),
  ('kivy.uix.progressbar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\progressbar.py',
   'PYMODULE'),
  ('kivy.uix.recycleboxlayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\recycleboxlayout.py',
   'PYMODULE'),
  ('kivy.uix.recyclegridlayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\recyclegridlayout.py',
   'PYMODULE'),
  ('kivy.uix.recyclelayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\recyclelayout.py',
   'PYMODULE'),
  ('kivy.uix.recycleview',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\recycleview\\__init__.py',
   'PYMODULE'),
  ('kivy.uix.recycleview.__init__',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\recycleview\\__init__.py',
   'PYMODULE'),
  ('kivy.uix.recycleview.datamodel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\recycleview\\datamodel.py',
   'PYMODULE'),
  ('kivy.uix.recycleview.layout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\recycleview\\layout.py',
   'PYMODULE'),
  ('kivy.uix.recycleview.views',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\recycleview\\views.py',
   'PYMODULE'),
  ('kivy.uix.relativelayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\relativelayout.py',
   'PYMODULE'),
  ('kivy.uix.rst',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\rst.py',
   'PYMODULE'),
  ('kivy.uix.sandbox',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\sandbox.py',
   'PYMODULE'),
  ('kivy.uix.scatter',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\scatter.py',
   'PYMODULE'),
  ('kivy.uix.scatterlayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\scatterlayout.py',
   'PYMODULE'),
  ('kivy.uix.screenmanager',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\screenmanager.py',
   'PYMODULE'),
  ('kivy.uix.scrollview',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\scrollview.py',
   'PYMODULE'),
  ('kivy.uix.settings',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\settings.py',
   'PYMODULE'),
  ('kivy.uix.slider',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\slider.py',
   'PYMODULE'),
  ('kivy.uix.spinner',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\spinner.py',
   'PYMODULE'),
  ('kivy.uix.splitter',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\splitter.py',
   'PYMODULE'),
  ('kivy.uix.stacklayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\stacklayout.py',
   'PYMODULE'),
  ('kivy.uix.stencilview',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\stencilview.py',
   'PYMODULE'),
  ('kivy.uix.switch',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\switch.py',
   'PYMODULE'),
  ('kivy.uix.tabbedpanel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\tabbedpanel.py',
   'PYMODULE'),
  ('kivy.uix.textinput',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\textinput.py',
   'PYMODULE'),
  ('kivy.uix.togglebutton',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\togglebutton.py',
   'PYMODULE'),
  ('kivy.uix.treeview',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\treeview.py',
   'PYMODULE'),
  ('kivy.uix.video',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\video.py',
   'PYMODULE'),
  ('kivy.uix.videoplayer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\videoplayer.py',
   'PYMODULE'),
  ('kivy.uix.vkeyboard',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\vkeyboard.py',
   'PYMODULE'),
  ('kivy.uix.widget',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\uix\\widget.py',
   'PYMODULE'),
  ('kivy.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\utils.py',
   'PYMODULE'),
  ('kivy.vector',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\vector.py',
   'PYMODULE'),
  ('kivy.weakmethod',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy\\weakmethod.py',
   'PYMODULE'),
  ('kivy_deps',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivy_deps\\__init__.py',
   'PYMODULE'),
  ('kivymd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\__init__.py',
   'PYMODULE'),
  ('kivymd._version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\_version.py',
   'PYMODULE'),
  ('kivymd.app',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\app.py',
   'PYMODULE'),
  ('kivymd.color_definitions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\color_definitions.py',
   'PYMODULE'),
  ('kivymd.factory_registers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\factory_registers.py',
   'PYMODULE'),
  ('kivymd.font_definitions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\font_definitions.py',
   'PYMODULE'),
  ('kivymd.icon_definitions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\icon_definitions.py',
   'PYMODULE'),
  ('kivymd.material_resources',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\material_resources.py',
   'PYMODULE'),
  ('kivymd.theming',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\theming.py',
   'PYMODULE'),
  ('kivymd.theming_dynamic_text',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\theming_dynamic_text.py',
   'PYMODULE'),
  ('kivymd.tools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\tools\\__init__.py',
   'PYMODULE'),
  ('kivymd.tools.packaging',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\tools\\packaging\\__init__.py',
   'PYMODULE'),
  ('kivymd.tools.packaging.pyinstaller',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\tools\\packaging\\pyinstaller\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors.backgroundcolor_behavior',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\backgroundcolor_behavior.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors.declarative_behavior',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\declarative_behavior.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors.elevation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\elevation.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors.focus_behavior',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\focus_behavior.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors.hover_behavior',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\hover_behavior.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors.magic_behavior',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\magic_behavior.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors.motion_behavior',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\motion_behavior.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors.ripple_behavior',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\ripple_behavior.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors.rotate_behavior',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\rotate_behavior.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors.scale_behavior',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\scale_behavior.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors.stencil_behavior',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\stencil_behavior.py',
   'PYMODULE'),
  ('kivymd.uix.behaviors.touch_behavior',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\behaviors\\touch_behavior.py',
   'PYMODULE'),
  ('kivymd.uix.boxlayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\boxlayout.py',
   'PYMODULE'),
  ('kivymd.uix.button',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\button\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.button.button',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\button\\button.py',
   'PYMODULE'),
  ('kivymd.uix.card',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\card\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.card.card',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\card\\card.py',
   'PYMODULE'),
  ('kivymd.uix.dialog',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\dialog\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.dialog.dialog',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\dialog\\dialog.py',
   'PYMODULE'),
  ('kivymd.uix.fitimage',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\fitimage\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.fitimage.fitimage',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\fitimage\\fitimage.py',
   'PYMODULE'),
  ('kivymd.uix.floatlayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\floatlayout.py',
   'PYMODULE'),
  ('kivymd.uix.gridlayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\gridlayout.py',
   'PYMODULE'),
  ('kivymd.uix.label',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\label\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.label.label',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\label\\label.py',
   'PYMODULE'),
  ('kivymd.uix.list',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\list\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.list.list',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\list\\list.py',
   'PYMODULE'),
  ('kivymd.uix.relativelayout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\relativelayout.py',
   'PYMODULE'),
  ('kivymd.uix.selectioncontrol',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\selectioncontrol\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.selectioncontrol.selectioncontrol',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\selectioncontrol\\selectioncontrol.py',
   'PYMODULE'),
  ('kivymd.uix.textfield',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\textfield\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.textfield.textfield',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\textfield\\textfield.py',
   'PYMODULE'),
  ('kivymd.uix.tooltip',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\tooltip\\__init__.py',
   'PYMODULE'),
  ('kivymd.uix.tooltip.tooltip',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\uix\\tooltip\\tooltip.py',
   'PYMODULE'),
  ('kivymd.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\utils\\__init__.py',
   'PYMODULE'),
  ('kivymd.utils.asynckivy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\utils\\asynckivy.py',
   'PYMODULE'),
  ('kivymd.utils.fpsmonitor',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kivymd\\utils\\fpsmonitor.py',
   'PYMODULE'),
  ('kiwisolver',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kiwisolver\\__init__.py',
   'PYMODULE'),
  ('kiwisolver.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\kiwisolver\\exceptions.py',
   'PYMODULE'),
  ('logging',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('logging.handlers',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\lzma.py',
   'PYMODULE'),
  ('markupsafe',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('matplotlib',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\__init__.py',
   'PYMODULE'),
  ('matplotlib._afm',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\_afm.py',
   'PYMODULE'),
  ('matplotlib._api',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\_api\\__init__.py',
   'PYMODULE'),
  ('matplotlib._api.deprecation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\_api\\deprecation.py',
   'PYMODULE'),
  ('matplotlib._blocking_input',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\_blocking_input.py',
   'PYMODULE'),
  ('matplotlib._cm',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\_cm.py',
   'PYMODULE'),
  ('matplotlib._cm_bivar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\_cm_bivar.py',
   'PYMODULE'),
  ('matplotlib._cm_listed',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\_cm_listed.py',
   'PYMODULE'),
  ('matplotlib._cm_multivar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\_cm_multivar.py',
   'PYMODULE'),
  ('matplotlib._color_data',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\_color_data.py',
   'PYMODULE'),
  ('matplotlib._constrained_layout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\_constrained_layout.py',
   'PYMODULE'),
  ('matplotlib._docstring',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\_docstring.py',
   'PYMODULE'),
  ('matplotlib._enums',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\_enums.py',
   'PYMODULE'),
  ('matplotlib._fontconfig_pattern',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\_fontconfig_pattern.py',
   'PYMODULE'),
  ('matplotlib._layoutgrid',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\_layoutgrid.py',
   'PYMODULE'),
  ('matplotlib._mathtext',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\_mathtext.py',
   'PYMODULE'),
  ('matplotlib._mathtext_data',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\_mathtext_data.py',
   'PYMODULE'),
  ('matplotlib._pylab_helpers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\_pylab_helpers.py',
   'PYMODULE'),
  ('matplotlib._text_helpers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\_text_helpers.py',
   'PYMODULE'),
  ('matplotlib._tight_bbox',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\_tight_bbox.py',
   'PYMODULE'),
  ('matplotlib._tight_layout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\_tight_layout.py',
   'PYMODULE'),
  ('matplotlib._version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\_version.py',
   'PYMODULE'),
  ('matplotlib.artist',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\artist.py',
   'PYMODULE'),
  ('matplotlib.axes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\axes\\__init__.py',
   'PYMODULE'),
  ('matplotlib.axes._axes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\axes\\_axes.py',
   'PYMODULE'),
  ('matplotlib.axes._base',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\axes\\_base.py',
   'PYMODULE'),
  ('matplotlib.axes._secondary_axes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\axes\\_secondary_axes.py',
   'PYMODULE'),
  ('matplotlib.axis',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\axis.py',
   'PYMODULE'),
  ('matplotlib.backend_bases',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\backend_bases.py',
   'PYMODULE'),
  ('matplotlib.backend_managers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\backend_managers.py',
   'PYMODULE'),
  ('matplotlib.backend_tools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\backend_tools.py',
   'PYMODULE'),
  ('matplotlib.backends',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\backends\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends._backend_tk',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\backends\\_backend_tk.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_agg',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\backends\\backend_agg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_tkagg',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\backends\\backend_tkagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\backends\\backend_webagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg_core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\backends\\backend_webagg_core.py',
   'PYMODULE'),
  ('matplotlib.backends.registry',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\backends\\registry.py',
   'PYMODULE'),
  ('matplotlib.bezier',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\bezier.py',
   'PYMODULE'),
  ('matplotlib.category',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\category.py',
   'PYMODULE'),
  ('matplotlib.cbook',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\cbook.py',
   'PYMODULE'),
  ('matplotlib.cm',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\cm.py',
   'PYMODULE'),
  ('matplotlib.collections',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\collections.py',
   'PYMODULE'),
  ('matplotlib.colorbar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\colorbar.py',
   'PYMODULE'),
  ('matplotlib.colorizer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\colorizer.py',
   'PYMODULE'),
  ('matplotlib.colors',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\colors.py',
   'PYMODULE'),
  ('matplotlib.container',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\container.py',
   'PYMODULE'),
  ('matplotlib.contour',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\contour.py',
   'PYMODULE'),
  ('matplotlib.dates',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\dates.py',
   'PYMODULE'),
  ('matplotlib.dviread',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\dviread.py',
   'PYMODULE'),
  ('matplotlib.figure',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\figure.py',
   'PYMODULE'),
  ('matplotlib.font_manager',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\font_manager.py',
   'PYMODULE'),
  ('matplotlib.gridspec',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\gridspec.py',
   'PYMODULE'),
  ('matplotlib.hatch',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\hatch.py',
   'PYMODULE'),
  ('matplotlib.image',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\image.py',
   'PYMODULE'),
  ('matplotlib.inset',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\inset.py',
   'PYMODULE'),
  ('matplotlib.layout_engine',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\layout_engine.py',
   'PYMODULE'),
  ('matplotlib.legend',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\legend.py',
   'PYMODULE'),
  ('matplotlib.legend_handler',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\legend_handler.py',
   'PYMODULE'),
  ('matplotlib.lines',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\lines.py',
   'PYMODULE'),
  ('matplotlib.markers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\markers.py',
   'PYMODULE'),
  ('matplotlib.mathtext',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\mathtext.py',
   'PYMODULE'),
  ('matplotlib.mlab',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\mlab.py',
   'PYMODULE'),
  ('matplotlib.offsetbox',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\offsetbox.py',
   'PYMODULE'),
  ('matplotlib.patches',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\patches.py',
   'PYMODULE'),
  ('matplotlib.path',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\path.py',
   'PYMODULE'),
  ('matplotlib.patheffects',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\patheffects.py',
   'PYMODULE'),
  ('matplotlib.projections',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\projections\\__init__.py',
   'PYMODULE'),
  ('matplotlib.projections.geo',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\projections\\geo.py',
   'PYMODULE'),
  ('matplotlib.projections.polar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\projections\\polar.py',
   'PYMODULE'),
  ('matplotlib.pylab',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\pylab.py',
   'PYMODULE'),
  ('matplotlib.pyplot',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\pyplot.py',
   'PYMODULE'),
  ('matplotlib.quiver',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\quiver.py',
   'PYMODULE'),
  ('matplotlib.rcsetup',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\rcsetup.py',
   'PYMODULE'),
  ('matplotlib.scale',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\scale.py',
   'PYMODULE'),
  ('matplotlib.spines',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\spines.py',
   'PYMODULE'),
  ('matplotlib.stackplot',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\stackplot.py',
   'PYMODULE'),
  ('matplotlib.streamplot',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\streamplot.py',
   'PYMODULE'),
  ('matplotlib.style',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\style\\__init__.py',
   'PYMODULE'),
  ('matplotlib.style.core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\style\\core.py',
   'PYMODULE'),
  ('matplotlib.table',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\table.py',
   'PYMODULE'),
  ('matplotlib.texmanager',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\texmanager.py',
   'PYMODULE'),
  ('matplotlib.text',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\text.py',
   'PYMODULE'),
  ('matplotlib.textpath',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\textpath.py',
   'PYMODULE'),
  ('matplotlib.ticker',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\ticker.py',
   'PYMODULE'),
  ('matplotlib.transforms',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\transforms.py',
   'PYMODULE'),
  ('matplotlib.tri',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\tri\\__init__.py',
   'PYMODULE'),
  ('matplotlib.tri._triangulation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\tri\\_triangulation.py',
   'PYMODULE'),
  ('matplotlib.tri._tricontour',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\tri\\_tricontour.py',
   'PYMODULE'),
  ('matplotlib.tri._trifinder',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\tri\\_trifinder.py',
   'PYMODULE'),
  ('matplotlib.tri._triinterpolate',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\tri\\_triinterpolate.py',
   'PYMODULE'),
  ('matplotlib.tri._tripcolor',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\tri\\_tripcolor.py',
   'PYMODULE'),
  ('matplotlib.tri._triplot',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\tri\\_triplot.py',
   'PYMODULE'),
  ('matplotlib.tri._trirefine',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\tri\\_trirefine.py',
   'PYMODULE'),
  ('matplotlib.tri._tritools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\tri\\_tritools.py',
   'PYMODULE'),
  ('matplotlib.typing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\typing.py',
   'PYMODULE'),
  ('matplotlib.units',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\units.py',
   'PYMODULE'),
  ('matplotlib.widgets',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\matplotlib\\widgets.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('mpl_toolkits', '-', 'PYMODULE'),
  ('mpl_toolkits.mplot3d',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.art3d',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\art3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axes3d',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\axes3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axis3d',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\axis3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.proj3d',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\proj3d.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\opcode.py',
   'PYMODULE'),
  ('openpyxl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('optparse',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\optparse.py',
   'PYMODULE'),
  ('outcome',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\outcome\\__init__.py',
   'PYMODULE'),
  ('outcome._impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\outcome\\_impl.py',
   'PYMODULE'),
  ('outcome._util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\outcome\\_util.py',
   'PYMODULE'),
  ('outcome._version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\outcome\\_version.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.boxplot',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\boxplot.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.converter',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\converter.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.groupby',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\groupby.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.hist',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\hist.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.misc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\misc.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.style',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\style.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.timeseries',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\timeseries.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.tools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\tools.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\pathlib.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\pdb.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\pickle.py',
   'PYMODULE'),
  ('pickletools',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\pickletools.py',
   'PYMODULE'),
  ('pkg_resources',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.actions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.common',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.diagram',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.helpers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.results',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.testing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.unicode',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\platform.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\plistlib.py',
   'PYMODULE'),
  ('plyer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\__init__.py',
   'PYMODULE'),
  ('plyer.facades',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\__init__.py',
   'PYMODULE'),
  ('plyer.facades.accelerometer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\accelerometer.py',
   'PYMODULE'),
  ('plyer.facades.audio',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\audio.py',
   'PYMODULE'),
  ('plyer.facades.barometer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\barometer.py',
   'PYMODULE'),
  ('plyer.facades.battery',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\battery.py',
   'PYMODULE'),
  ('plyer.facades.bluetooth',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\bluetooth.py',
   'PYMODULE'),
  ('plyer.facades.brightness',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\brightness.py',
   'PYMODULE'),
  ('plyer.facades.call',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\call.py',
   'PYMODULE'),
  ('plyer.facades.camera',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\camera.py',
   'PYMODULE'),
  ('plyer.facades.compass',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\compass.py',
   'PYMODULE'),
  ('plyer.facades.cpu',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\cpu.py',
   'PYMODULE'),
  ('plyer.facades.devicename',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\devicename.py',
   'PYMODULE'),
  ('plyer.facades.email',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\email.py',
   'PYMODULE'),
  ('plyer.facades.filechooser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\filechooser.py',
   'PYMODULE'),
  ('plyer.facades.flash',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\flash.py',
   'PYMODULE'),
  ('plyer.facades.gps',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\gps.py',
   'PYMODULE'),
  ('plyer.facades.gravity',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\gravity.py',
   'PYMODULE'),
  ('plyer.facades.gyroscope',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\gyroscope.py',
   'PYMODULE'),
  ('plyer.facades.humidity',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\humidity.py',
   'PYMODULE'),
  ('plyer.facades.irblaster',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\irblaster.py',
   'PYMODULE'),
  ('plyer.facades.keystore',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\keystore.py',
   'PYMODULE'),
  ('plyer.facades.light',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\light.py',
   'PYMODULE'),
  ('plyer.facades.notification',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\notification.py',
   'PYMODULE'),
  ('plyer.facades.orientation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\orientation.py',
   'PYMODULE'),
  ('plyer.facades.processors',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\processors.py',
   'PYMODULE'),
  ('plyer.facades.proximity',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\proximity.py',
   'PYMODULE'),
  ('plyer.facades.screenshot',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\screenshot.py',
   'PYMODULE'),
  ('plyer.facades.sms',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\sms.py',
   'PYMODULE'),
  ('plyer.facades.spatialorientation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\spatialorientation.py',
   'PYMODULE'),
  ('plyer.facades.storagepath',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\storagepath.py',
   'PYMODULE'),
  ('plyer.facades.stt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\stt.py',
   'PYMODULE'),
  ('plyer.facades.temperature',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\temperature.py',
   'PYMODULE'),
  ('plyer.facades.tts',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\tts.py',
   'PYMODULE'),
  ('plyer.facades.uniqueid',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\uniqueid.py',
   'PYMODULE'),
  ('plyer.facades.vibrator',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\vibrator.py',
   'PYMODULE'),
  ('plyer.facades.wifi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\facades\\wifi.py',
   'PYMODULE'),
  ('plyer.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\plyer\\utils.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\pprint.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pyarrow',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\__init__.py',
   'PYMODULE'),
  ('pyarrow._compute_docstrings',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\_compute_docstrings.py',
   'PYMODULE'),
  ('pyarrow._generated_version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\_generated_version.py',
   'PYMODULE'),
  ('pyarrow.acero',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\acero.py',
   'PYMODULE'),
  ('pyarrow.benchmark',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\benchmark.py',
   'PYMODULE'),
  ('pyarrow.cffi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\cffi.py',
   'PYMODULE'),
  ('pyarrow.compute',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\compute.py',
   'PYMODULE'),
  ('pyarrow.conftest',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\conftest.py',
   'PYMODULE'),
  ('pyarrow.csv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\csv.py',
   'PYMODULE'),
  ('pyarrow.cuda',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\cuda.py',
   'PYMODULE'),
  ('pyarrow.dataset',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\dataset.py',
   'PYMODULE'),
  ('pyarrow.feather',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\feather.py',
   'PYMODULE'),
  ('pyarrow.flight',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\flight.py',
   'PYMODULE'),
  ('pyarrow.fs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\fs.py',
   'PYMODULE'),
  ('pyarrow.interchange',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\interchange\\__init__.py',
   'PYMODULE'),
  ('pyarrow.interchange.buffer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\interchange\\buffer.py',
   'PYMODULE'),
  ('pyarrow.interchange.column',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\interchange\\column.py',
   'PYMODULE'),
  ('pyarrow.interchange.dataframe',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pyarrow.interchange.from_dataframe',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pyarrow.ipc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\ipc.py',
   'PYMODULE'),
  ('pyarrow.json',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\json.py',
   'PYMODULE'),
  ('pyarrow.jvm',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\jvm.py',
   'PYMODULE'),
  ('pyarrow.orc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\orc.py',
   'PYMODULE'),
  ('pyarrow.pandas_compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\pandas_compat.py',
   'PYMODULE'),
  ('pyarrow.parquet',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\parquet\\__init__.py',
   'PYMODULE'),
  ('pyarrow.parquet.core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\parquet\\core.py',
   'PYMODULE'),
  ('pyarrow.parquet.encryption',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\parquet\\encryption.py',
   'PYMODULE'),
  ('pyarrow.substrait',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\substrait.py',
   'PYMODULE'),
  ('pyarrow.tests',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\tests\\__init__.py',
   'PYMODULE'),
  ('pyarrow.tests.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\tests\\util.py',
   'PYMODULE'),
  ('pyarrow.types',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\types.py',
   'PYMODULE'),
  ('pyarrow.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\util.py',
   'PYMODULE'),
  ('pyarrow.vendored',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\vendored\\__init__.py',
   'PYMODULE'),
  ('pyarrow.vendored.docscrape',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\vendored\\docscrape.py',
   'PYMODULE'),
  ('pyarrow.vendored.version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyarrow\\vendored\\version.py',
   'PYMODULE'),
  ('pycparser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pygments',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\__init__.py',
   'PYMODULE'),
  ('pygments.console',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\console.py',
   'PYMODULE'),
  ('pygments.filter',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\filter.py',
   'PYMODULE'),
  ('pygments.filters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\filters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatter',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatter.py',
   'PYMODULE'),
  ('pygments.formatters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatters._mapping',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\_mapping.py',
   'PYMODULE'),
  ('pygments.formatters.bbcode',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\bbcode.py',
   'PYMODULE'),
  ('pygments.formatters.groff',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\groff.py',
   'PYMODULE'),
  ('pygments.formatters.html',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\html.py',
   'PYMODULE'),
  ('pygments.formatters.img',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\img.py',
   'PYMODULE'),
  ('pygments.formatters.irc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\irc.py',
   'PYMODULE'),
  ('pygments.formatters.latex',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\latex.py',
   'PYMODULE'),
  ('pygments.formatters.other',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\other.py',
   'PYMODULE'),
  ('pygments.formatters.pangomarkup',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\pangomarkup.py',
   'PYMODULE'),
  ('pygments.formatters.rtf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\rtf.py',
   'PYMODULE'),
  ('pygments.formatters.svg',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\svg.py',
   'PYMODULE'),
  ('pygments.formatters.terminal',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\terminal.py',
   'PYMODULE'),
  ('pygments.formatters.terminal256',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\formatters\\terminal256.py',
   'PYMODULE'),
  ('pygments.lexer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexer.py',
   'PYMODULE'),
  ('pygments.lexers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\__init__.py',
   'PYMODULE'),
  ('pygments.lexers._ada_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_ada_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._asy_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_asy_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cl_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_cl_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cocoa_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_cocoa_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._csound_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_csound_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._css_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_css_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._googlesql_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_googlesql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._julia_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_julia_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lasso_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_lasso_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lilypond_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_lilypond_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lua_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_lua_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._luau_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_luau_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mapping',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_mapping.py',
   'PYMODULE'),
  ('pygments.lexers._mql_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_mql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mysql_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_mysql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._openedge_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_openedge_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._php_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_php_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._postgres_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_postgres_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._qlik_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_qlik_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scheme_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_scheme_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scilab_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_scilab_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._sourcemod_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_sourcemod_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stan_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_stan_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stata_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_stata_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._tsql_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_tsql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._usd_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_usd_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vbscript_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_vbscript_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vim_builtins',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\_vim_builtins.py',
   'PYMODULE'),
  ('pygments.lexers.actionscript',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\actionscript.py',
   'PYMODULE'),
  ('pygments.lexers.ada',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ada.py',
   'PYMODULE'),
  ('pygments.lexers.agile',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\agile.py',
   'PYMODULE'),
  ('pygments.lexers.algebra',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\algebra.py',
   'PYMODULE'),
  ('pygments.lexers.ambient',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ambient.py',
   'PYMODULE'),
  ('pygments.lexers.amdgpu',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\amdgpu.py',
   'PYMODULE'),
  ('pygments.lexers.ampl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ampl.py',
   'PYMODULE'),
  ('pygments.lexers.apdlexer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\apdlexer.py',
   'PYMODULE'),
  ('pygments.lexers.apl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\apl.py',
   'PYMODULE'),
  ('pygments.lexers.archetype',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\archetype.py',
   'PYMODULE'),
  ('pygments.lexers.arrow',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\arrow.py',
   'PYMODULE'),
  ('pygments.lexers.arturo',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\arturo.py',
   'PYMODULE'),
  ('pygments.lexers.asc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\asc.py',
   'PYMODULE'),
  ('pygments.lexers.asm',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\asm.py',
   'PYMODULE'),
  ('pygments.lexers.asn1',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\asn1.py',
   'PYMODULE'),
  ('pygments.lexers.automation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\automation.py',
   'PYMODULE'),
  ('pygments.lexers.bare',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\bare.py',
   'PYMODULE'),
  ('pygments.lexers.basic',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\basic.py',
   'PYMODULE'),
  ('pygments.lexers.bdd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\bdd.py',
   'PYMODULE'),
  ('pygments.lexers.berry',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\berry.py',
   'PYMODULE'),
  ('pygments.lexers.bibtex',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\bibtex.py',
   'PYMODULE'),
  ('pygments.lexers.blueprint',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\blueprint.py',
   'PYMODULE'),
  ('pygments.lexers.boa',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\boa.py',
   'PYMODULE'),
  ('pygments.lexers.bqn',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\bqn.py',
   'PYMODULE'),
  ('pygments.lexers.business',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\business.py',
   'PYMODULE'),
  ('pygments.lexers.c_cpp',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\c_cpp.py',
   'PYMODULE'),
  ('pygments.lexers.c_like',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\c_like.py',
   'PYMODULE'),
  ('pygments.lexers.capnproto',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\capnproto.py',
   'PYMODULE'),
  ('pygments.lexers.carbon',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\carbon.py',
   'PYMODULE'),
  ('pygments.lexers.cddl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\cddl.py',
   'PYMODULE'),
  ('pygments.lexers.chapel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\chapel.py',
   'PYMODULE'),
  ('pygments.lexers.clean',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\clean.py',
   'PYMODULE'),
  ('pygments.lexers.codeql',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\codeql.py',
   'PYMODULE'),
  ('pygments.lexers.comal',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\comal.py',
   'PYMODULE'),
  ('pygments.lexers.compiled',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\compiled.py',
   'PYMODULE'),
  ('pygments.lexers.configs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\configs.py',
   'PYMODULE'),
  ('pygments.lexers.console',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\console.py',
   'PYMODULE'),
  ('pygments.lexers.cplint',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\cplint.py',
   'PYMODULE'),
  ('pygments.lexers.crystal',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\crystal.py',
   'PYMODULE'),
  ('pygments.lexers.csound',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\csound.py',
   'PYMODULE'),
  ('pygments.lexers.css',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\css.py',
   'PYMODULE'),
  ('pygments.lexers.d',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\d.py',
   'PYMODULE'),
  ('pygments.lexers.dalvik',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\dalvik.py',
   'PYMODULE'),
  ('pygments.lexers.data',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\data.py',
   'PYMODULE'),
  ('pygments.lexers.dax',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\dax.py',
   'PYMODULE'),
  ('pygments.lexers.devicetree',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\devicetree.py',
   'PYMODULE'),
  ('pygments.lexers.diff',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\diff.py',
   'PYMODULE'),
  ('pygments.lexers.dns',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\dns.py',
   'PYMODULE'),
  ('pygments.lexers.dotnet',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\dotnet.py',
   'PYMODULE'),
  ('pygments.lexers.dsls',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\dsls.py',
   'PYMODULE'),
  ('pygments.lexers.dylan',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\dylan.py',
   'PYMODULE'),
  ('pygments.lexers.ecl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ecl.py',
   'PYMODULE'),
  ('pygments.lexers.eiffel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\eiffel.py',
   'PYMODULE'),
  ('pygments.lexers.elm',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\elm.py',
   'PYMODULE'),
  ('pygments.lexers.elpi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\elpi.py',
   'PYMODULE'),
  ('pygments.lexers.email',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\email.py',
   'PYMODULE'),
  ('pygments.lexers.erlang',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\erlang.py',
   'PYMODULE'),
  ('pygments.lexers.esoteric',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\esoteric.py',
   'PYMODULE'),
  ('pygments.lexers.ezhil',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ezhil.py',
   'PYMODULE'),
  ('pygments.lexers.factor',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\factor.py',
   'PYMODULE'),
  ('pygments.lexers.fantom',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\fantom.py',
   'PYMODULE'),
  ('pygments.lexers.felix',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\felix.py',
   'PYMODULE'),
  ('pygments.lexers.fift',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\fift.py',
   'PYMODULE'),
  ('pygments.lexers.floscript',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\floscript.py',
   'PYMODULE'),
  ('pygments.lexers.forth',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\forth.py',
   'PYMODULE'),
  ('pygments.lexers.fortran',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\fortran.py',
   'PYMODULE'),
  ('pygments.lexers.foxpro',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\foxpro.py',
   'PYMODULE'),
  ('pygments.lexers.freefem',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\freefem.py',
   'PYMODULE'),
  ('pygments.lexers.func',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\func.py',
   'PYMODULE'),
  ('pygments.lexers.functional',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\functional.py',
   'PYMODULE'),
  ('pygments.lexers.futhark',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\futhark.py',
   'PYMODULE'),
  ('pygments.lexers.gcodelexer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\gcodelexer.py',
   'PYMODULE'),
  ('pygments.lexers.gdscript',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\gdscript.py',
   'PYMODULE'),
  ('pygments.lexers.gleam',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\gleam.py',
   'PYMODULE'),
  ('pygments.lexers.go',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\go.py',
   'PYMODULE'),
  ('pygments.lexers.grammar_notation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\grammar_notation.py',
   'PYMODULE'),
  ('pygments.lexers.graph',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\graph.py',
   'PYMODULE'),
  ('pygments.lexers.graphics',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\graphics.py',
   'PYMODULE'),
  ('pygments.lexers.graphql',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\graphql.py',
   'PYMODULE'),
  ('pygments.lexers.graphviz',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\graphviz.py',
   'PYMODULE'),
  ('pygments.lexers.gsql',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\gsql.py',
   'PYMODULE'),
  ('pygments.lexers.hare',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\hare.py',
   'PYMODULE'),
  ('pygments.lexers.haskell',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\haskell.py',
   'PYMODULE'),
  ('pygments.lexers.haxe',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\haxe.py',
   'PYMODULE'),
  ('pygments.lexers.hdl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\hdl.py',
   'PYMODULE'),
  ('pygments.lexers.hexdump',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\hexdump.py',
   'PYMODULE'),
  ('pygments.lexers.html',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\html.py',
   'PYMODULE'),
  ('pygments.lexers.idl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\idl.py',
   'PYMODULE'),
  ('pygments.lexers.igor',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\igor.py',
   'PYMODULE'),
  ('pygments.lexers.inferno',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\inferno.py',
   'PYMODULE'),
  ('pygments.lexers.installers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\installers.py',
   'PYMODULE'),
  ('pygments.lexers.int_fiction',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\int_fiction.py',
   'PYMODULE'),
  ('pygments.lexers.iolang',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\iolang.py',
   'PYMODULE'),
  ('pygments.lexers.j',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\j.py',
   'PYMODULE'),
  ('pygments.lexers.javascript',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\javascript.py',
   'PYMODULE'),
  ('pygments.lexers.jmespath',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\jmespath.py',
   'PYMODULE'),
  ('pygments.lexers.jslt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\jslt.py',
   'PYMODULE'),
  ('pygments.lexers.json5',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\json5.py',
   'PYMODULE'),
  ('pygments.lexers.jsonnet',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\jsonnet.py',
   'PYMODULE'),
  ('pygments.lexers.jsx',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\jsx.py',
   'PYMODULE'),
  ('pygments.lexers.julia',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\julia.py',
   'PYMODULE'),
  ('pygments.lexers.jvm',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\jvm.py',
   'PYMODULE'),
  ('pygments.lexers.kuin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\kuin.py',
   'PYMODULE'),
  ('pygments.lexers.kusto',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\kusto.py',
   'PYMODULE'),
  ('pygments.lexers.ldap',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ldap.py',
   'PYMODULE'),
  ('pygments.lexers.lean',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\lean.py',
   'PYMODULE'),
  ('pygments.lexers.lilypond',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\lilypond.py',
   'PYMODULE'),
  ('pygments.lexers.lisp',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\lisp.py',
   'PYMODULE'),
  ('pygments.lexers.macaulay2',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\macaulay2.py',
   'PYMODULE'),
  ('pygments.lexers.make',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\make.py',
   'PYMODULE'),
  ('pygments.lexers.maple',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\maple.py',
   'PYMODULE'),
  ('pygments.lexers.markup',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\markup.py',
   'PYMODULE'),
  ('pygments.lexers.math',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\math.py',
   'PYMODULE'),
  ('pygments.lexers.matlab',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\matlab.py',
   'PYMODULE'),
  ('pygments.lexers.maxima',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\maxima.py',
   'PYMODULE'),
  ('pygments.lexers.meson',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\meson.py',
   'PYMODULE'),
  ('pygments.lexers.mime',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\mime.py',
   'PYMODULE'),
  ('pygments.lexers.minecraft',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\minecraft.py',
   'PYMODULE'),
  ('pygments.lexers.mips',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\mips.py',
   'PYMODULE'),
  ('pygments.lexers.ml',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ml.py',
   'PYMODULE'),
  ('pygments.lexers.modeling',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\modeling.py',
   'PYMODULE'),
  ('pygments.lexers.modula2',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\modula2.py',
   'PYMODULE'),
  ('pygments.lexers.mojo',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\mojo.py',
   'PYMODULE'),
  ('pygments.lexers.monte',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\monte.py',
   'PYMODULE'),
  ('pygments.lexers.mosel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\mosel.py',
   'PYMODULE'),
  ('pygments.lexers.ncl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ncl.py',
   'PYMODULE'),
  ('pygments.lexers.nimrod',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\nimrod.py',
   'PYMODULE'),
  ('pygments.lexers.nit',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\nit.py',
   'PYMODULE'),
  ('pygments.lexers.nix',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\nix.py',
   'PYMODULE'),
  ('pygments.lexers.numbair',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\numbair.py',
   'PYMODULE'),
  ('pygments.lexers.oberon',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\oberon.py',
   'PYMODULE'),
  ('pygments.lexers.objective',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\objective.py',
   'PYMODULE'),
  ('pygments.lexers.ooc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ooc.py',
   'PYMODULE'),
  ('pygments.lexers.openscad',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\openscad.py',
   'PYMODULE'),
  ('pygments.lexers.other',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\other.py',
   'PYMODULE'),
  ('pygments.lexers.parasail',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\parasail.py',
   'PYMODULE'),
  ('pygments.lexers.parsers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\parsers.py',
   'PYMODULE'),
  ('pygments.lexers.pascal',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\pascal.py',
   'PYMODULE'),
  ('pygments.lexers.pawn',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\pawn.py',
   'PYMODULE'),
  ('pygments.lexers.pddl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\pddl.py',
   'PYMODULE'),
  ('pygments.lexers.perl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\perl.py',
   'PYMODULE'),
  ('pygments.lexers.phix',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\phix.py',
   'PYMODULE'),
  ('pygments.lexers.php',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\php.py',
   'PYMODULE'),
  ('pygments.lexers.pointless',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\pointless.py',
   'PYMODULE'),
  ('pygments.lexers.pony',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\pony.py',
   'PYMODULE'),
  ('pygments.lexers.praat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\praat.py',
   'PYMODULE'),
  ('pygments.lexers.procfile',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\procfile.py',
   'PYMODULE'),
  ('pygments.lexers.prolog',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\prolog.py',
   'PYMODULE'),
  ('pygments.lexers.promql',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\promql.py',
   'PYMODULE'),
  ('pygments.lexers.prql',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\prql.py',
   'PYMODULE'),
  ('pygments.lexers.ptx',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ptx.py',
   'PYMODULE'),
  ('pygments.lexers.python',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\python.py',
   'PYMODULE'),
  ('pygments.lexers.q',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\q.py',
   'PYMODULE'),
  ('pygments.lexers.qlik',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\qlik.py',
   'PYMODULE'),
  ('pygments.lexers.qvt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\qvt.py',
   'PYMODULE'),
  ('pygments.lexers.r',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\r.py',
   'PYMODULE'),
  ('pygments.lexers.rdf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\rdf.py',
   'PYMODULE'),
  ('pygments.lexers.rebol',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\rebol.py',
   'PYMODULE'),
  ('pygments.lexers.rego',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\rego.py',
   'PYMODULE'),
  ('pygments.lexers.resource',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\resource.py',
   'PYMODULE'),
  ('pygments.lexers.ride',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ride.py',
   'PYMODULE'),
  ('pygments.lexers.rita',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\rita.py',
   'PYMODULE'),
  ('pygments.lexers.rnc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\rnc.py',
   'PYMODULE'),
  ('pygments.lexers.roboconf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\roboconf.py',
   'PYMODULE'),
  ('pygments.lexers.robotframework',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\robotframework.py',
   'PYMODULE'),
  ('pygments.lexers.ruby',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ruby.py',
   'PYMODULE'),
  ('pygments.lexers.rust',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\rust.py',
   'PYMODULE'),
  ('pygments.lexers.sas',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\sas.py',
   'PYMODULE'),
  ('pygments.lexers.savi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\savi.py',
   'PYMODULE'),
  ('pygments.lexers.scdoc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\scdoc.py',
   'PYMODULE'),
  ('pygments.lexers.scripting',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\scripting.py',
   'PYMODULE'),
  ('pygments.lexers.sgf',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\sgf.py',
   'PYMODULE'),
  ('pygments.lexers.shell',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\shell.py',
   'PYMODULE'),
  ('pygments.lexers.sieve',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\sieve.py',
   'PYMODULE'),
  ('pygments.lexers.slash',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\slash.py',
   'PYMODULE'),
  ('pygments.lexers.smalltalk',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\smalltalk.py',
   'PYMODULE'),
  ('pygments.lexers.smithy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\smithy.py',
   'PYMODULE'),
  ('pygments.lexers.smv',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\smv.py',
   'PYMODULE'),
  ('pygments.lexers.snobol',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\snobol.py',
   'PYMODULE'),
  ('pygments.lexers.solidity',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\solidity.py',
   'PYMODULE'),
  ('pygments.lexers.soong',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\soong.py',
   'PYMODULE'),
  ('pygments.lexers.sophia',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\sophia.py',
   'PYMODULE'),
  ('pygments.lexers.special',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\special.py',
   'PYMODULE'),
  ('pygments.lexers.spice',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\spice.py',
   'PYMODULE'),
  ('pygments.lexers.sql',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\sql.py',
   'PYMODULE'),
  ('pygments.lexers.srcinfo',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\srcinfo.py',
   'PYMODULE'),
  ('pygments.lexers.stata',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\stata.py',
   'PYMODULE'),
  ('pygments.lexers.supercollider',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\supercollider.py',
   'PYMODULE'),
  ('pygments.lexers.tablegen',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\tablegen.py',
   'PYMODULE'),
  ('pygments.lexers.tact',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\tact.py',
   'PYMODULE'),
  ('pygments.lexers.tal',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\tal.py',
   'PYMODULE'),
  ('pygments.lexers.tcl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\tcl.py',
   'PYMODULE'),
  ('pygments.lexers.teal',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\teal.py',
   'PYMODULE'),
  ('pygments.lexers.templates',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\templates.py',
   'PYMODULE'),
  ('pygments.lexers.teraterm',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\teraterm.py',
   'PYMODULE'),
  ('pygments.lexers.testing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\testing.py',
   'PYMODULE'),
  ('pygments.lexers.text',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\text.py',
   'PYMODULE'),
  ('pygments.lexers.textedit',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\textedit.py',
   'PYMODULE'),
  ('pygments.lexers.textfmts',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\textfmts.py',
   'PYMODULE'),
  ('pygments.lexers.theorem',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\theorem.py',
   'PYMODULE'),
  ('pygments.lexers.thingsdb',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\thingsdb.py',
   'PYMODULE'),
  ('pygments.lexers.tlb',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\tlb.py',
   'PYMODULE'),
  ('pygments.lexers.tls',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\tls.py',
   'PYMODULE'),
  ('pygments.lexers.tnt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\tnt.py',
   'PYMODULE'),
  ('pygments.lexers.trafficscript',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\trafficscript.py',
   'PYMODULE'),
  ('pygments.lexers.typoscript',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\typoscript.py',
   'PYMODULE'),
  ('pygments.lexers.typst',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\typst.py',
   'PYMODULE'),
  ('pygments.lexers.ul4',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\ul4.py',
   'PYMODULE'),
  ('pygments.lexers.unicon',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\unicon.py',
   'PYMODULE'),
  ('pygments.lexers.urbi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\urbi.py',
   'PYMODULE'),
  ('pygments.lexers.usd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\usd.py',
   'PYMODULE'),
  ('pygments.lexers.varnish',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\varnish.py',
   'PYMODULE'),
  ('pygments.lexers.verification',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\verification.py',
   'PYMODULE'),
  ('pygments.lexers.verifpal',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\verifpal.py',
   'PYMODULE'),
  ('pygments.lexers.vip',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\vip.py',
   'PYMODULE'),
  ('pygments.lexers.vyper',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\vyper.py',
   'PYMODULE'),
  ('pygments.lexers.web',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\web.py',
   'PYMODULE'),
  ('pygments.lexers.webassembly',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\webassembly.py',
   'PYMODULE'),
  ('pygments.lexers.webidl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\webidl.py',
   'PYMODULE'),
  ('pygments.lexers.webmisc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\webmisc.py',
   'PYMODULE'),
  ('pygments.lexers.wgsl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\wgsl.py',
   'PYMODULE'),
  ('pygments.lexers.whiley',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\whiley.py',
   'PYMODULE'),
  ('pygments.lexers.wowtoc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\wowtoc.py',
   'PYMODULE'),
  ('pygments.lexers.wren',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\wren.py',
   'PYMODULE'),
  ('pygments.lexers.x10',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\x10.py',
   'PYMODULE'),
  ('pygments.lexers.xorg',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\xorg.py',
   'PYMODULE'),
  ('pygments.lexers.yang',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\yang.py',
   'PYMODULE'),
  ('pygments.lexers.yara',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\yara.py',
   'PYMODULE'),
  ('pygments.lexers.zig',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\lexers\\zig.py',
   'PYMODULE'),
  ('pygments.modeline',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\modeline.py',
   'PYMODULE'),
  ('pygments.plugin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\plugin.py',
   'PYMODULE'),
  ('pygments.regexopt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\regexopt.py',
   'PYMODULE'),
  ('pygments.scanner',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\scanner.py',
   'PYMODULE'),
  ('pygments.style',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\style.py',
   'PYMODULE'),
  ('pygments.styles',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\__init__.py',
   'PYMODULE'),
  ('pygments.styles._mapping',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\_mapping.py',
   'PYMODULE'),
  ('pygments.styles.abap',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\abap.py',
   'PYMODULE'),
  ('pygments.styles.algol',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\algol.py',
   'PYMODULE'),
  ('pygments.styles.algol_nu',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\algol_nu.py',
   'PYMODULE'),
  ('pygments.styles.arduino',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\arduino.py',
   'PYMODULE'),
  ('pygments.styles.autumn',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\autumn.py',
   'PYMODULE'),
  ('pygments.styles.borland',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\borland.py',
   'PYMODULE'),
  ('pygments.styles.bw',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\bw.py',
   'PYMODULE'),
  ('pygments.styles.coffee',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\coffee.py',
   'PYMODULE'),
  ('pygments.styles.colorful',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\colorful.py',
   'PYMODULE'),
  ('pygments.styles.default',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\default.py',
   'PYMODULE'),
  ('pygments.styles.dracula',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\dracula.py',
   'PYMODULE'),
  ('pygments.styles.emacs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\emacs.py',
   'PYMODULE'),
  ('pygments.styles.friendly',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\friendly.py',
   'PYMODULE'),
  ('pygments.styles.friendly_grayscale',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\friendly_grayscale.py',
   'PYMODULE'),
  ('pygments.styles.fruity',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\fruity.py',
   'PYMODULE'),
  ('pygments.styles.gh_dark',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\gh_dark.py',
   'PYMODULE'),
  ('pygments.styles.gruvbox',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\gruvbox.py',
   'PYMODULE'),
  ('pygments.styles.igor',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\igor.py',
   'PYMODULE'),
  ('pygments.styles.inkpot',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\inkpot.py',
   'PYMODULE'),
  ('pygments.styles.lightbulb',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\lightbulb.py',
   'PYMODULE'),
  ('pygments.styles.lilypond',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\lilypond.py',
   'PYMODULE'),
  ('pygments.styles.lovelace',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\lovelace.py',
   'PYMODULE'),
  ('pygments.styles.manni',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\manni.py',
   'PYMODULE'),
  ('pygments.styles.material',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\material.py',
   'PYMODULE'),
  ('pygments.styles.monokai',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\monokai.py',
   'PYMODULE'),
  ('pygments.styles.murphy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\murphy.py',
   'PYMODULE'),
  ('pygments.styles.native',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\native.py',
   'PYMODULE'),
  ('pygments.styles.nord',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\nord.py',
   'PYMODULE'),
  ('pygments.styles.onedark',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\onedark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_dark',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\paraiso_dark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_light',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\paraiso_light.py',
   'PYMODULE'),
  ('pygments.styles.pastie',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\pastie.py',
   'PYMODULE'),
  ('pygments.styles.perldoc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\perldoc.py',
   'PYMODULE'),
  ('pygments.styles.rainbow_dash',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\rainbow_dash.py',
   'PYMODULE'),
  ('pygments.styles.rrt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\rrt.py',
   'PYMODULE'),
  ('pygments.styles.sas',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\sas.py',
   'PYMODULE'),
  ('pygments.styles.solarized',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\solarized.py',
   'PYMODULE'),
  ('pygments.styles.staroffice',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\staroffice.py',
   'PYMODULE'),
  ('pygments.styles.stata_dark',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\stata_dark.py',
   'PYMODULE'),
  ('pygments.styles.stata_light',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\stata_light.py',
   'PYMODULE'),
  ('pygments.styles.tango',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\tango.py',
   'PYMODULE'),
  ('pygments.styles.trac',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\trac.py',
   'PYMODULE'),
  ('pygments.styles.vim',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\vim.py',
   'PYMODULE'),
  ('pygments.styles.vs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\vs.py',
   'PYMODULE'),
  ('pygments.styles.xcode',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\xcode.py',
   'PYMODULE'),
  ('pygments.styles.zenburn',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\styles\\zenburn.py',
   'PYMODULE'),
  ('pygments.token',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\token.py',
   'PYMODULE'),
  ('pygments.unistring',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\unistring.py',
   'PYMODULE'),
  ('pygments.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pygments\\util.py',
   'PYMODULE'),
  ('pyparsing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.common',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('pythoncom',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('pytz',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pywin',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('pywin.mfc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin32_system32', '-', 'PYMODULE'),
  ('pywintypes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('queue',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\random.py',
   'PYMODULE'),
  ('requests',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\runpy.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\selectors.py',
   'PYMODULE'),
  ('setuptools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._deprecation_warning',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_deprecation_warning.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\_collections.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.py37compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\py37compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.register',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\register.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\upload.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\py39compat.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.__about__',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.actions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.common',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.diagram',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.helpers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.results',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.testing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.unicode',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.extern',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\py34compat.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\signal.py',
   'PYMODULE'),
  ('site',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\site.py',
   'PYMODULE'),
  ('six',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('smtplib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\smtplib.py',
   'PYMODULE'),
  ('sniffio',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\sniffio\\__init__.py',
   'PYMODULE'),
  ('sniffio._impl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\sniffio\\_impl.py',
   'PYMODULE'),
  ('sniffio._version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\sniffio\\_version.py',
   'PYMODULE'),
  ('socket',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\socketserver.py',
   'PYMODULE'),
  ('socks',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\socks.py',
   'PYMODULE'),
  ('sortedcontainers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\sortedcontainers\\__init__.py',
   'PYMODULE'),
  ('sortedcontainers.sorteddict',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\sortedcontainers\\sorteddict.py',
   'PYMODULE'),
  ('sortedcontainers.sortedlist',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\sortedcontainers\\sortedlist.py',
   'PYMODULE'),
  ('sortedcontainers.sortedset',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\sortedcontainers\\sortedset.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\ssl.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\threading.py',
   'PYMODULE'),
  ('tkinter',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.font',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\tkinter\\font.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('token',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tornado',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\__init__.py',
   'PYMODULE'),
  ('tornado._locale_data',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\_locale_data.py',
   'PYMODULE'),
  ('tornado.autoreload',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\autoreload.py',
   'PYMODULE'),
  ('tornado.concurrent',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\concurrent.py',
   'PYMODULE'),
  ('tornado.escape',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\escape.py',
   'PYMODULE'),
  ('tornado.gen',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\gen.py',
   'PYMODULE'),
  ('tornado.http1connection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\http1connection.py',
   'PYMODULE'),
  ('tornado.httpclient',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\httpclient.py',
   'PYMODULE'),
  ('tornado.httpserver',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\httpserver.py',
   'PYMODULE'),
  ('tornado.httputil',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\httputil.py',
   'PYMODULE'),
  ('tornado.ioloop',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\ioloop.py',
   'PYMODULE'),
  ('tornado.iostream',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\iostream.py',
   'PYMODULE'),
  ('tornado.locale',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\locale.py',
   'PYMODULE'),
  ('tornado.locks',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\locks.py',
   'PYMODULE'),
  ('tornado.log',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\log.py',
   'PYMODULE'),
  ('tornado.netutil',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\netutil.py',
   'PYMODULE'),
  ('tornado.options',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\options.py',
   'PYMODULE'),
  ('tornado.platform',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\platform\\__init__.py',
   'PYMODULE'),
  ('tornado.platform.asyncio',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\platform\\asyncio.py',
   'PYMODULE'),
  ('tornado.process',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\process.py',
   'PYMODULE'),
  ('tornado.queues',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\queues.py',
   'PYMODULE'),
  ('tornado.routing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\routing.py',
   'PYMODULE'),
  ('tornado.simple_httpclient',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\simple_httpclient.py',
   'PYMODULE'),
  ('tornado.tcpclient',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\tcpclient.py',
   'PYMODULE'),
  ('tornado.tcpserver',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\tcpserver.py',
   'PYMODULE'),
  ('tornado.template',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\template.py',
   'PYMODULE'),
  ('tornado.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\util.py',
   'PYMODULE'),
  ('tornado.web',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\web.py',
   'PYMODULE'),
  ('tornado.websocket',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\tornado\\websocket.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('trio',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\__init__.py',
   'PYMODULE'),
  ('trio._abc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_abc.py',
   'PYMODULE'),
  ('trio._channel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_channel.py',
   'PYMODULE'),
  ('trio._core',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\__init__.py',
   'PYMODULE'),
  ('trio._core._asyncgens',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_asyncgens.py',
   'PYMODULE'),
  ('trio._core._concat_tb',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_concat_tb.py',
   'PYMODULE'),
  ('trio._core._entry_queue',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_entry_queue.py',
   'PYMODULE'),
  ('trio._core._exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_exceptions.py',
   'PYMODULE'),
  ('trio._core._generated_instrumentation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_generated_instrumentation.py',
   'PYMODULE'),
  ('trio._core._generated_io_epoll',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_generated_io_epoll.py',
   'PYMODULE'),
  ('trio._core._generated_io_kqueue',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_generated_io_kqueue.py',
   'PYMODULE'),
  ('trio._core._generated_io_windows',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_generated_io_windows.py',
   'PYMODULE'),
  ('trio._core._generated_run',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_generated_run.py',
   'PYMODULE'),
  ('trio._core._instrumentation',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_instrumentation.py',
   'PYMODULE'),
  ('trio._core._io_common',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_io_common.py',
   'PYMODULE'),
  ('trio._core._io_epoll',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_io_epoll.py',
   'PYMODULE'),
  ('trio._core._io_kqueue',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_io_kqueue.py',
   'PYMODULE'),
  ('trio._core._io_windows',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_io_windows.py',
   'PYMODULE'),
  ('trio._core._ki',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_ki.py',
   'PYMODULE'),
  ('trio._core._local',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_local.py',
   'PYMODULE'),
  ('trio._core._mock_clock',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_mock_clock.py',
   'PYMODULE'),
  ('trio._core._parking_lot',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_parking_lot.py',
   'PYMODULE'),
  ('trio._core._run',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_run.py',
   'PYMODULE'),
  ('trio._core._run_context',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_run_context.py',
   'PYMODULE'),
  ('trio._core._thread_cache',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_thread_cache.py',
   'PYMODULE'),
  ('trio._core._traps',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_traps.py',
   'PYMODULE'),
  ('trio._core._unbounded_queue',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_unbounded_queue.py',
   'PYMODULE'),
  ('trio._core._wakeup_socketpair',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_wakeup_socketpair.py',
   'PYMODULE'),
  ('trio._core._windows_cffi',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_core\\_windows_cffi.py',
   'PYMODULE'),
  ('trio._deprecate',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_deprecate.py',
   'PYMODULE'),
  ('trio._dtls',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_dtls.py',
   'PYMODULE'),
  ('trio._file_io',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_file_io.py',
   'PYMODULE'),
  ('trio._highlevel_generic',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_highlevel_generic.py',
   'PYMODULE'),
  ('trio._highlevel_open_tcp_listeners',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_highlevel_open_tcp_listeners.py',
   'PYMODULE'),
  ('trio._highlevel_open_tcp_stream',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_highlevel_open_tcp_stream.py',
   'PYMODULE'),
  ('trio._highlevel_open_unix_stream',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_highlevel_open_unix_stream.py',
   'PYMODULE'),
  ('trio._highlevel_serve_listeners',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_highlevel_serve_listeners.py',
   'PYMODULE'),
  ('trio._highlevel_socket',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_highlevel_socket.py',
   'PYMODULE'),
  ('trio._highlevel_ssl_helpers',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_highlevel_ssl_helpers.py',
   'PYMODULE'),
  ('trio._path',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_path.py',
   'PYMODULE'),
  ('trio._signals',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_signals.py',
   'PYMODULE'),
  ('trio._socket',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_socket.py',
   'PYMODULE'),
  ('trio._ssl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_ssl.py',
   'PYMODULE'),
  ('trio._subprocess',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_subprocess.py',
   'PYMODULE'),
  ('trio._subprocess_platform',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_subprocess_platform\\__init__.py',
   'PYMODULE'),
  ('trio._subprocess_platform.kqueue',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_subprocess_platform\\kqueue.py',
   'PYMODULE'),
  ('trio._subprocess_platform.waitid',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_subprocess_platform\\waitid.py',
   'PYMODULE'),
  ('trio._subprocess_platform.windows',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_subprocess_platform\\windows.py',
   'PYMODULE'),
  ('trio._sync',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_sync.py',
   'PYMODULE'),
  ('trio._threads',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_threads.py',
   'PYMODULE'),
  ('trio._timeouts',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_timeouts.py',
   'PYMODULE'),
  ('trio._unix_pipes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_unix_pipes.py',
   'PYMODULE'),
  ('trio._util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_util.py',
   'PYMODULE'),
  ('trio._version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_version.py',
   'PYMODULE'),
  ('trio._wait_for_object',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_wait_for_object.py',
   'PYMODULE'),
  ('trio._windows_pipes',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\_windows_pipes.py',
   'PYMODULE'),
  ('trio.abc',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\abc.py',
   'PYMODULE'),
  ('trio.from_thread',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\from_thread.py',
   'PYMODULE'),
  ('trio.lowlevel',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\lowlevel.py',
   'PYMODULE'),
  ('trio.socket',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\socket.py',
   'PYMODULE'),
  ('trio.testing',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\testing\\__init__.py',
   'PYMODULE'),
  ('trio.testing._check_streams',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\testing\\_check_streams.py',
   'PYMODULE'),
  ('trio.testing._checkpoints',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\testing\\_checkpoints.py',
   'PYMODULE'),
  ('trio.testing._memory_streams',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\testing\\_memory_streams.py',
   'PYMODULE'),
  ('trio.testing._network',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\testing\\_network.py',
   'PYMODULE'),
  ('trio.testing._raises_group',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\testing\\_raises_group.py',
   'PYMODULE'),
  ('trio.testing._sequencer',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\testing\\_sequencer.py',
   'PYMODULE'),
  ('trio.testing._trio_test',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\testing\\_trio_test.py',
   'PYMODULE'),
  ('trio.to_thread',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\trio\\to_thread.py',
   'PYMODULE'),
  ('tty',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\tty.py',
   'PYMODULE'),
  ('typing',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\uuid.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('win32com',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.client',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.build',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('win32com.client.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('win32com.server',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.server.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.shell',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32comext\\shell\\__init__.py',
   'PYMODULE'),
  ('win32com.shell.shellcon',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32comext\\shell\\shellcon.py',
   'PYMODULE'),
  ('win32com.universal',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.util',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32con',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('win32traceutil',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('winerror',
   'C:\\Users\\<USER>\\Desktop\\Python\\Tools\\kivy_venv\\Lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('xml',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.dom',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\zipfile.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\\Lib\\zipimport.py',
   'PYMODULE')])
