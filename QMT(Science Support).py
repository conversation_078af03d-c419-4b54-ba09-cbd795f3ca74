import kivy
import getpass
import os
import pyodbc
import re   
import datetime
from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.label import Label
from kivy.uix.textinput import TextInput
from kivy.uix.button import Button
from kivy.uix.spinner import Spinner, SpinnerOption
from kivy.uix.dropdown import DropDown
from kivy.core.window import Window
from kivy.uix.popup import Popup
from kivy.metrics import dp
from kivy.uix.image import Image
from kivy.uix.scrollview import ScrollView
from kivy.uix.gridlayout import GridLayout
from kivy.graphics import Color, Ellipse
from kivy.uix.floatlayout import FloatLayout
from kivy.clock import Clock
from kivy.uix.widget import Widget
import threading
import subprocess
import sys
import os
import webbrowser
import inspect
import win32com.client

# Add KivyMD imports
from kivymd.app import MDApp
from kivymd.uix.button import MDRaisedButton, MDFlat<PERSON>utton, MDIconButton, MDRectangleFlatButton
from kivymd.uix.dialog import MDDialog
from kivymd.theming import ThemeManager
from kivymd.icon_definitions import md_icons

# Add these imports at the top of your file if not already present
import os
import datetime
import math
import numpy as np
import pandas as pd
from kivy.app import App
from kivy.uix.popup import Popup
from kivy.uix.button import Button
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.label import Label
from kivy.uix.textinput import TextInput
from kivy.uix.spinner import Spinner
from kivy.uix.filechooser import FileChooserListView, FileChooserIconView
from plyer import filechooser  # Use plyer for file dialogs

# Create a simple DatePicker class
class DatePicker(BoxLayout):
    """Simple date picker widget."""
    def __init__(self, **kwargs):
        # Call BoxLayout's __init__ with the kwargs
        super(DatePicker, self).__init__(**kwargs)
        self.orientation = 'horizontal'
        self.spacing = 2
        
        # Create dropdown for month (1-12)
        self.month_spinner = Spinner(
            text="01",
            values=[f"{i:02d}" for i in range(1, 13)],
            size_hint=(None, None),
            size=(70, 30)
        )
        
        # Create dropdown for day (1-31)
        self.day_spinner = Spinner(
            text="01",
            values=[f"{i:02d}" for i in range(1, 32)],
            size_hint=(None, None),
            size=(70, 30)
        )
        
        # Create dropdown for year (current year and previous 5 years)
        current_year = datetime.datetime.now().year
        self.year_spinner = Spinner(
            text=str(current_year),
            values=[str(year) for year in range(current_year-5, current_year+1)],
            size_hint=(None, None),
            size=(70, 30)
        )
        
        # Add widgets to layout
        self.add_widget(self.month_spinner)
        self.add_widget(self.day_spinner)
        self.add_widget(self.year_spinner)
    
    def get_date(self):
        """Get the selected date in MM/DD/YYYY format."""
        return f"{self.month_spinner.text}/{self.day_spinner.text}/{self.year_spinner.text}"
    
    def set_date(self, date):
        """Set the date from a datetime object."""
        if isinstance(date, datetime.datetime) or isinstance(date, pd.Timestamp):
            self.month_spinner.text = f"{date.month:02d}"
            self.day_spinner.text = f"{date.day:02d}"
            self.year_spinner.text = str(date.year)
        elif isinstance(date, str):
            # Try to parse the string as a date
            try:
                date_obj = datetime.datetime.strptime(date, "%m/%d/%Y")
                self.month_spinner.text = f"{date_obj.month:02d}"
                self.day_spinner.text = f"{date_obj.day:02d}"
                self.year_spinner.text = str(date_obj.year)
            except ValueError:
                print(f"Invalid date string format: {date}")
        else:
            print(f"Unsupported date type: {type(date)}")

class CustomSpinnerOption(SpinnerOption):
    def __init__(self, **kwargs):
        super(CustomSpinnerOption, self).__init__(**kwargs)
        self.background_color = (0.3, 0.5, 0.7, 1)  # Slightly lighter blue for options
        self.background_normal = ''
        self.color = (1, 1, 1, 1)  # White text
        self.font_size = '16sp'
        self.height = 44  # Taller option items
        self.border = [15, 15, 15, 15]  # Curved borders for options

class QualityApp(MDApp):
    def __init__(self, **kwargs):
        super(QualityApp, self).__init__(**kwargs)
        # Set theme colors
        self.theme_cls.primary_palette = "Blue"
        self.theme_cls.accent_palette = "Teal"
        self.theme_cls.theme_style = "Light"
        
        self.distracting_count = 0
        self.destructive_count = 0
        self.critical_count = 0
        self.overall_error_count = 0
        self.entries = []
        self.processes = []
        self.sub_processes = {}  # Cache for sub-processes by process
        self.quality_checks_cache = {}  # Cache for quality checks by process and sub-process
        self.existing_audit_id = None  # To track if we're editing an existing audit
        
        # Task randomizer variables
        self.randomizer_df = None
        self.randomizer_final_export = None
        self.randomizer_date_filtered_df = None
        
        # Set custom icon if it exists
        icon_path = os.path.join(os.path.expanduser("~"), "Desktop", "Icon.ico")
        if os.path.exists(icon_path):
            self.icon = icon_path
        
        # Create a connection pool or reusable connection
        self.conn_str = (
            r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};"
            r"DBQ=\\ant\dept-in\Digiflex\DF-Cataloghealth\Quality Products\Audits\Workflow\Workflow database.accdb;"
        )
        # Preload processes in a background thread after initialization
        Clock.schedule_once(lambda dt: threading.Thread(target=self.preload_processes, daemon=True).start(), 1)

    def preload_processes(self):
        try:
            with pyodbc.connect(self.conn_str) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT DISTINCT Process FROM Workflow")
                processes = cursor.fetchall()
                self.processes = [row[0] for row in processes]
                
                # Preload sub-processes for each process
                for process in self.processes:
                    cursor.execute("SELECT DISTINCT [Sub Process] FROM Workflow WHERE Process = ?", (process,))
                    sub_processes = cursor.fetchall()
                    self.sub_processes[process] = [row[0] for row in sub_processes]
                    
                # Update UI in the main thread
                Clock.schedule_once(lambda dt: self.update_process_spinner(), 0)
        except Exception as e:
            print(f"Error preloading data: {e}")

    def update_process_spinner(self):
        if hasattr(self, 'process_spinner'):
            self.process_spinner.values = self.processes

    def build(self):
        self.title = "Quality Monitoring Tool"
        Window.size = (1000, 100)
        Window.maximize()
        root = FloatLayout()

        # Background Image with improved quality
        background_image_path = os.path.join(os.path.expanduser("~"), "Desktop", "Background Image.png")
        background = Image(
            source=background_image_path, 
            allow_stretch=True, 
            keep_ratio=False,
            mipmap=True  # Enable mipmapping for better quality when scaled
        )
        # Set the image to be behind other widgets
        background.opacity = 1.0  # Full opacity
        root.add_widget(background)

        # Info Button
        info_button = MDIconButton(
            icon="information",
            pos_hint={'x': 0.01, 'top': 0.98},
            theme_text_color="Custom",
            text_color=(1, 1, 1, 1),
            md_bg_color=(0.2, 0.4, 0.6, 1)
        )
        info_button.bind(on_press=self.show_info_popup)
        root.add_widget(info_button)
        
        # Quality Scores Button
        quality_scores_button = MDRaisedButton(
            text="Quality Scores",
            pos_hint={'right': 0.99, 'top': 0.98},
            size_hint=(None, None),
            width=150,
            height=40,
            font_style="Button",
            text_color=(1, 1, 1, 1),
            md_bg_color=(0.2, 0.4, 0.6, 1)
        )
        quality_scores_button.bind(on_press=self.show_quality_scores)
        root.add_widget(quality_scores_button)
        
        # Task Randomizer Button
        task_randomizer_button = MDRaisedButton(
            text="Task Randomizer",
            pos_hint={'center_x': 0.5, 'top': 0.2},  # Centered horizontally, positioned above spinners
            size_hint=(None, None),
            width=300,  # Increased from 200 to 300
            height=80,  # Increased from 60 to 80
            font_style="Button",
            text_color=(1, 1, 1, 1),
            md_bg_color=(0.2, 0.6, 0.4, 1)
        )
        task_randomizer_button.bind(on_press=self.launch_task_randomizer)
        root.add_widget(task_randomizer_button)  # Add the button to the root layout
        
        # Removed Quick Links button from main window
        
        layout = BoxLayout(orientation='horizontal', padding=5, spacing=5,
                           size_hint=(None, None), size=(1000, 100),
                           pos_hint={'center_x': 0.49, 'center_y': 0.10})

        # Process Spinner
        process_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=40, spacing=8)
        process_label = Label(text="Process:", size_hint=(None, 1), width=140, halign='center', bold=True)

        # Enhanced premium style for Process spinner
        self.process_spinner = Spinner(
            text='Select Process',
            values=[],
            size_hint=(None, 1),
            width=300,
            background_color=(0.2, 0.4, 0.6, 1),  # Deep blue background
            background_normal='',
            color=(1, 1, 1, 1),  # White text
            bold=True,
            font_size='16sp',
            option_cls=CustomSpinnerOption,  # Use custom option class
            border=[15, 15, 15, 15]  # Curved borders for main spinner
        )
        self.process_spinner.bind(text=self.on_process_selected)

        process_layout.add_widget(process_label)
        process_layout.add_widget(self.process_spinner)

        # Sub-Process Spinner
        sub_process_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=40, spacing=8)
        sub_process_label = Label(text="Sub Process:", size_hint=(None, 1), width=140, halign='center', bold=True)

        # Enhanced premium style for Sub Process spinner
        self.sub_process_spinner = Spinner(
            text='Select Workflow',
            values=[],
            size_hint=(None, 1),
            width=400,
            background_color=(0.2, 0.4, 0.6, 1),  # Deep blue background
            background_normal='',
            color=(1, 1, 1, 1),  # White text
            bold=True,
            font_size='16sp',
            option_cls=CustomSpinnerOption,  # Use custom option class
            border=[15, 15, 15, 15]  # Curved borders for main spinner
        )
        self.sub_process_spinner.bind(text=self.on_sub_process_selected)

        sub_process_layout.add_widget(sub_process_label)
        sub_process_layout.add_widget(self.sub_process_spinner)

        layout.add_widget(process_layout)
        layout.add_widget(sub_process_layout)
        root.add_widget(layout)

        # Load processes from the database
        self.load_processes()

        return root

    def on_process_selected(self, spinner, text):
        # Check if sub-processes for this process are already cached
        if text in self.sub_processes:
            self.sub_process_spinner.values = self.sub_processes[text]
            self.sub_process_spinner.text = 'Select Workflow'
            return
        
        # Otherwise load from database
        self.load_sub_processes(text)

    def on_sub_process_selected(self, spinner, text):
        # Only open the quality tab if a valid sub-process is selected
        if text != 'Select Workflow' and text in self.sub_process_spinner.values:
            self.open_quality_tab(self.process_spinner.text, text)

    def load_processes(self):
        # If processes are already loaded, use them
        if self.processes:
            self.process_spinner.values = self.processes
            return
        
        # Otherwise load from database
        try:
            with pyodbc.connect(self.conn_str) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT DISTINCT Process FROM Workflow")
                processes = cursor.fetchall()
                self.processes = [row[0] for row in processes]
                self.process_spinner.values = self.processes
        except Exception as e:
            self.show_error("Database Error", f"An error occurred while loading processes: {str(e)}")

    def load_sub_processes(self, value):
        try:
            with pyodbc.connect(self.conn_str) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT DISTINCT [Sub Process] FROM Workflow WHERE Process = ?", (value,))
                sub_processes = cursor.fetchall()
                if sub_processes:
                    self.sub_processes[value] = [row[0] for row in sub_processes]
                    self.sub_process_spinner.values = self.sub_processes[value]
                    self.sub_process_spinner.text = 'Select Workflow'
                else:
                    self.sub_processes[value] = []
                    self.sub_process_spinner.values = []
                    self.sub_process_spinner.text = 'Select Workflow'
        except Exception as e:
            self.show_error("Database Error", f"An error occurred while loading sub processes: {str(e)}")

    # The following methods remain unchanged
    
    def open_quality_tab(self, process, sub_process):
        # Check if quality checks for this process and sub-process are already cached
        cache_key = f"{process}_{sub_process}"
        if cache_key in self.quality_checks_cache:
            self.show_quality_checks(self.quality_checks_cache[cache_key], process, sub_process)
            return
        
        # Otherwise load from database
        try:
            # Print diagnostic information
            print(f"Attempting to fetch quality checks for Process: '{process}', Sub Process: '{sub_process}'")
            
            # Create a new connection for this query
            conn_str = (
                r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};"
                r"DBQ=\\ant\dept-in\Digiflex\DF-Cataloghealth\Quality Products\Audits\Workflow\Workflow database.accdb;"
            )
            
            # Try with explicit connection and cursor management
            conn = pyodbc.connect(conn_str)
            cursor = conn.cursor()
            
            # Construct and print the query for debugging
            query = "SELECT [Checks Category], [Audit Checklist], [Field], [Error Category], [Score] FROM Workflow WHERE [Process] = ? AND [Sub Process] = ?"
            print(f"Executing query: {query} with parameters: {process}, {sub_process}")
            
            # Execute the query
            cursor.execute(query, (process, sub_process))
            
            # Fetch all results
            quality_checks = cursor.fetchall()
            print(f"Fetched {len(quality_checks)} quality checks")
            
            # Debug print the scores to verify they're coming in correctly
            for check in quality_checks:
                print(f"Score from DB: {check[4]}, type: {type(check[4])}")
            
            # Close cursor and connection
            cursor.close()
            conn.close()

            if not quality_checks:
                self.show_error("No Data", "No quality checks found for the selected Process and Sub Process.")
                return

            # Cache the quality checks for future use
            self.quality_checks_cache[cache_key] = quality_checks
            self.show_quality_checks(quality_checks, process, sub_process)
        except pyodbc.Error as e:
            error_msg = f"Database Error: {e}"
            print(error_msg)
            self.show_error("Database Error", f"An error occurred while fetching quality checks: {str(e)}")
        except Exception as e:
            error_msg = f"Unexpected Error: {e}"
            print(error_msg)
            self.show_error("Unexpected Error", f"An unexpected error occurred: {str(e)}")

    def show_quality_checks(self, quality_checks, process, sub_process):
        
        root_layout = BoxLayout(orientation='vertical', padding=10, spacing=10)
        
        # Create a top container for the back button and other widgets
        top_container = BoxLayout(size_hint_y=None, height=50, spacing=20)  # Increased spacing from 10 to 20
        
        # Add an arrow button to go back to the main window
        back_button = MDIconButton(
            icon="arrow-left",
            size_hint=(None, None),
            size=(40, 40),
            theme_text_color="Custom",
            text_color=(1, 1, 1, 1)  # White color
        )
        back_button.bind(on_press=self.close_quality_tab)
        top_container.add_widget(back_button)
        
        # Add the input fields and buttons next to the back button with padding
        self.vcid_input = TextInput(hint_text="VCID/GTI/URN/TT", multiline=False, size_hint_x=None, width=180, padding=[10, 5], write_tab=False)
        self.user_input = TextInput(hint_text="User ID", multiline=False, size_hint_x=None, width=100, padding=[10, 5], write_tab=False)
        self.week_input = TextInput(hint_text="Week", multiline=False, size_hint_x=None, width=80, padding=[10, 5], write_tab=False)
        self.op_qced_date_input = TextInput(hint_text="Op Qced Date (M/D/YYYY)", multiline=False, size_hint_x=None, width=180, padding=[10, 5], write_tab=False)
        self.quality_label = Label(text="Quality %: N/A", size_hint_x=None, width=180)

        # Add RE-Audits button
        reaudit_button = MDRaisedButton(
            text="Reaudit",
            size_hint_x=None,
            width=80,
            height=30
        )
        reaudit_button.bind(on_press=lambda x: self.load_existing_audit(process, sub_process))
        
        # Add Quick Links dropdown button
        quick_links_button = MDRaisedButton(
            text="Quick Links",
            size_hint_x=None,
            width=120,
            height=30
        )
        quick_links_button.bind(on_press=self.show_quick_links_dropdown)
        
        submit_button = MDRaisedButton(
            text="Submit",
            size_hint_x=None,
            width=80,
            height=30
        )
        submit_button.bind(on_press=lambda x: self.submit_to_db(process, sub_process))

        top_container.add_widget(self.week_input)
        top_container.add_widget(self.user_input)
        top_container.add_widget(self.vcid_input)
        top_container.add_widget(self.op_qced_date_input)
        top_container.add_widget(self.quality_label)
        top_container.add_widget(reaudit_button)
        top_container.add_widget(quick_links_button)  # Add the Quick Links button
        top_container.add_widget(submit_button)
        
        # Add the top container to the root layout
        root_layout.add_widget(top_container)

        # Move Pass/Fail buttons to the top container
        pass_all_btn = MDRectangleFlatButton(
            text="Pass",
            size_hint_x=None,
            width=80,
            height=30,
            text_color=(0.2, 0.7, 0.2, 1),
            line_color=(0.2, 0.7, 0.2, 1)
        )
        fail_all_btn = MDRectangleFlatButton(
            text="Fail",
            size_hint_x=None,
            width=80,
            height=30,
            text_color=(0.7, 0.2, 0.2, 1),
            line_color=(0.7, 0.2, 0.2, 1)
        )
        
        reset_btn = MDRectangleFlatButton(
            text="Reset",
            size_hint_x=None,
            width=80,
            height=30,
            text_color=(0.5, 0.5, 0.5, 1),
            line_color=(0.5, 0.5, 0.5, 1)
        )
        
        pass_all_btn.bind(on_press=lambda x: self.fill_all("pass"))
        fail_all_btn.bind(on_press=lambda x: self.fill_all("fail"))
        reset_btn.bind(on_press=lambda x: self.reset_all())
        
        top_container.add_widget(pass_all_btn)
        top_container.add_widget(fail_all_btn)
        top_container.add_widget(reset_btn)

        # Create a container for the table with horizontal scrolling
        table_container = BoxLayout(orientation='vertical', size_hint=(1, 1))
        
        # Define column widths - even wider to use more space
        col_widths = [350, 500, 350, 250, 150]  # Further increased widths
        
        # Table headers
        header_grid = GridLayout(cols=5, size_hint_y=None, height=50, spacing=5)  # Increased height and spacing
        
        for i, header in enumerate(["Checks Category", "Audit Checklist", "Field", "Error Category", "Result (Pass/Fail)"]):
            header_layout = BoxLayout(size_hint_x=None, width=col_widths[i])
            lbl = Label(
                text=header, 
                bold=True, 
                halign='center', 
                valign='middle',
                font_size='16sp'  # Larger font for headers
            )
            lbl.bind(size=lbl.setter('text_size'))
            header_layout.add_widget(lbl)
            header_grid.add_widget(header_layout)
        
        table_container.add_widget(header_grid)

        # Scrollable table with quality checks
        scroll_view = ScrollView(size_hint=(1, 1), do_scroll_x=True, do_scroll_y=True, bar_width=12)  # Wider scrollbar
        
        # Main grid for all rows
        main_grid = GridLayout(cols=1, size_hint_y=None, spacing=15)  # Increased spacing between rows
        main_grid.bind(minimum_height=main_grid.setter('height'))
        
        # Set minimum width based on sum of column widths plus spacing
        total_width = sum(col_widths) + (len(col_widths) * 5)  # Add more spacing
        main_grid.size_hint_x = None
        main_grid.width = total_width
        
        self.entries = []  # Store tuples of (entry_widget, score, error_category)
        print(f"Quality Checks: {quality_checks}")

        for checks_category, audit_checklist, field, error_category, score in quality_checks:
            print(f"Check Category: {checks_category}")

            # Create a row for each quality check
            row_grid = GridLayout(cols=5, size_hint_y=None, height=80, spacing=5)  # Increased height and spacing
            row_grid.size_hint_x = None
            row_grid.width = total_width
            
            # Add each cell with fixed width
            cat_layout = BoxLayout(size_hint_x=None, width=col_widths[0], padding=(5, 5))
            cat_label = Label(
                text=checks_category, 
                halign='center', 
                valign='middle',
                size_hint=(1, 1),  # Fill the entire layout
                font_size='14sp'
            )
            # Set text_size to the layout size to enable proper wrapping and alignment
            cat_label.text_size = (col_widths[0]-20, None)
            cat_layout.add_widget(cat_label)
            row_grid.add_widget(cat_layout)

            audit_layout = BoxLayout(size_hint_x=None, width=col_widths[1], padding=(5, 5))
            audit_label = Label(
                text=audit_checklist, 
                halign='center', 
                valign='middle',
                size_hint=(1, 1),  # Fill the entire layout
                font_size='14sp'
            )
            # Set text_size to the layout size to enable proper wrapping and alignment
            audit_label.text_size = (col_widths[1]-20, None)
            audit_layout.add_widget(audit_label)
            row_grid.add_widget(audit_layout)

            field_layout = BoxLayout(size_hint_x=None, width=col_widths[2], padding=(5, 5))
            field_label = Label(
                text=field, 
                halign='center', 
                valign='middle',
                size_hint=(1, 1),  # Fill the entire layout
                font_size='14sp'
            )
            # Set text_size to the layout size to enable proper wrapping and alignment
            field_label.text_size = (col_widths[2]-20, None)
            field_layout.add_widget(field_label)
            row_grid.add_widget(field_layout)

            error_layout = BoxLayout(size_hint_x=None, width=col_widths[3], padding=(5, 5))
            error_label = Label(
                text=error_category, 
                halign='center', 
                valign='middle',
                size_hint=(1, 1),  # Fill the entire layout
                font_size='14sp'
            )
            # Set text_size to the layout size to enable proper wrapping and alignment
            error_label.text_size = (col_widths[3]-20, None)
            error_layout.add_widget(error_label)
            row_grid.add_widget(error_layout)
            
            # Result input
            result_layout = BoxLayout(size_hint_x=None, width=col_widths[4], padding=(10, 15))
            entry = TextInput(
                multiline=False, 
                size_hint=(1, 0.7), 
                halign='center',
                font_size='14sp',
                write_tab=False
            )
            entry.bind(text=self.on_entry_text_change)
            result_layout.add_widget(entry)
            row_grid.add_widget(result_layout)
            
            # Save entry + score (used only in calculation)
            self.entries.append((entry, score, error_category))
            
            # Add the row to the main grid
            main_grid.add_widget(row_grid)
        
        scroll_view.add_widget(main_grid)
        table_container.add_widget(scroll_view)
        root_layout.add_widget(table_container)

        # Error Count Display
        error_count_layout = BoxLayout(size_hint_y=None, height=40, spacing=10, padding=(10, 0))
        self.distracting_count_label = Label(text=f"Distracting: {self.distracting_count}", size_hint_x=None, width=150)
        self.destructive_count_label = Label(text=f"Destructive: {self.destructive_count}", size_hint_x=None, width=150)
        self.critical_count_label = Label(text=f"Critical: {self.critical_count}", size_hint_x=None, width=150)
        self.overall_error_count_label = Label(text=f"Overall Error: {self.overall_error_count}", size_hint_x=None, width=150)
        
        # Add Checks text input after the overall error count label
        self.checks_input = TextInput(hint_text="Checks", multiline=False, size_hint_x=100, width=150, write_tab=False)
        self.modified_checks_input = TextInput(hint_text="Reaudit Checks", multiline=False, size_hint_x=100, width=150, write_tab=False)

        error_count_layout.add_widget(self.distracting_count_label)
        error_count_layout.add_widget(self.destructive_count_label)
        error_count_layout.add_widget(self.critical_count_label)
        error_count_layout.add_widget(self.overall_error_count_label)
        error_count_layout.add_widget(self.checks_input)
        error_count_layout.add_widget(self.modified_checks_input)  # Add Checks text input after the overall error count label   

        root_layout.add_widget(error_count_layout)

        # Buttons + Feedback Entry
        button_row = BoxLayout(size_hint_y=None, height=70, spacing=20, padding=(10, 5))
        feedback_label = Label(text="Feedback:", size_hint=(None, None), size=(80, 30))
        self.feedback_input = TextInput(hint_text="Enter feedback", multiline=False, size_hint=(1, None), height=50, write_tab=False)

        button_row.add_widget(feedback_label)
        button_row.add_widget(self.feedback_input)

        root_layout.add_widget(button_row)

        self.popup = Popup(
            title=f"{process} - {sub_process}",
            content=root_layout,
            size_hint=(0.95, 0.95),
            size=(1600, 900)  # Even wider popup
        )
        self.popup.open()

    def close_quality_tab(self, instance):
        self.popup.dismiss()  # Close the quality tab popup
        # Reset the process and sub-process spinners
        self.process_spinner.text = 'Select Process'
        self.sub_process_spinner.text = 'Select Workflow'
        self.sub_process_spinner.values = []  # Clear sub-process values
        
        # Make sure the spinners are enabled and can be interacted with
        self.process_spinner.disabled = False
        self.sub_process_spinner.disabled = False
        
        # Rebind the spinner events to ensure they work properly
        self.process_spinner.bind(text=self.on_process_selected)
        self.sub_process_spinner.bind(text=self.on_sub_process_selected)
        
        # Reload the processes to ensure the spinner has valid values
        self.load_processes()

    def fill_all(self, value):
        for entry, _, _ in self.entries:
            entry.text = value.capitalize()
            # Color will be applied by the on_entry_text_change binding

    def reset_all(self):
        for entry, _, _ in self.entries:
            entry.text = ""

    def calculate_quality(self, quality_checks):
        # Initialize counters
        self.distracting_count = 0
        self.destructive_count = 0
        self.critical_count = 0
        
        # Calculate total possible score from all checks
        total_possible_score = sum(float(score) for _, score, _ in self.entries)
        
        # Initialize actual score
        actual_score = 0
        critical_fail = False
        
        # Process each entry
        for entry, score, category in self.entries:
            val = entry.text.strip().lower()
            score_float = float(score)
            
            if val == "pass" or val == "na":  # Add NA handling here
                # Add the score for passed checks or NA entries
                actual_score += score_float
            elif val == "fail":
                # Count failures by category
                cat_lower = category.lower()
                if "critical" in cat_lower:
                    critical_fail = True
                    self.critical_count += 1
                elif "destructive" in cat_lower:
                    self.destructive_count += 1
                elif "distracting" in cat_lower:
                    self.distracting_count += 1
                # For failures, we don't add to the score (effectively subtracting from total)
        
        # Calculate quality percentage
        if critical_fail:
            quality_percentage = 0.00
        else:
            quality_percentage = (actual_score / total_possible_score) * 100 if total_possible_score > 0 else 0
        
        # Update the quality label
        self.quality_label.text = f"Quality %: {quality_percentage:.2f}"
        
        # Update error counts in labels
        self.distracting_count_label.text = f"Distracting: {self.distracting_count}"
        self.destructive_count_label.text = f"Destructive: {self.destructive_count}"
        self.critical_count_label.text = f"Critical: {self.critical_count}"
        self.overall_error_count = self.distracting_count + self.destructive_count + self.critical_count
        self.overall_error_count_label.text = f"Overall Error: {self.overall_error_count}"

    def submit_to_db(self, process, subprocess):
        vcid_gti = self.vcid_input.text.strip()
        user_id = self.user_input.text.strip()
        week = self.week_input.text.strip()
        op_qced_date = self.op_qced_date_input.text.strip()
        distracting = self.distracting_count
        destructive = self.destructive_count
        critical = self.critical_count
        overall_error = self.overall_error_count
        feedback = self.feedback_input.text.strip()
        quality = self.quality_label.text.split(": ")[1]
        auditor_id = getpass.getuser()  # Get the current system user ID
        
        # Get the re-audit reason if available
        reaudit_reason = getattr(self, 'reaudit_reason', '') if hasattr(self, 'reaudit_mode') and hasattr(self, 'reaudit_reason') else ''
        
        # Get the modified checks from the text input
        modified_checks = self.modified_checks_input.text.strip()  # Get the modified checks from the text input
        
        # Collect failed checks information for the original Checks column
        failed_checks = []
        for i, (entry, _, category) in enumerate(self.entries):
            if entry.text.strip().lower() == "fail":
                # Get the corresponding audit checklist from the quality checks
                cache_key = f"{process}_{subprocess}"
                if cache_key in self.quality_checks_cache and i < len(self.quality_checks_cache[cache_key]):
                    quality_check = self.quality_checks_cache[cache_key][i]
                    check_name = quality_check[1]  # Audit Checklist is at index 1
                    failed_checks.append(f"{category} - {check_name}")
        
        # Format failed checks as a numbered list
        checks_details = ""
        for i, check in enumerate(failed_checks, 1):
            checks_details += f"{i}. {check}\n"

        print(f"Failed checks to be saved: {checks_details}")
        
        # Get current date in M/D/YYYY format for Audit Qc Date and Modified Date
        current_date = datetime.datetime.now().strftime("%m/%d/%Y")

        if not (vcid_gti and user_id and week):
            self.show_error("Input Error", "Fill all required fields.")
            return

        # Validate date format (M/D/YYYY)
        if op_qced_date and not re.match(r'^(1[0-2]|0?[1-9])/(3[01]|[12][0-9]|0?[1-9])/\d{4}$', op_qced_date):
            self.show_error("Input Error", "Op Qc'ed Date must be in M/D/YYYY format.")
            return

        if quality == "N/A":
            self.show_error("Calculation Error", "Calculate quality before submitting.")
            return
        
        # Make sure error counts are calculated before submitting
        if hasattr(self, 'entries') and self.entries:
            quality_checks = [(None, None, None, cat, score) for _, score, cat in self.entries]
            self.calculate_quality(quality_checks)

        try:
            conn_str = (
                r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};"
                r"DBQ=\\ant\\dept-in\\Digiflex\\DF-Cataloghealth\\Quality Products\\Audits\\QP-&-Tentpole-Audits\\Audits(Science Support).accdb;"
            )
            with pyodbc.connect(conn_str) as conn:
                cursor = conn.cursor()
                # Get all table names from the database
                tables = [table[2] for table in cursor.tables(tableType='TABLE')]
                
                # Map subprocess to table name by finding the closest match
                table_mapping = {}
                for table in tables:
                    # Remove brackets and 'Audits' suffix for comparison
                    clean_table = table.replace('[', '').replace(']', '')
                    if 'Audits' in clean_table:
                        clean_table = clean_table.replace(' Audits', '')
                    table_mapping[clean_table] = table
                
                # Find the best match for the subprocess
                table_name = table_mapping.get(subprocess)

                # If no exact match, try partial matching with full table name
                if not table_name:
                    # First try to find if sub_process is contained within any table name
                    for key, value in table_mapping.items():
                        if subprocess.lower() in key.lower():
                            table_name = value
                            break
                    
                    # If still not found, try if any table name is contained in sub_process
                    if not table_name:
                        for key, value in table_mapping.items():
                            if key.lower() in subprocess.lower():
                                table_name = value
                                break

                if table_name is None:
                    self.show_error("Input Error", "Invalid subprocess selected.")
                    return

                # Ensure table name is properly formatted with square brackets
                if not (table_name.startswith('[') and table_name.endswith(']')):
                    table_name = f"[{table_name}]"

                # Check if we're updating an existing record
                if hasattr(self, 'existing_audit_id') and self.existing_audit_id:
                    # First, retrieve the existing record to get the original Checks and Audit Qc Date
                    query = f"SELECT [Checks], [Audit Qc Date] FROM {table_name} WHERE ID = ?"
                    cursor.execute(query, (self.existing_audit_id,))
                    result = cursor.fetchone()
                    original_checks = result[0] if result else ""
                    original_audit_qc_date = result[1] if result else ""
                    
                    # Update existing record - preserve original Checks and Audit Qc Date, only update Modified Date
                    query = f"""UPDATE {table_name} SET 
                        [VCID/GTI/URN/TT] = ?, [User ID] = ?, [Week] = ?, [Sub-Process] = ?, [Quality %] = ?, 
                        [Auditor ID] = ?, [Distracting] = ?, [Destructive] = ?, [Critical] = ?, 
                        [Overall Error] = ?, [Feedback] = ?, [Op Qced Date] = ?, [Audit Qc Date] = ?,
                        [Modified Date] = ?, [Modified Auditor ID] = ?, [Reaudit Checks] = ?, [Reason] = ?
                        WHERE ID = ?"""

                    cursor.execute(query, 
                        (vcid_gti, user_id, week, subprocess, quality, auditor_id, 
                        distracting, destructive, critical, overall_error, feedback, 
                        op_qced_date, original_audit_qc_date, current_date, auditor_id, modified_checks, 
                        reaudit_reason, self.existing_audit_id)
                    )
                    
                    message = f"Updated existing audit for VCID/GTI/URN/TT: {vcid_gti}"
                    # Clear the existing audit ID after update
                    self.existing_audit_id = None
                else:
                    # Check if VCID/GTI already exists in the database
                    query = f"SELECT COUNT(*) FROM {table_name} WHERE [VCID/GTI/URN/TT] = ?"
                    cursor.execute(query, (vcid_gti,))
                    count = cursor.fetchone()[0]
                    
                    if count > 0 and not hasattr(self, 'reaudit_mode'):
                        self.show_error("Duplicate Entry", f"VCID/GTI/URN/TT: {vcid_gti} already exists in the database.")
                        return
                    
                    # Insert new record
                    query = f"""INSERT INTO {table_name}
                        ([VCID/GTI/URN/TT], [User ID], [Week], [Sub-Process], [Quality %], [Auditor ID], 
                        [Distracting], [Destructive], [Critical], [Overall Error], [Feedback], 
                        [Op Qced Date], [Audit Qc Date], [Modified Date], [Modified Auditor ID], [Checks], [Reaudit Checks], [Reason])
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"""
                    
                    cursor.execute(query, 
                        (vcid_gti, user_id, week, subprocess, quality, auditor_id, 
                        distracting, destructive, critical, overall_error, feedback, 
                        op_qced_date, current_date, current_date, auditor_id, checks_details, modified_checks, reaudit_reason)
                    )
                    
                    message = f"Submitted successfully for Week {week}"
                
                conn.commit()
                self.show_info("Success", message)
                
                # Email notification logic:
                # 1. In reaudit mode: Always send email regardless of error count
                # 2. In regular mode: Only send email if there are errors (overall_error > 0)
                if hasattr(self, 'reaudit_mode') or overall_error > 0:
                    # Use modified_checks for reaudit mode, checks_details for regular mode
                    email_checks = modified_checks if hasattr(self, 'reaudit_mode') else checks_details
                    self.send_email_notification(vcid_gti, user_id, week, op_qced_date, current_date, 
                                                auditor_id, distracting, destructive, critical, 
                                                overall_error, quality, feedback, email_checks)
                
                # Reset input fields and reaudit mode flag
                self.reset_input_fields()
                if hasattr(self, 'reaudit_mode'):
                    delattr(self, 'reaudit_mode')

        except Exception as e:
            import traceback
            traceback.print_exc()  # Print the full error traceback for debugging
            self.show_error("Database Error", f"An error occurred: {str(e)}")

    def reset_input_fields(self):
        self.vcid_input.text = ""
        self.user_input.text = ""
        self.week_input.text = ""
        self.op_qced_date_input.text = ""  # Reset Op Qc'ed Date field
        self.quality_label.text = "Quality %: N/A"   
        self.feedback_input.text = ""
        self.checks_input.text = ""
        self.modified_checks_input.text = ""  # Reset the checks input field
        
        # Reset all entry fields
        for entry, _, _ in self.entries:
            entry.text = ""
        
        # Reset error counts
        self.distracting_count = 0
        self.destructive_count = 0
        self.critical_count = 0
        self.overall_error_count = 0
        
        # Update error count labels
        self.distracting_count_label.text = f"Distracting: {self.distracting_count}"
        self.destructive_count_label.text = f"Destructive: {self.destructive_count}"
        self.critical_count_label.text = f"Critical: {self.critical_count}"
        self.overall_error_count_label.text = f"Overall Error: {self.overall_error_count}"
        
        # Clear existing audit ID
        self.existing_audit_id = None
        
        # Clear re-audit reason if it exists
        if hasattr(self, 'reaudit_reason'):
            delattr(self, 'reaudit_reason')
        
        # Clear re-audit mode flag if it exists
        if hasattr(self, 'reaudit_mode'):
            delattr(self, 'reaudit_mode')

    def show_error(self, title, message):
        """Show an error popup with the given title and message."""
        dialog = MDDialog(
            title=title,
            text=message,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

    def show_info(self, title, message):
        """Show an information popup with the given title and message."""
        dialog = MDDialog(
            title=title,
            text=message,
            buttons=[
                MDFlatButton(
                    text="OK",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

    def show_info_popup(self, instance):
        info_text = (
            "1. Quality checks are bucketized into four categories (Validation, Accuracy Of Information, Documentation, Action Taken), with defects classified as either Critical or Non-Critical (Distracting and Destructive):\n\n"
            "     Distracting – 10% weightage\n"
            "     [color=#FFFF00]Destructive[/color] – 30% weightage\n"
            "     [color=#e74c3c]Critical[/color] – 60% weightage\n\n"
            "2. Each process will have different number of checks across categories. Weightage calculation example (for TV Series process): 33 total checks: 10 (Distracting), 20 [color=#FFFF00](Destructive)[/color], 3 [color=#e74c3c](Critical)[/color].\n\n"
            "3. Weightage calculated as (Category Weightage / Number Of Checks) - 30 (Destructive) / 20 checks = 1.5 for each check. Similarly, weightage can be calculated for other checks.\n\n"
            "4. The audit mechanism allows auditors to mark each check as [color=#90ee90]“Pass”[/color] or [color=#e74c3c]“Fail”[/color] similar to validation performed by associates.\n\n"
            "5. If a title critical quality check was failed, it would be termed as fatal and the overall quality score will be zero at title level.\n\n"
            "6. Non-critical fails will result in score deduction based on predefined weightage.\n\n"
            "7. 10% of samples should be audited for each associate in a week."
        )

        content = BoxLayout(orientation='vertical')
        scroll = ScrollView(size_hint=(1, 1))

        label = Label(
            text=info_text,
            markup=True,
            size_hint_y=1,
            text_size=(980, None),
            halign='left',
            valign='top'
        )
        label.bind(texture_size=lambda instance, value: setattr(label, 'height', value[1]))

        scroll.add_widget(label)
        content.add_widget(scroll)

        popup = Popup(
            title="Quality Checks Info",
            content=content,
            size_hint=(None, None),
            size=(1020, 700)
        )
        popup.open()

    def on_entry_text_change(self, instance, value):
        value = value.strip().lower()
        if value == "pass":
            instance.background_color = (0.8, 1, 0.8, 1)  # Light green
        elif value == "fail":
            instance.background_color = (1, 0.8, 0.8, 1)  # Light red
        else:
            instance.background_color = (1, 1, 1, 1)  # White (default)
            
        # Automatically calculate quality whenever any entry changes
        if hasattr(self, 'entries') and self.entries:
            # Extract just the scores and categories from entries for recalculation
            quality_checks = [(None, None, None, cat, score) for _, score, cat in self.entries]
            self.calculate_quality(quality_checks)

    def test_database_connection(self):
        try:
            conn_str = (
                r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};"
                r"DBQ=\\ant\dept-in\Digiflex\DF-Cataloghealth\Quality Products\Audits\Workflow\Workflow database.accdb;"
            )
            conn = pyodbc.connect(conn_str)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM Workflow")
            count = cursor.fetchone()[0]
            cursor.close()
            conn.close()
            print(f"Successfully connected to database. Found {count} records in Workflow table.")
            return True
        except Exception as e:
            print(f"Database connection test failed: {e}")
            return False

    def load_existing_audit(self, process, sub_process):
        # Create a popup to enter the reason for re-audit
        reason_popup = Popup(
            title="Reaudit Reason",
            size_hint=(None, None),
            size=(400, 300)
        )
        
        # Create layout for the popup
        layout = BoxLayout(orientation='vertical', padding=10, spacing=10)
        
        # Add label and text input for reason
        layout.add_widget(Label(text="Enter reason for re-audit:"))
        reason_input = TextInput(multiline=False, size_hint=(1, None), height=40, write_tab=False)
        layout.add_widget(reason_input)
        
        # Add buttons
        button_layout = BoxLayout(size_hint=(1, None), height=40, spacing=10)
        
        # Cancel button
        cancel_button = Button(text="Cancel")
        cancel_button.bind(on_press=reason_popup.dismiss)
        button_layout.add_widget(cancel_button)
        
        # Save button
        save_button = Button(text="Save")
        save_button.bind(on_press=lambda x: self.process_reaudit(process, sub_process, reason_input.text, reason_popup))
        button_layout.add_widget(save_button)
        
        layout.add_widget(button_layout)
        reason_popup.content = layout
        reason_popup.open()

    def populate_entries_from_checks(self, checks_details, process, sub_process):
        """Populate Pass/Fail entries based on checks details from database."""
        if not checks_details or not hasattr(self, 'entries'):
            return
        
        try:
            # Parse the checks details to extract failed check names
            failed_checks = []
            lines = str(checks_details).strip().split('\n')
            for line in lines:
                line = line.strip()
                if line and '. ' in line:
                    # Extract the part after the number and category
                    # Format: "1. Category - Check Name"
                    parts = line.split('. ', 1)
                    if len(parts) > 1:
                        check_part = parts[1]
                        # Remove category prefix (e.g., "Distracting - ")
                        if ' - ' in check_part:
                            check_name = check_part.split(' - ', 1)[1]
                            failed_checks.append(check_name.strip())
            
            # Get quality checks for comparison
            cache_key = f"{process}_{sub_process}"
            if cache_key not in self.quality_checks_cache:
                return
            
            quality_checks = self.quality_checks_cache[cache_key]
            
            # Set all entries to "Pass" first, then mark failed ones as "Fail"
            for i, (entry, _, _) in enumerate(self.entries):
                if i < len(quality_checks):
                    audit_checklist = quality_checks[i][1]  # Audit Checklist is at index 1
                    
                    # Check if this audit checklist is in the failed checks
                    is_failed = any(failed_check in audit_checklist for failed_check in failed_checks)
                    
                    entry.text = "Fail" if is_failed else "Pass"
            
        except Exception as e:
            print(f"Error populating entries from checks: {e}")

    def process_reaudit(self, process, sub_process, reason, popup):
        # Close the popup
        popup.dismiss()
        
        # Store the reason for later use when saving to database
        self.reaudit_reason = reason
        # Ensure reaudit_mode flag is set
        self.reaudit_mode = True
        
        # Continue with the existing re-audit logic
        vcid_gti = self.vcid_input.text.strip()
        
        if not vcid_gti:
            self.show_error("Input Error", "Please enter a VCID/GTI/URN/TT to search for existing audits.")
            return
        
        try:
            # Connect to the database
            conn_str = (
                r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};"
                r"DBQ=\\ant\\dept-in\\Digiflex\\DF-Cataloghealth\\Quality Products\\Audits\\QP-&-Tentpole-Audits\\Audits(Science Support).accdb;"
            )
            
            with pyodbc.connect(conn_str) as conn:
                cursor = conn.cursor()
                # Get all table names from the database
                tables = [table[2] for table in cursor.tables(tableType='TABLE')]
                
                # Map subprocess to table name by finding the closest match
                table_mapping = {}
                for table in tables:
                    # Remove brackets and 'Audits' suffix for comparison
                    clean_table = table.replace('[', '').replace(']', '')
                    if 'Audits' in clean_table:
                        clean_table = clean_table.replace(' Audits', '')
                    table_mapping[clean_table] = table
                
                # Find the best match for the sub_process
                table_name = table_mapping.get(sub_process)
                
                # If no exact match, try partial matching with full table name
                if not table_name:
                    # First try to find if sub_process is contained within any table name
                    for key, value in table_mapping.items():
                        if sub_process.lower() in key.lower():
                            table_name = value
                            break
                    
                    # If still not found, try if any table name is contained in sub_process
                    if not table_name:
                        for key, value in table_mapping.items():
                            if key.lower() in sub_process.lower():
                                table_name = value
                                break

                if table_name is None:
                    self.show_error("Input Error", "Invalid subprocess selected.")
                    return

                # Ensure table name is properly formatted with square brackets
                if not (table_name.startswith('[') and table_name.endswith(']')):
                    table_name = f"[{table_name}]"

                # Query to find the existing audit record
                query = f"""SELECT * FROM {table_name} WHERE [VCID/GTI/URN/TT] = ?"""
                cursor.execute(query, (vcid_gti,))
                
                audit_record = cursor.fetchone()
                
                if not audit_record:
                    self.show_error("Not Found", f"No existing audit found for VCID/GTI/URN/TT: {vcid_gti}")
                    return
                
                # Get column names
                column_names = [column[0] for column in cursor.description]
                
                # Create a dictionary of the record
                audit_data = {column_names[i]: audit_record[i] for i in range(len(column_names))}
                
                # Populate the form fields with the existing data - convert to string to handle numeric values
                self.user_input.text = str(audit_data.get('User ID', '')) if audit_data.get('User ID') is not None else ''
                self.week_input.text = str(audit_data.get('Week', '')) if audit_data.get('Week') is not None else ''
                
                # Handle Op Qced Date - could be a datetime object or string
                op_qced_date = audit_data.get('Op Qced Date', '')
                if op_qced_date:
                    if isinstance(op_qced_date, datetime.datetime):
                        self.op_qced_date_input.text = op_qced_date.strftime("%m/%d/%Y")
                    else:
                        self.op_qced_date_input.text = str(op_qced_date)
                else:
                    self.op_qced_date_input.text = ''
                
                # Set quality percentage - convert to string if it's a number
                quality_value = audit_data.get('Quality %', 'N/A')
                if quality_value is not None:
                    self.quality_label.text = f"Quality %: {quality_value}"
                else:
                    self.quality_label.text = "Quality %: N/A"
                
                # Set error counts - ensure they're integers
                self.distracting_count = int(audit_data.get('Distracting', 0)) if audit_data.get('Distracting') is not None else 0
                self.destructive_count = int(audit_data.get('Destructive', 0)) if audit_data.get('Destructive') is not None else 0
                self.critical_count = int(audit_data.get('Critical', 0)) if audit_data.get('Critical') is not None else 0
                self.overall_error_count = int(audit_data.get('Overall Error', 0)) if audit_data.get('Overall Error') is not None else 0
                
                # Update error count labels
                self.distracting_count_label.text = f"Distracting: {self.distracting_count}"
                self.destructive_count_label.text = f"Destructive: {self.destructive_count}"
                self.critical_count_label.text = f"Critical: {self.critical_count}"
                self.overall_error_count_label.text = f"Overall Error: {self.overall_error_count}"
                
                # Set feedback - convert to string if it's not None
                feedback = audit_data.get('Feedback', '')
                self.feedback_input.text = str(feedback) if feedback is not None else ''
                
                # Store the original record ID for updating later
                self.existing_audit_id = audit_data.get('ID', None)
                
                # Get the checks details if available
                checks_details = audit_data.get('Checks', '')
                print(f"Loaded checks details: {checks_details}")
                self.checks_input.text = str(checks_details) if checks_details is not None else ''
                
                # Populate Pass/Fail entries based on the existing audit checks
                self.populate_entries_from_checks(checks_details, process, sub_process)
                
                self.show_info("Success", f"Loaded existing audit for VCID/GTI/URN/TT: {vcid_gti}")
                
        except Exception as e:
            import traceback
            traceback.print_exc()  # Print the full error traceback for debugging
            self.show_error("Database Error", f"An error occurred while loading existing audit: {str(e)}")

    def launch_task_randomizer(self, instance):
        """Launch the task randomizer popup."""
        try:
            # Import necessary Kivy classes
            from kivy.graphics import Color, Rectangle
            
            # Create a popup for the task randomizer
            self.randomizer_popup = Popup(
                title="Task Randomizer",
                size_hint=(0.8, 0.9),
                auto_dismiss=False
            )
            
            # Create main content area
            content = BoxLayout(orientation='vertical', spacing=10, padding=10)
            
            # Add a close button at the top right
            close_button = MDRaisedButton(
                text="Close",
                size_hint=(None, None),
                width=100,
                pos_hint={'right': 1},
                text_color=(1, 1, 1, 1),  # White text
                md_bg_color=(0.2, 0.4, 0.6, 1)  # Blue background to make white text visible
            )
            close_button.bind(on_press=self.randomizer_popup.dismiss)
            content.add_widget(close_button)
            
            # Create grey background container for all elements
            grey_container = BoxLayout(
                orientation='vertical',
                spacing=15,
                padding=20,
                size_hint=(1, 1)
            )
            
            # Set grey background color
            with grey_container.canvas.before:
                Color(0.7, 0.7, 0.7, 1)  # Light grey color
                self.grey_rect = Rectangle(pos=grey_container.pos, size=grey_container.size)
            
            # Update rectangle position and size when the layout changes
            def update_rect(instance, value):
                self.grey_rect.pos = instance.pos
                self.grey_rect.size = instance.size
            
            grey_container.bind(pos=update_rect, size=update_rect)
            
            # Add upload button and reset/clear buttons in a horizontal layout
            button_row = BoxLayout(
                orientation='horizontal',
                spacing=10,
                size_hint=(0.8, None),
                height=40,
                pos_hint={'center_x': 0.5}
            )
            
            # Clear button (left side)
            clear_button = MDFlatButton(
                text="Clear",
                size_hint=(None, None)
            )
            clear_button.bind(on_press=self.randomizer_clear_filters)
            button_row.add_widget(clear_button)
            
            # Upload button (center)
            upload_button = MDRaisedButton(
                text="Upload & Process",
                size_hint=(None, None),
                pos_hint={'center_x': 0.5}
            )
            upload_button.bind(on_press=self.randomizer_process_file)
            grey_container.add_widget(upload_button)
            
            # Date filter section
            date_label = Label(text="Filter by Date Range:", bold=True, size_hint=(1, None), height=30, color=(0, 0, 0, 1))
            grey_container.add_widget(date_label)
            
            # Date selectors
            date_selectors = BoxLayout(orientation='horizontal', spacing=10, size_hint=(0.8, None), height=40, pos_hint={'center_x': 0.5})
            
            # Reset button
            reset_button = MDFlatButton(
                text="Reset",
                size_hint=(None, None)
            )
            reset_button.bind(on_press=self.randomizer_reset_filters)
            date_selectors.add_widget(reset_button)
            
            # Clear button
            clear_button = MDFlatButton(
                text="Clear",
                size_hint=(None, None)
            )
            clear_button.bind(on_press=self.randomizer_clear_filters)
            date_selectors.add_widget(clear_button)
            
            # Date range separator
            
            # From date
            from_label = Label(text="From:", size_hint=(None, 1), width=50, color=(0, 0, 0, 1))
            self.date_from_selector = DatePicker(size_hint=(1, 1))
            date_selectors.add_widget(from_label)
            date_selectors.add_widget(self.date_from_selector)
            
            # Till date
            till_label = Label(text="Till:", size_hint=(None, 1), width=40, color=(0, 0, 0, 1))
            self.date_till_selector = DatePicker(size_hint=(1, 1))
            date_selectors.add_widget(till_label)
            date_selectors.add_widget(self.date_till_selector)
            
            grey_container.add_widget(date_selectors)
            
            # Filter button
            self.filter_button = MDRaisedButton(
                text="Filter by Date",
                size_hint=(None, None),
                pos_hint={'center_x': 0.5}
            )
            self.filter_button.bind(on_press=self.randomizer_filter_by_date)
            self.filter_button.disabled = True
            grey_container.add_widget(self.filter_button)
            
            # Add spacing
            grey_container.add_widget(Widget(size_hint_y=None, height=20))
            
            # Time selection section
            time_label = Label(text="Select Total Time:", size_hint=(1, None), height=30, color=(0, 0, 0, 1))
            grey_container.add_widget(time_label)
            
            # Total time dropdown
            self.total_time_dropdown = Spinner(
                text="Select a time range",
                values=["Select a time range"],
                size_hint=(None, None),
                size=(200, 40),
                pos_hint={'center_x': 0.5}
            )
            self.total_time_dropdown.disabled = True
            grey_container.add_widget(self.total_time_dropdown)
            
            # Randomize button
            self.randomize_button = MDRaisedButton(
                text="Randomize Tasks",
                size_hint=(None, None),
                pos_hint={'center_x': 0.5}
            )
            self.randomize_button.bind(on_press=self.randomizer_randomize_tasks)
            self.randomize_button.disabled = True
            grey_container.add_widget(self.randomize_button)
            
            # Results section
            results_label = Label(text="Randomized Results:", bold=True, size_hint=(1, None), height=30, color=(0, 0, 0, 1))
            grey_container.add_widget(results_label)
            
            # Scrollable results area
            scroll_view = ScrollView(size_hint=(1, 0.5))
            self.results_grid = GridLayout(cols=1, spacing=5, size_hint_y=None)
            self.results_grid.bind(minimum_height=self.results_grid.setter('height'))
            scroll_view.add_widget(self.results_grid)
            grey_container.add_widget(scroll_view)
            
            # Export button
            self.export_button = MDRaisedButton(
                text="Export to Excel",
                size_hint=(None, None),
                pos_hint={'center_x': 0.5}
            )
            self.export_button.bind(on_press=self.randomizer_export_to_excel)
            self.export_button.disabled = True
            grey_container.add_widget(self.export_button)
            
            # Add the grey container to the main content
            content.add_widget(grey_container)
            
            self.randomizer_popup.content = content
            self.randomizer_popup.open()
        except Exception as e:
            import traceback
            traceback.print_exc()
            self.show_error("Error", f"Failed to create Task Randomizer popup: {str(e)}")

    def show_quick_links_dropdown(self, button):
        # Create dropdown with options
        dropdown = DropDown()
        
        # Define links for each option
        links = {
            "EDP": "https://atv-optic-domain-tooling-prod-iad.iad.proxy.amazon.com/entity-detail/",
            "CST": "https://cst.ops.video.a2z.com/avails",
            "SOP": "https://policy.a2z.com/folder/bac142fc-ea7f-bbce-280f-2594c0c652c2/c8c9dcc9-c82e-beca-3258-2f197f4202e5",
            "TT": "https://t.corp.amazon.com/issues/all-my-groups",
            "CRMS": "https://atv-ro-domaintooling-iad.iad.proxy.amazon.com/ContentRightsIndex"
        }
        
        # Create buttons for each option
        for option, link in links.items():
            btn = Button(
                text=option,
                size_hint_y=None,
                height=40,
            )
            
            # Bind the button to open the link
            btn.bind(on_release=lambda btn, link=link: self.open_link(link))
            
            # Add the button to the dropdown
            dropdown.add_widget(btn)
        
        # Open the dropdown
        dropdown.open(button)

    def open_link(self, link):
        # Open the link in the default web browser
        import webbrowser
        webbrowser.open(link)

    # Task Randomizer methods
    def randomizer_process_file(self, instance):
        """Process the uploaded Excel file for task randomization."""
        try:
            # Use filechooser directly instead of plyer's implementation
            from tkinter import filedialog
            import tkinter as tk
            
            # Create a temporary Tkinter root window
            root = tk.Tk()
            root.withdraw()  # Hide the root window
            
            # Open file dialog
            file_path = filedialog.askopenfilename(
                filetypes=[("Excel files", "*.xlsx *.xls")],
                title="Select Excel File"
            )
            
            if not file_path or not file_path.strip():
                return
            
            # Process the file
            self.randomizer_df = pd.read_excel(file_path)
            
            # Check if QC Date column exists
            if 'QC Date' not in self.randomizer_df.columns:
                self.show_error("Error", "The file must contain a 'QC Date' column.")
                return
            
            # Convert QC Date to datetime format
            self.randomizer_df['QC Date'] = pd.to_datetime(self.randomizer_df['QC Date'], errors='coerce')
            
            # Print date format for debugging
            print(f"QC Date column type: {self.randomizer_df['QC Date'].dtype}")
            print(f"Sample dates: {self.randomizer_df['QC Date'].head()}")
            
            # Set min and max dates in the date selectors
            if hasattr(self, 'date_from_selector') and hasattr(self, 'date_till_selector'):
                # Get min and max dates from the dataframe
                min_date = self.randomizer_df['QC Date'].min()
                max_date = self.randomizer_df['QC Date'].max()
                
                # Set the dates in the date selectors
                if pd.notna(min_date):
                    self.date_from_selector.set_date(min_date)
                if pd.notna(max_date):
                    self.date_till_selector.set_date(max_date)
                
                print(f"Set min date: {min_date}, max date: {max_date}")
            
            self.show_info("Success", f"File loaded successfully with {len(self.randomizer_df)} records.")
            
            # Enable filter button
            if hasattr(self, 'filter_button'):
                self.filter_button.disabled = False
        
        except Exception as e:
            # Show a more detailed error message
            import traceback
            error_details = traceback.format_exc()
            print(f"Error processing file: {str(e)}\n{error_details}")
            # Display error to user
            self.show_error("Error", f"Error processing file: {str(e)}")
    
    def randomizer_filter_by_date(self, instance):
        """Filter the data by date range."""
        if self.randomizer_df is None or self.randomizer_df.empty:
            self.show_error("Error", "No data loaded. Please upload a file first.")
            return
        
        try:
            # Get selected dates
            from_date = self.date_from_selector.get_date()
            till_date = self.date_till_selector.get_date()
            
            if not from_date or not till_date:
                self.show_error("Error", "Please select both From and Till dates.")
                return
            
            print(f"Filtering from date: {from_date} to date: {till_date}")
            
            # Convert to datetime objects
            from_date = datetime.datetime.strptime(from_date, "%m/%d/%Y")
            till_date = datetime.datetime.strptime(till_date, "%m/%d/%Y")
            # Set till_date to end of day
            till_date = till_date.replace(hour=23, minute=59, second=59)
            
            # Ensure QC Date column is properly converted to datetime
            self.randomizer_df['QC Date'] = pd.to_datetime(self.randomizer_df['QC Date'], errors='coerce')
            
            # Filter data by date range
            self.randomizer_date_filtered_df = self.randomizer_df[
                (self.randomizer_df['QC Date'] >= from_date) & 
                (self.randomizer_df['QC Date'] <= till_date)
            ]
            
            print(f"Original data size: {len(self.randomizer_df)}")
            print(f"Filtered data size: {len(self.randomizer_date_filtered_df)}")
            
            if self.randomizer_date_filtered_df.empty:
                self.show_error("Error", "No data found for the selected date range.")
                return
                
            # Check if Total Time column exists
            if 'Total Time' in self.randomizer_date_filtered_df.columns:
                # Convert Total Time to numeric if it's not already
                if not pd.api.types.is_numeric_dtype(self.randomizer_date_filtered_df['Total Time']):
                    self.randomizer_date_filtered_df['Total Time'] = pd.to_numeric(
                        self.randomizer_date_filtered_df['Total Time'], errors='coerce')
                
                # Get unique Total Time values
                total_times = self.randomizer_date_filtered_df['Total Time'].dropna().unique()
                
                if len(total_times) > 0:
                    # Sort the values
                    total_times = sorted(total_times)
                    
                    # Create time ranges (e.g., 0-1, 1-2, etc.)
                    time_options = []
                    
                    # Get min and max times
                    min_time = min(total_times)
                    max_time = max(total_times)
                    
                    # Create ranges in 1.0 increments
                    current = math.floor(min_time)
                    while current < math.ceil(max_time):
                        next_val = current + 5.0
                        time_options.append(f"{current:.2f}-{next_val:.2f}")
                        current = next_val
                    
                    # Add "All" option at the beginning
                    time_options.insert(0, "All")
                    
                    # Add "less than min" option
                    time_options.insert(1, f"<{min_time:.2f}")
                    
                    # Add "greater than max" option
                    time_options.append(f">{max_time:.2f}")
                    
                    # Update the dropdown
                    if hasattr(self, 'total_time_dropdown'):
                        self.total_time_dropdown.values = time_options
                        self.total_time_dropdown.text = time_options[0]  # Set default value
                else:
                    # Fallback if no Total Time values
                    if hasattr(self, 'total_time_dropdown'):
                        self.total_time_dropdown.values = ["No time data available"]
                        self.total_time_dropdown.text = "No time data available"
            else:
                # Fallback if Total Time column doesn't exist
                if hasattr(self, 'total_time_dropdown'):
                    self.total_time_dropdown.values = ["Total Time column not found"]
                    self.total_time_dropdown.text = "Total Time column not found"
            
            # Enable the dropdown and randomize button
            if hasattr(self, 'total_time_dropdown'):
                self.total_time_dropdown.disabled = False
            if hasattr(self, 'randomize_button'):
                self.randomize_button.disabled = False
            
            self.show_info("Success", f"Filtered data contains {len(self.randomizer_date_filtered_df)} records.")
        except Exception as e:
            import traceback
            traceback.print_exc()
            self.show_error("Error", f"Error filtering by date: {str(e)}")
    
    def randomizer_randomize_tasks(self, instance):
        """Randomize tasks among users."""
        if self.randomizer_date_filtered_df is None or self.randomizer_date_filtered_df.empty:
            self.show_error("Error", "No filtered data available.")
            return
        
        try:
            # Check if S.no column exists
            if 'S.no' not in self.randomizer_date_filtered_df.columns:
                # Try to find similar column names
                possible_columns = [col for col in self.randomizer_date_filtered_df.columns 
                                   if col.lower().replace(' ', '').replace('.', '') == 'sno']
                
                if possible_columns:
                    # Use the first matching column
                    self.randomizer_date_filtered_df.rename(columns={possible_columns[0]: 'S.no'}, inplace=True)
                    print(f"Renamed column '{possible_columns[0]}' to 'S.no'")
                else:
                    self.show_error("Error", "Required column 'S.no' not found in the data.")
                    return
            
            # Get selected time range
            selected_range = self.total_time_dropdown.text
            
            # Filter by time range if applicable
            if selected_range != "All" and 'Total Time' in self.randomizer_date_filtered_df.columns:
                # Parse the range
                if selected_range.startswith("<"):
                    threshold = float(selected_range[1:])
                    time_filtered = self.randomizer_date_filtered_df[self.randomizer_date_filtered_df['Total Time'] < threshold]
                elif selected_range.startswith(">"):
                    threshold = float(selected_range[1:])
                    time_filtered = self.randomizer_date_filtered_df[self.randomizer_date_filtered_df['Total Time'] > threshold]
                elif "-" in selected_range:
                    min_val, max_val = map(float, selected_range.split("-"))
                    time_filtered = self.randomizer_date_filtered_df[
                        (self.randomizer_date_filtered_df['Total Time'] >= min_val) & 
                        (self.randomizer_date_filtered_df['Total Time'] < max_val)
                    ]
                else:
                    time_filtered = self.randomizer_date_filtered_df
            
                if not time_filtered.empty:
                    selected_sno = time_filtered['S.no'].unique()
                    time_filtered_df = self.randomizer_date_filtered_df[self.randomizer_date_filtered_df['S.no'].isin(selected_sno)]
                else:
                    time_filtered_df = pd.DataFrame()
            else:
                time_filtered_df = self.randomizer_date_filtered_df
            
            if time_filtered_df.empty:
                self.show_error("Error", "No data found for the selected time range.")
                return
            
            # Get unique dates
            unique_dates = time_filtered_df['QC Date'].dt.date.unique()
            
            if len(unique_dates) == 0:
                self.show_error("Error", "No dates found in the filtered data.")
                return
            
            all_samples = []
            
            # Process each date
            for date in unique_dates:
                # Get data for this date
                date_data = time_filtered_df[time_filtered_df['QC Date'].dt.date == date]
                
                # Get unique User IDs for this date
                unique_users = date_data['User ID'].unique()
                
                # Process each User ID for this date
                for user_id in unique_users:
                    # Get data for this user on this date
                    user_data = date_data[date_data['User ID'] == user_id]
                    
                    # Get unique S.no values for this user on this date
                    unique_sno = user_data['S.no'].unique()
                    total_titles = len(unique_sno)
                    
                    # Calculate 10% of total titles (minimum 1)
                    sample_size = max(1, math.ceil(0.1 * total_titles))
                    
                    # Sample 10% of unique S.no values
                    if sample_size < total_titles:
                        sampled_sno = np.random.choice(unique_sno, size=sample_size, replace=False)
                    else:
                        sampled_sno = unique_sno
                    
                    # Get all rows for the sampled S.no values
                    sampled_data = user_data[user_data['S.no'].isin(sampled_sno)]
                    
                    # Add to all samples
                    all_samples.append(sampled_data)
                    
                    # Log sampling details
                    print(f"User {user_id} on {date}: Total titles={total_titles}, Sampled={len(sampled_sno)}")
            
            # Combine all samples
            if all_samples:
                self.randomizer_final_export = pd.concat(all_samples, ignore_index=True)
                
                # Store user title counts for display
                self.user_title_counts = {}
                for user_id in self.randomizer_final_export['User ID'].unique():
                    user_data = time_filtered_df[time_filtered_df['User ID'] == user_id]
                    user_total_titles = len(user_data['S.no'].unique())
                    
                    user_sampled = self.randomizer_final_export[self.randomizer_final_export['User ID'] == user_id]
                    user_sampled_titles = len(user_sampled['S.no'].unique())
                    
                    self.user_title_counts[user_id] = {
                        'total': user_total_titles,
                        'sampled': user_sampled_titles,
                        'percentage': round(user_sampled_titles / user_total_titles * 100, 1) if user_total_titles > 0 else 0
                    }
                
                # Enable export button
                if hasattr(self, 'export_button'):
                    self.export_button.disabled = False
                
                # Display results in the results grid
                self.update_results_display()
            else:
                self.show_error("Error", "No data found for the selected criteria.")
        except Exception as e:
            import traceback
            traceback.print_exc()
            self.show_error("Error", f"Error randomizing tasks: {str(e)}")

    def update_results_display(self):
        """Update the results display with sampling information."""
        if not hasattr(self, 'results_grid') or self.randomizer_final_export is None:
            return
        
        # Clear previous results
        self.results_grid.clear_widgets()
        
        # Add user-specific counts with percentage information
        if hasattr(self, 'user_title_counts'):
            for user, data in self.user_title_counts.items():
                user_label = Label(
                    text=f"{user}: {data['sampled']}/{data['total']}", 
                    size_hint_y=None, 
                    height=30, 
                    halign='center', 
                    color=(0, 0, 0, 1)
                )
                user_label.bind(size=user_label.setter('text_size'))
                self.results_grid.add_widget(user_label)
        
        # Add category distribution if available
        # if 'Category' in self.randomizer_final_export.columns:
        #     category_counts = self.randomizer_final_export['Category'].value_counts()
        #     category_label = Label(
        #         text=f"Category distribution: {', '.join([f'{cat}: {count}' for cat, count in category_counts.items()])}",
        #         size_hint_y=None, height=30, halign='center', color=(0, 0, 0, 1)
        #     )
        #     category_label.bind(size=category_label.setter('text_size'))
        #     self.results_grid.add_widget(category_label)
        
        # # Add AHT distribution if available
        # if 'AHT' in self.randomizer_final_export.columns:
        #     aht_count = self.randomizer_final_export['AHT'].notna().sum()
        #     total_count = len(self.randomizer_final_export)
        #     aht_label = Label(
        #         text=f"AHT distribution: {aht_count}/{total_count} tasks with AHT values ({(aht_count/total_count)*100:.1f}%)",
        #         size_hint_y=None, height=30, halign='center', color=(0, 0, 0, 1)
        #     )
        #     aht_label.bind(size=aht_label.setter('text_size'))
        #     self.results_grid.add_widget(aht_label)

    def send_email_notification(self, vcid_gti, user_id, week, op_qced_date, audit_qc_date, 
                               auditor_id, distracting, destructive, critical, 
                               overall_error, quality, feedback, checks_details):
        """Send email notification with audit details using Outlook."""
        try:
            import win32com.client as win32
            
            # Create Outlook application object
            outlook = win32.Dispatch('Outlook.Application')
            mail = outlook.CreateItem(0)  # 0 = olMailItem
            
            # Get manager and SME for the current subprocess
            manager = ""
            sme = ""
            try:
                # Connect to the workflow database
                conn_str = (
                    r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};"
                    r"DBQ=\\ant\dept-in\Digiflex\DF-Cataloghealth\Quality Products\Audits\Workflow\Workflow database.accdb;"
                )
                with pyodbc.connect(conn_str) as conn:
                    cursor = conn.cursor()
                    # Get the current subprocess from the popup title if available
                    subprocess = ""
                    if hasattr(self, 'popup') and self.popup:
                        title_parts = self.popup.title.split(' - ')
                        if len(title_parts) > 1:
                            subprocess = title_parts[1]


                    print(f'subprocess:{subprocess}')



        
                    # Query for manager and SME
                    cursor.execute("SELECT Manager, SME FROM Workflow WHERE [Sub Process] = ?", (subprocess,))
                    result = cursor.fetchone()
                    #print(f"Result added to CC: {result}")
                    
                    if result:
                        manager, sme = result
                        print(f"Sme email added to CC: {sme}")
                        print(f"Manager email added to CC: {manager}")
            except Exception as e:
                print(f"Error retrieving manager and SME: {e}")
            
            # Set recipients - validate email addresses before adding them
            if user_id and '@' not in user_id:  # If user_id doesn't contain @, add the domain
                mail.To = f"{user_id}@amazon.com"
            else:
                mail.To = user_id
            
            # Build CC list with validation
            cc_recipients = []
            if sme:
                if '@' not in sme:
                    cc_recipients.append(f"{sme}@amazon.com")
                else:
                    cc_recipients.append(sme)

                
                
            if manager:
                if '@' not in manager:
                    cc_recipients.append(f"{manager}@amazon.com")
                else:
                    cc_recipients.append(manager)

            if cc_recipients:
                mail.CC = "; ".join(cc_recipients)
            
            # Set subject
            mail.Subject = f"{'Reaudit' if hasattr(self, 'reaudit_mode') else 'Quality Audit'} Feedback/Scores - Week {week}"
            
            # Create HTML table for the email body
            html_body = f"""
            <html>
            <body>
            <p>Hello,</p>
            <p>Please find below the results of your recent quality audit:</p>
            <table border="1" cellpadding="5" cellspacing="0">
                <tr bgcolor="#f2f2f2">
                    <th>Field</th>
                    <th>Value</th>
                </tr>
                <tr>
                    <td>VCID/GTI/URN/TT</td>
                    <td>{vcid_gti}</td>
                </tr>
                <tr>
                    <td>User ID</td>
                    <td>{user_id}</td>
                </tr>
                <tr>
                    <td>Week</td>
                    <td>{week}</td>
                </tr>
                <tr>
                    <td>Op Qc'ed Date</td>
                    <td>{op_qced_date}</td>
                </tr>
                <tr>
                    <td>Audit Qc Date</td>
                    <td>{audit_qc_date}</td>
                </tr>
                <tr>
                    <td>Auditor ID</td>
                    <td>{auditor_id}</td>
                </tr>
                <tr>
                    <td>Distracting</td>
                    <td>{distracting}</td>
                </tr>
                <tr>
                    <td>Destructive</td>
                    <td>{destructive}</td>
                </tr>
                <tr>
                    <td>Critical</td>
                    <td>{critical}</td>
                </tr>
                <tr>
                    <td>Overall Error</td>
                    <td>{overall_error}</td>
                </tr>
                <tr>
                    <td>Quality %</td>
                    <td>{quality}</td>
                </tr>
                <tr>
                    <td>Feedback</td>
                    <td>{feedback}</td>
                </tr>
                <tr>
                    <td>Checks Details</td>
                    <td>{checks_details}</td>
                </tr>
            </table>
            <p>{'Kindly review the feedback.' if hasattr(self, 'reaudit_mode') else 'Kindly review the feedback and if you wish to dispute the feedback, please use the <a href="https://t.corp.amazon.com/create/templates/b7db5c84-b9b5-4471-9b79-e24778615138">link</a> below to submit the dispute form.'}</p>            
            <p>Regards,<br>{auditor_id}</p>
            </body>
            </html>
            """
            
            mail.HTMLBody = html_body
            
            # Send the email directly instead of displaying it
            mail.Send()
            
            print(f"Email notification sent to {user_id}@amazon.com")
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            print(f"Failed to send email notification: {str(e)}")
            self.show_error("Email Error", f"Failed to send email notification: {str(e)}")
            self.show_error("Error", "Failed to generate randomized tasks.")
        except Exception as e:
            import traceback
            traceback.print_exc()
            self.show_error("Error", f"Error randomizing tasks: {str(e)}")

    def randomizer_export_to_excel(self, instance):
        """Export the randomized tasks to an Excel file."""
        if self.randomizer_final_export is None or self.randomizer_final_export.empty:
            self.show_error("Error", "No randomized tasks to export.")
            return
        
        try:
            # Use tkinter's filedialog
            from tkinter import filedialog
            import tkinter as tk
            
            # Create a temporary Tkinter root window
            root = tk.Tk()
            root.withdraw()  # Hide the root window
            
            # Open save file dialog
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx")],
                title="Save Randomized Tasks as Excel File"
            )
            
            if not file_path or not file_path.strip():
                return
            
            # Ensure file has .xlsx extension
            if not file_path.endswith('.xlsx'):
                file_path += '.xlsx'
            
            # Create a sorting key for Total Time - rows with Total Time come first
            if 'Total Time' in self.randomizer_final_export.columns:
                # Convert Total Time to numeric if it's not already
                self.randomizer_final_export['Total Time'] = pd.to_numeric(
                    self.randomizer_final_export['Total Time'], errors='coerce')
                
                # Create a sorting flag: False for rows with Total Time (to sort first), True for NaN
                self.randomizer_final_export['_total_time_missing'] = self.randomizer_final_export['Total Time'].isna()
                
                # Sort by S.no first, then by _total_time_missing flag, then by Total Time if present
                self.randomizer_final_export = self.randomizer_final_export.sort_values(
                    by=['S.no', '_total_time_missing', 'Total Time'])
                
                # Remove the temporary sorting column
                self.randomizer_final_export = self.randomizer_final_export.drop('_total_time_missing', axis=1)
            else:
                # If Total Time column doesn't exist, just sort by S.no
                self.randomizer_final_export = self.randomizer_final_export.sort_values(by='S.no')
            
            # Export to Excel with explicit index=False to ensure all rows are included
            self.randomizer_final_export.to_excel(file_path, index=False)
            
            self.show_info("Success", f"Data exported successfully")
        except Exception as e:
            import traceback
            traceback.print_exc()
            self.show_error("Error", f"Error exporting to Excel: {str(e)}")

    def randomizer_reset_filters(self, instance):
        """Reset date filters to original values from the loaded data."""
        try:
            if self.randomizer_df is None or self.randomizer_df.empty:
                self.show_error("Error", "No data loaded. Please upload a file first.")
                return
            
            # Reset to min and max dates from the original dataframe
            min_date = self.randomizer_df['QC Date'].min()
            max_date = self.randomizer_df['QC Date'].max()
            
            # Set the dates in the date selectors
            if pd.notna(min_date):
                self.date_from_selector.set_date(min_date)
            if pd.notna(max_date):
                self.date_till_selector.set_date(max_date)
            
            # Reset the total time dropdown
            if hasattr(self, 'total_time_dropdown'):
                self.total_time_dropdown.text = "Select a time range"
                self.total_time_dropdown.values = ["Select a time range"]
                self.total_time_dropdown.disabled = True
            
            # Reset the randomize button
            if hasattr(self, 'randomize_button'):
                self.randomize_button.disabled = True
            
            # Clear results
            if hasattr(self, 'results_grid'):
                self.results_grid.clear_widgets()
            
            # Reset filtered dataframes
            self.randomizer_date_filtered_df = None
            self.randomizer_final_export = None
            
            self.show_info("Reset", "Filters have been reset to original values.")
        except Exception as e:
            import traceback
            traceback.print_exc()
            self.show_error("Error", f"Error resetting filters: {str(e)}")

    def randomizer_clear_filters(self, instance):
        """Clear all filters and reset the form."""
        try:
            # Clear date selectors
            if hasattr(self, 'date_from_selector'):
                self.date_from_selector.month_spinner.text = "01"
                self.date_from_selector.day_spinner.text = "01"
                current_year = datetime.datetime.now().year
                self.date_from_selector.year_spinner.text = str(current_year)
            
            if hasattr(self, 'date_till_selector'):
                self.date_till_selector.month_spinner.text = "12"
                self.date_till_selector.day_spinner.text = "31"
                current_year = datetime.datetime.now().year
                self.date_till_selector.year_spinner.text = str(current_year)
            
            # Reset the total time dropdown
            if hasattr(self, 'total_time_dropdown'):
                self.total_time_dropdown.text = "Select a time range"
                self.total_time_dropdown.values = ["Select a time range"]
                self.total_time_dropdown.disabled = True
            
            # Reset the randomize button
            if hasattr(self, 'randomize_button'):
                self.randomize_button.disabled = True
            
            # Clear results
            if hasattr(self, 'results_grid'):
                self.results_grid.clear_widgets()
            
            # Reset filtered dataframes
            self.randomizer_date_filtered_df = None
            self.randomizer_final_export = None
            
            self.show_info("Cleared", "All filters have been cleared.")
        except Exception as e:
            import traceback
            traceback.print_exc()
            self.show_error("Error", f"Error clearing filters: {str(e)}")
    
    def show_quality_scores(self, instance):
        """Show quality scores popup with filtering options."""
        try:
            # Import necessary Kivy classes
            from kivy.graphics import Color, Rectangle
            
            # Create a popup for the quality scores
            self.quality_scores_popup = Popup(
                title="Quality Scores",
                size_hint=(0.8, 0.9),
                auto_dismiss=False
            )
            
            # Create main content area
            content = BoxLayout(orientation='vertical', spacing=10, padding=10)
            
            # Add a close button at the top right
            close_button = MDRaisedButton(
                text="Close",
                size_hint=(None, None),
                width=100,
                pos_hint={'right': 1},
                text_color=(1, 1, 1, 1),
                md_bg_color=(0.2, 0.4, 0.6, 1)
            )
            close_button.bind(on_press=self.quality_scores_popup.dismiss)
            content.add_widget(close_button)
            
            # Create grey background container for all elements
            grey_container = BoxLayout(
                orientation='vertical',
                spacing=15,
                padding=20,
                size_hint=(1, 1)
            )
            
            # Set grey background color
            with grey_container.canvas.before:
                Color(0.7, 0.7, 0.7, 1)  # Light grey color
                self.grey_rect = Rectangle(pos=grey_container.pos, size=grey_container.size)
            
            # Update rectangle position and size when the layout changes
            def update_rect(instance, value):
                self.grey_rect.pos = instance.pos
                self.grey_rect.size = instance.size
            
            grey_container.bind(pos=update_rect, size=update_rect)
            
            # Add filter section title
            filter_label = Label(
                text="Filter Quality Scores",
                bold=True,
                size_hint=(1, None),
                height=40,
                color=(0, 0, 0, 1)
            )
            grey_container.add_widget(filter_label)
            
            # Process and Subprocess dropdowns in a single horizontal layout
            process_row = BoxLayout(
                orientation='horizontal',
                spacing=10,
                size_hint=(0.8, None),
                height=50,
                pos_hint={'center_x': 0.5}
            )
            
            # Process dropdown
            process_label = Label(
                text="Process:",
                size_hint=(None, 1),
                width=80,
                color=(0, 0, 0, 1)
            )
            self.quality_process_dropdown = Spinner(
                text="Select Process",
                values=["Select Process"],
                size_hint=(None, 1),
                width=300,
                background_color=(0.2, 0.4, 0.6, 1),
                background_normal='',
                color=(1, 1, 1, 1),
                option_cls=CustomSpinnerOption
            )
            self.quality_process_dropdown.bind(text=self.on_quality_process_selected)
            
            # Subprocess dropdown (now in the same row)
            subprocess_label = Label(
                text="Subprocess:",
                size_hint=(None, 1),
                width=120,
                color=(0, 0, 0, 1)
            )
            self.quality_subprocess_dropdown = Spinner(
                text="Select Subprocess",
                values=["Select Subprocess"],
                size_hint=(None, 1),
                width=400,
                background_color=(0.2, 0.4, 0.6, 1),
                background_normal='',
                color=(1, 1, 1, 1),
                option_cls=CustomSpinnerOption
            )
            
            # Add all widgets to the same row with a spacer between Process and Subprocess
            process_row.add_widget(process_label)
            process_row.add_widget(self.quality_process_dropdown)
            
            # Add spacer widget
            spacer = Widget(size_hint_x=0.1)
            process_row.add_widget(spacer)
            
            process_row.add_widget(subprocess_label)
            process_row.add_widget(self.quality_subprocess_dropdown)
            
            grey_container.add_widget(process_row)
            
            # Week and User ID dropdowns in a horizontal layout
            filter_row = BoxLayout(
                orientation='horizontal',
                spacing=10,
                size_hint=(0.8, None),
                height=50,
                pos_hint={'center_x': 0.7}
            )
            
            # Week dropdown
            week_label = Label(
                text="Week:",
                size_hint=(None, 1),
                width=100,
                color=(0, 0, 0, 1)
            )
            self.quality_week_dropdown = Spinner(
                text="All",
                values=["All"],
                size_hint=(None, 1),
                width=120,
                background_color=(0.2, 0.4, 0.6, 1),
                background_normal='',
                color=(1, 1, 1, 1),
                option_cls=CustomSpinnerOption
            )
            self.quality_week_dropdown.bind(text=self.on_quality_filter_changed)
            
            # User ID dropdown
            user_label = Label(
                text="User ID:",
                size_hint=(None, 1),
                width=100,
                color=(0, 0, 0, 1)
            )
            self.quality_user_dropdown = Spinner(
                text="All",
                values=["All"],
                size_hint=(None, 1),
                width=120,
                background_color=(0.2, 0.4, 0.6, 1),
                background_normal='',
                color=(1, 1, 1, 1),
                option_cls=CustomSpinnerOption
            )
            self.quality_user_dropdown.bind(text=self.on_quality_filter_changed)
            
            filter_row.add_widget(week_label)
            filter_row.add_widget(self.quality_week_dropdown)
            filter_row.add_widget(user_label)
            filter_row.add_widget(self.quality_user_dropdown)
            
            grey_container.add_widget(filter_row)
            
            # Load data button
            button_row = BoxLayout(
                orientation='horizontal',
                spacing=20,
                size_hint=(None, None),
                width=350,
                height=50,
                pos_hint={'center_x': 0.5}
            )
            
            load_button = MDRaisedButton(
                text="Load Data",
                size_hint=(None, None),
                width=150,
                height=50,
                text_color=(1, 1, 1, 1),
                md_bg_color=(0.2, 0.4, 0.6, 1)
            )
            load_button.bind(on_press=self.load_quality_scores)
            
            # Add Team Quality label
            self.team_quality_label = Label(
                text="Team Quality: N/A",
                size_hint=(None, None),
                width=400,
                height=50,
                color=(0, 0, 0, 1),
                bold=True
            )
            
            # Add Send Mail button
            send_mail_button = MDRaisedButton(
                text="Send Mail",
                size_hint=(None, None),
                width=150,
                height=50,
                text_color=(1, 1, 1, 1),
                md_bg_color=(0.2, 0.6, 0.4, 1)  # Green color to differentiate
            )
            send_mail_button.bind(on_press=self.send_quality_scores_email)
            
            button_row.add_widget(load_button)
            button_row.add_widget(send_mail_button)
            button_row.add_widget(self.team_quality_label)
            grey_container.add_widget(button_row)
            
            # Results section
            results_label = Label(
                text="Quality Score Results:",
                bold=True,
                size_hint=(1, None),
                height=40,
                color=(0, 0, 0, 1)
            )
            grey_container.add_widget(results_label)
            
            # Scrollable results area
            scroll_view = ScrollView(size_hint=(1, 1))
            self.quality_results_grid = GridLayout(
                cols=1,  # Single column layout
                spacing=5,
                size_hint_y=None
            )
            self.quality_results_grid.bind(minimum_height=self.quality_results_grid.setter('height'))
            
            # Add header row
            header_row = GridLayout(
                cols=3,
                size_hint_y=None,
                height=40
            )
            
            header_user = Label(
                text="User ID",
                bold=True,
                color=(0, 0, 0, 1)
            )
            header_week = Label(
                text="Week",
                bold=True,
                color=(0, 0, 0, 1)
            )
            header_quality = Label(
                text="Quality %",
                bold=True,
                color=(0, 0, 0, 1)
            )
            
            header_row.add_widget(header_user)
            header_row.add_widget(header_week)
            header_row.add_widget(header_quality)
            
            self.quality_results_grid.add_widget(header_row)
            scroll_view.add_widget(self.quality_results_grid)
            grey_container.add_widget(scroll_view)
            
            # Add the grey container to the main content
            content.add_widget(grey_container)
            
            # Initialize the team quality label
            self.team_quality_label.text = "Team Quality: N/A"
            
            self.quality_scores_popup.content = content
            self.quality_scores_popup.open()
            
            # Load processes for the dropdown
            self.load_quality_processes()
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            self.show_error("Error", f"Failed to create Quality Scores popup: {str(e)}")
    
    def load_quality_processes(self):
        """Load processes for the quality scores dropdown."""
        try:
            # If processes are already loaded, use them
            if self.processes:
                self.quality_process_dropdown.values = ["All"] + self.processes
                return
            
            # Otherwise load from database
            with pyodbc.connect(self.conn_str) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT DISTINCT Process FROM Workflow")
                processes = cursor.fetchall()
                self.processes = [row[0] for row in processes]
                self.quality_process_dropdown.values = ["All"] + self.processes
        except Exception as e:
            self.show_error("Database Error", f"An error occurred while loading processes: {str(e)}")
    
    def on_quality_process_selected(self, spinner, text):
        """Handle process selection in quality scores popup."""
        if text == "Select Process" or text == "All":
            self.quality_subprocess_dropdown.values = ["Select Subprocess"]
            self.quality_subprocess_dropdown.text = "Select Subprocess"
            return
        
        try:
            # Check if sub-processes for this process are already cached
            if text in self.sub_processes:
                self.quality_subprocess_dropdown.values = ["All"] + self.sub_processes[text]
                self.quality_subprocess_dropdown.text = "All"
                return
            
            # Otherwise load from database
            with pyodbc.connect(self.conn_str) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT DISTINCT [Sub Process] FROM Workflow WHERE Process = ?", (text,))
                sub_processes = cursor.fetchall()
                if sub_processes:
                    self.sub_processes[text] = [row[0] for row in sub_processes]
                    self.quality_subprocess_dropdown.values = ["All"] + self.sub_processes[text]
                    self.quality_subprocess_dropdown.text = "All"
                else:
                    self.sub_processes[text] = []
                    self.quality_subprocess_dropdown.values = ["All"]
                    self.quality_subprocess_dropdown.text = "All"
        except Exception as e:
            self.show_error("Database Error", f"An error occurred while loading sub processes: {str(e)}")
    
    def load_quality_scores(self, instance):
        """Load quality scores from the database based on selected filters."""
        try:
            process = self.quality_process_dropdown.text
            subprocess = self.quality_subprocess_dropdown.text
            
            if process == "Select Process":
                self.show_error("Input Error", "Please select a Process.")
                return
            
            # Connect to the database - use the specific path provided
            conn_str = (
                r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};"
                r"DBQ=\\ant\dept-in\Digiflex\DF-Cataloghealth\Quality Products\Audits\QP-&-Tentpole-Audits\Audits(Science Support).accdb;"
            )
            
            # Initialize lists to store data
            all_data = []
            
            # Use a new connection for each table to avoid "Query Objects are being updated" error
            with pyodbc.connect(conn_str) as conn:
                cursor = conn.cursor()
                # Get all table names from the database
                tables = [table[2] for table in cursor.tables(tableType='TABLE')]
                
                # Find tables that match the selected subprocess
                matching_tables = []
                
                if subprocess == "All" or subprocess == "Select Subprocess":
                    # If "All" is selected, get all subprocess values from the dropdown (except "All")
                    # and find matching tables for each
                    subprocess_values = [val for val in self.quality_subprocess_dropdown.values if val not in ["All", "Select Subprocess"]]
                    
                    for sub_proc in subprocess_values:
                        # Map each subprocess to table name by finding the closest match
                        for table in tables:
                            # Remove brackets and 'Audits' suffix for comparison
                            clean_table = table.replace('[', '').replace(']', '')
                            if 'Audits' in clean_table:
                                clean_table = clean_table.replace(' Audits', '')
                            
                            # Check if the subprocess matches or is contained in the table name
                            if sub_proc.lower() == clean_table.lower() or sub_proc.lower() in clean_table.lower():
                                if table not in matching_tables:  # Avoid duplicates
                                    matching_tables.append(table)
                else:
                    # Map subprocess to table name by finding the closest match
                    for table in tables:
                        # Remove brackets and 'Audits' suffix for comparison
                        clean_table = table.replace('[', '').replace(']', '')
                        if 'Audits' in clean_table:
                            clean_table = clean_table.replace(' Audits', '')
                        
                        # Check if the subprocess matches or is contained in the table name
                        if subprocess.lower() == clean_table.lower() or subprocess.lower() in clean_table.lower():
                            matching_tables.append(table)
                
                if not matching_tables:
                    self.show_error("Input Error", "No matching tables found for the selected subprocess.")
                    return
            
            # Query each matching table with a separate connection
            for table in matching_tables:
                try:
                    # Create a new connection for each table
                    with pyodbc.connect(conn_str) as conn:
                        cursor = conn.cursor()
                        
                        # Ensure table name is properly formatted with square brackets
                        if not (table.startswith('[') and table.endswith(']')):
                            table = f"[{table}]"
                        
                        # Query to get quality scores
                        query = f"""SELECT [User ID], [Week], [Quality %] FROM {table}"""
                        cursor.execute(query)
                        
                        # Fetch all results
                        results = cursor.fetchall()
                        
                        # Add to all data
                        for row in results:
                            all_data.append({
                                'User ID': row[0],
                                'Week': row[1],
                                'Quality %': row[2]
                            })
                except Exception as table_error:
                    print(f"Error processing table {table}: {str(table_error)}")
                    continue  # Skip this table and continue with others
            
            # Convert to DataFrame for easier filtering
            df = pd.DataFrame(all_data)
            
            if df.empty:
                self.show_error("No Data", "No quality scores found for the selected criteria.")
                return
            
            # Get unique weeks and user IDs for dropdowns
            unique_weeks = sorted(df['Week'].unique().tolist())
            unique_users = sorted(df['User ID'].unique().tolist())
            
            # Update dropdowns
            self.quality_week_dropdown.values = ["All"] + [str(week) for week in unique_weeks]
            self.quality_user_dropdown.values = ["All"] + unique_users
            
            # Store the data for filtering
            self.quality_scores_df = df
            
            # Reset team quality label before displaying new data
            if hasattr(self, 'team_quality_label'):
                self.team_quality_label.text = "Team Quality: N/A"
            
            # Display the data
            self.display_quality_scores()
                
        except Exception as e:
            import traceback
            traceback.print_exc()
            self.show_error("Database Error", f"An error occurred while loading quality scores: {str(e)}")
                
        except Exception as e:
            import traceback
            traceback.print_exc()
            self.show_error("Database Error", f"An error occurred while loading quality scores: {str(e)}")
    
    def on_quality_filter_changed(self, spinner, text):
        """Handle filter changes in quality scores popup."""
        if hasattr(self, 'quality_scores_df'):
            # If week filter changed, update user dropdown
            if spinner == self.quality_week_dropdown:
                self.update_user_dropdown_for_week()
            self.display_quality_scores()
    
    def update_user_dropdown_for_week(self):
        """Update user ID dropdown based on selected week."""
        if not hasattr(self, 'quality_scores_df') or self.quality_scores_df.empty:
            return
        
        selected_week = self.quality_week_dropdown.text
        
        if selected_week == "All":
            # Show all users
            unique_users = sorted(self.quality_scores_df['User ID'].unique().tolist())
        else:
            # Filter users by selected week
            week_filtered_df = self.quality_scores_df[self.quality_scores_df['Week'].astype(str) == selected_week]
            unique_users = sorted(week_filtered_df['User ID'].unique().tolist())
        
        # Update user dropdown values
        self.quality_user_dropdown.values = ["All"] + unique_users
        # Reset user selection to "All" when week changes
        self.quality_user_dropdown.text = "All"
    
    def display_quality_scores(self):
        """Display quality scores based on selected filters."""
        if not hasattr(self, 'quality_scores_df') or self.quality_scores_df.empty:
            return
        
        try:
            # Get selected filters
            selected_week = self.quality_week_dropdown.text
            selected_user = self.quality_user_dropdown.text
            
            # Apply filters
            filtered_df = self.quality_scores_df.copy()
            
            if selected_week != "All":
                filtered_df = filtered_df[filtered_df['Week'].astype(str) == selected_week]
            
            if selected_user != "All":
                filtered_df = filtered_df[filtered_df['User ID'] == selected_user]
            
            # Check if both week and user ID are "All" - special condition
            if selected_week == "All" and selected_user == "All":
                # Group by User ID only and calculate average of average quality across all weeks
                grouped_df = filtered_df.groupby(['Week', 'User ID'])['Quality %'].mean().reset_index()
                # Then group by User ID and calculate average of those averages
                grouped_df = grouped_df.groupby('User ID')['Quality %'].mean().reset_index()
                # Add a placeholder Week column for display consistency
                grouped_df['Week'] = 'All Weeks'
            else:
                # Group by Week and User ID, and calculate average Quality %
                grouped_df = filtered_df.groupby(['Week', 'User ID'])['Quality %'].mean().reset_index()
            
            # Calculate team average quality score
            team_avg_quality = grouped_df['Quality %'].mean()
            
            # Update the team quality label
            if hasattr(self, 'team_quality_label'):
                self.team_quality_label.text = f"Team Quality: {team_avg_quality:.2f}%"
            
            # Store the grouped dataframe for email sending
            self.quality_scores_grouped_df = grouped_df
            
            # Clear previous results
            self.quality_results_grid.clear_widgets()
            
            # Add header row
            header_box = BoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=40
            )
            
            header_user = Label(
                text="User ID",
                bold=True,
                size_hint_x=0.33,
                color=(0, 0, 0, 1)
            )
            header_week = Label(
                text="Week",
                bold=True,
                size_hint_x=0.33,
                color=(0, 0, 0, 1)
            )
            header_quality = Label(
                text="Quality % (Avg)",
                bold=True,
                size_hint_x=0.34,
                color=(0, 0, 0, 1)
            )
            
            header_box.add_widget(header_user)
            header_box.add_widget(header_week)
            header_box.add_widget(header_quality)
            
            self.quality_results_grid.add_widget(header_box)
            
            # Add data rows from grouped data
            for _, row in grouped_df.iterrows():
                data_box = BoxLayout(
                    orientation='horizontal',
                    size_hint_y=None,
                    height=40
                )
                
                user_label = Label(
                    text=str(row['User ID']),
                    size_hint_x=0.33,
                    color=(0, 0, 0, 1)
                )
                week_label = Label(
                    text=str(row['Week']),
                    size_hint_x=0.33,
                    color=(0, 0, 0, 1)
                )
                quality_label = Label(
                    text=f"{row['Quality %']:.2f}",
                    size_hint_x=0.34,
                    color=(0, 0, 0, 1)
                )
                
                data_box.add_widget(user_label)
                data_box.add_widget(week_label)
                data_box.add_widget(quality_label)
                
                self.quality_results_grid.add_widget(data_box)
            
            # Update grid height
            self.quality_results_grid.height = len(grouped_df) * 40 + 40  # +40 for header
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            self.show_error("Error", f"An error occurred while displaying quality scores: {str(e)}")

    def send_quality_scores_email(self, instance):
        """Send quality scores email to selected users."""
        if not hasattr(self, 'quality_scores_grouped_df') or self.quality_scores_grouped_df.empty:
            self.show_error("Error", "No quality scores data available. Please load data first.")
            return
        
        try:
            # Get the process and subprocess for the email subject
            process = self.quality_process_dropdown.text
            subprocess = self.quality_subprocess_dropdown.text
            selected_week = self.quality_week_dropdown.text
            
            # Create a confirmation dialog
            confirm_dialog = MDDialog(
                title="Send Email Confirmation",
                text=f"Send quality scores email for {process} - {subprocess} to all users in the current view?",
                buttons=[
                    MDFlatButton(
                        text="CANCEL",
                        on_release=lambda x: confirm_dialog.dismiss()
                    ),
                    MDRaisedButton(
                        text="SEND",
                        on_release=lambda x: self.process_send_emails(confirm_dialog)
                    ),
                ],
            )
            confirm_dialog.open()
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            self.show_error("Error", f"Failed to prepare email: {str(e)}")
    
    def process_send_emails(self, dialog):
        """Process and send emails after confirmation."""
        dialog.dismiss()
        
        try:
            # Get the process and subprocess for the email subject
            process = self.quality_process_dropdown.text
            subprocess = self.quality_subprocess_dropdown.text
            selected_week = self.quality_week_dropdown.text
            
            # Get current date for the email
            current_date = datetime.datetime.now().strftime("%m/%d/%Y")
            
            # Group by User ID to send one email per user with all their weeks
            user_groups = self.quality_scores_grouped_df.groupby('User ID')
            
            # Track success and failures
            success_count = 0
            failure_count = 0
            
            # Connect to the workflow database to get manager and SME info
            manager = ""
            sme = ""
            try:
                conn_str = (
                    r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};"
                    r"DBQ=\\ant\dept-in\Digiflex\DF-Cataloghealth\Quality Products\Audits\Workflow\Workflow database.accdb;"
                )
                with pyodbc.connect(conn_str) as conn:
                    cursor = conn.cursor()
                    # Query for manager and SME
                    cursor.execute("SELECT Manager, SME FROM Workflow WHERE [Sub Process] = ?", (subprocess,))
                    result = cursor.fetchone()
                    if result:
                        manager, sme = result
            except Exception as e:
                print(f"Error retrieving manager and SME: {e}")
            
            # Process each user
            for user_id, user_data in user_groups:
                try:
                    # Create Outlook application object
                    import win32com.client as win32
                    outlook = win32.Dispatch('Outlook.Application')
                    mail = outlook.CreateItem(0)  # 0 = olMailItem
                    
                    # Set recipients
                    if user_id and '@' not in user_id:
                        mail.To = f"{user_id}@amazon.com"
                    else:
                        mail.To = user_id
                    
                    # Build CC list with validation
                    cc_recipients = []
                    if sme and sme.strip():
                        if '@' not in sme:
                            cc_recipients.append(f"{sme}@amazon.com")
                        else:
                            cc_recipients.append(sme)
                        
                    if manager and manager.strip():
                        if '@' not in manager:
                            cc_recipients.append(f"{manager}@amazon.com")
                        else:
                            cc_recipients.append(manager)
                        
                    if cc_recipients:
                        mail.CC = "; ".join(cc_recipients)
                    
                    # Set subject
                    week_text = f"Week {selected_week}" if selected_week != "All" else "Weekly Summary"
                    mail.Subject = f"Weekly Quality Scores - {process} - {subprocess} - {week_text}"
                    
                    # Create HTML table for the email body
                    html_body = f"""
                    <html>
                    <body>
                    <p>Hello,</p>
                    <p>Please find below your quality scores summary:</p>
                    <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; text-align: center;">
                        <tr bgcolor="#f2f2f2">
                            <th style="text-align: center;">Week</th>
                            <th style="text-align: center;">Quality %</th>
                        </tr>
                    """
                    
                    # Add rows for each week
                    for _, row in user_data.iterrows():
                        quality_score = float(row['Quality %'])
                        
                        html_body += f"""
                        <tr>
                            <td style="text-align: center;">{row['Week']}</td>
                            <td style="text-align: center; font-weight: bold;">{quality_score:.2f}%</td>
                        </tr>
                        """
                    
                    # Close the table
                    html_body += f"""
                    </table>
                    <p>Please review your quality scores and take necessary actions to improve where needed.</p>
                    <p>If you have any questions or need clarification, please reach out to your SME or Manager.</p>
                    <p>Regards,<br>{getpass.getuser()}</p>
                    </body>
                    </html>
                    """
                    
                    mail.HTMLBody = html_body
                    
                    # Send the email
                    mail.Send()
                    success_count += 1
                    
                except Exception as e:
                    print(f"Failed to send email to {user_id}: {str(e)}")
                    failure_count += 1
            
            # Show summary
            if success_count > 0:
                self.show_info("Email Sent", f"Successfully sent {success_count} emails. Failed: {failure_count}")
            else:
                self.show_error("Email Error", f"Failed to send any emails. Please check your Outlook configuration.")
                
        except Exception as e:
            import traceback
            traceback.print_exc()
            self.show_error("Error", f"Failed to send emails: {str(e)}")

if __name__ == '__main__':
    QualityApp().run()
