# Database Concurrency Control for Audits Automation

This document explains how to use the `db_manager.py` module to prevent database corruption when multiple users try to save data simultaneously.

## Overview

The `db_manager.py` module provides a `DatabaseManager` class that handles database connections with proper locking to prevent concurrent access issues. This is especially important when multiple users are trying to save data to the same database file.

## How to Use

1. Import the database manager at the top of your file:
   ```python
   from db_manager import db_manager
   ```

2. Use the database manager to get a connection with proper locking:
   ```python
   # For workflow database
   workflow_db_path = r"\\ant\dept-in\Digiflex\DF-Cataloghealth\Quality Products\Audits\Workflow\Workflow database.accdb"
   
   # For audits database
   audits_db_path = r"\\ant\dept-in\Digiflex\DF-Cataloghealth\Quality Products\Audits\QP-&-Tentpole-Audits\Audits.accdb"
   
   # Use the database manager to handle the database operations with proper locking
   with db_manager.get_connection(db_path) as conn:
       cursor = conn.cursor()
       # Execute your SQL queries here
       # ...
   ```

3. Handle timeout errors when the database is busy:
   ```python
   try:
       with db_manager.get_connection(db_path) as conn:
           cursor = conn.cursor()
           # Execute your SQL queries here
           # ...
   except TimeoutError:
       # Show a message to the user that the database is busy
       self.show_error("Database Busy", "The database is currently in use by another user. Please try again in a few moments.")
   except Exception as e:
       # Handle other errors
       self.show_error("Database Error", f"An error occurred: {str(e)}")
   ```

4. Use the `find_matching_table` method to find a table that matches a subprocess name:
   ```python
   table_name = db_manager.find_matching_table(subprocess, db_path)
   ```

## Example: Submitting Data to Database

```python
def submit_to_db(self, process, subprocess):
    # ... (collect data from form fields)
    
    try:
        # Find the matching table for the subprocess
        table_name = db_manager.find_matching_table(subprocess, self.audits_db_path)
        
        if table_name is None:
            self.show_error("Input Error", "Invalid subprocess selected.")
            return

        # Use the database manager to handle the database operations with proper locking
        with db_manager.get_connection(self.audits_db_path) as conn:
            cursor = conn.cursor()
            
            # Execute your SQL queries here
            # ...
            
    except TimeoutError:
        self.show_error("Database Busy", "The database is currently in use by another user. Please try again in a few moments.")
    except Exception as e:
        self.show_error("Database Error", f"An error occurred: {str(e)}")
```

## How It Works

1. The `DatabaseManager` class creates a lock file for each database file to ensure only one process can access it at a time.
2. When a connection is requested, it tries to acquire the lock with a timeout.
3. If the lock cannot be acquired within the timeout period, it raises a `TimeoutError`.
4. If the lock is acquired, it creates a connection and yields it to the caller.
5. When the connection is no longer needed, it releases the lock automatically.

This approach ensures that multiple users can safely access the database without corrupting it.