faiss/__init__.py,sha256=Ee7xdNka8Lk7UXXIQwDDbs4kIzTQt-IkX1GFjpp8Asw,13188
faiss/__pycache__/__init__.cpython-311.pyc,,
faiss/__pycache__/array_conversions.cpython-311.pyc,,
faiss/__pycache__/class_wrappers.cpython-311.pyc,,
faiss/__pycache__/extra_wrappers.cpython-311.pyc,,
faiss/__pycache__/gpu_wrappers.cpython-311.pyc,,
faiss/__pycache__/loader.cpython-311.pyc,,
faiss/__pycache__/setup.cpython-311.pyc,,
faiss/__pycache__/swigfaiss.cpython-311.pyc,,
faiss/__pycache__/swigfaiss_avx2.cpython-311.pyc,,
faiss/_swigfaiss.cp311-win_amd64.pyd,sha256=MJ2cF5hMAZQiGHyuoxa6UhMzCDe7y1qkh9Nsw8or2v0,11396608
faiss/_swigfaiss_avx2.cp311-win_amd64.pyd,sha256=PmyeIKfL266T_MilV6EkPZHx_fVjai8sb1rEoshRX9M,12402688
faiss/array_conversions.py,sha256=nVKx-YM9ewGZSqLKA9eJbatp8NJWNoPoZhP17UnQE_A,5962
faiss/class_wrappers.py,sha256=s0qvYCdkx4tYrvHEUTIYY7egoxtwt2K59jYznv4KR-U,50046
faiss/contrib/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
faiss/contrib/__pycache__/__init__.cpython-311.pyc,,
faiss/contrib/__pycache__/big_batch_search.cpython-311.pyc,,
faiss/contrib/__pycache__/client_server.cpython-311.pyc,,
faiss/contrib/__pycache__/clustering.cpython-311.pyc,,
faiss/contrib/__pycache__/datasets.cpython-311.pyc,,
faiss/contrib/__pycache__/evaluation.cpython-311.pyc,,
faiss/contrib/__pycache__/exhaustive_search.cpython-311.pyc,,
faiss/contrib/__pycache__/factory_tools.cpython-311.pyc,,
faiss/contrib/__pycache__/inspect_tools.cpython-311.pyc,,
faiss/contrib/__pycache__/ivf_tools.cpython-311.pyc,,
faiss/contrib/__pycache__/ondisk.cpython-311.pyc,,
faiss/contrib/__pycache__/rpc.cpython-311.pyc,,
faiss/contrib/__pycache__/torch_utils.cpython-311.pyc,,
faiss/contrib/__pycache__/vecs_io.cpython-311.pyc,,
faiss/contrib/big_batch_search.py,sha256=VBkUTYtr6tviDh14yOpUyb-lul4mSOqQyCEt16gz7Cs,18155
faiss/contrib/client_server.py,sha256=DjZH447C8vEfqAb8YMpRCJ7QWr1jkwixLdXNQWiR38s,2865
faiss/contrib/clustering.py,sha256=chssE7iEWDkYrOMNr8IVjTyZMuaSgtu0IOvow4BNDqc,13127
faiss/contrib/datasets.py,sha256=82uf5MsXHm_TsrdU5jYQ4NB8GWoCycRnGel5j9svVaU,12660
faiss/contrib/evaluation.py,sha256=v5tt5h1oo3WCcML98rIXx41BAG6lxI4bi-UEQa5HsUQ,15487
faiss/contrib/exhaustive_search.py,sha256=Mj4KVVlxkKb8M4QCOJLlzzabgcjvOCqR0Cw-AGETNZE,12747
faiss/contrib/factory_tools.py,sha256=vdj1uU7OhyRAJlGGOcpT3pczPzTzSwvsbTBHlzwfO0c,5234
faiss/contrib/inspect_tools.py,sha256=1jCGH5E8KUnoUW3d9PhA15g53saq8Hn38mFB_X3IKQo,3866
faiss/contrib/ivf_tools.py,sha256=aeFHfMwtgAf1gV9sNfodblfMC3eA8WUta9KXLQF3JWs,5022
faiss/contrib/ondisk.py,sha256=Oqg8c58L-GvkKzI7-UhGKp-DgPAhB9jEmFBHNOkTIow,2128
faiss/contrib/rpc.py,sha256=7Z3nhtQMe1pgTijm5T4PGXqqt3F-ONercYHidCdp_Rg,7563
faiss/contrib/torch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
faiss/contrib/torch/__pycache__/__init__.cpython-311.pyc,,
faiss/contrib/torch/__pycache__/clustering.cpython-311.pyc,,
faiss/contrib/torch/__pycache__/quantization.cpython-311.pyc,,
faiss/contrib/torch/clustering.py,sha256=Nmtnyg3-fGKLQ3YpYgSgxA_3cD8n4U_3mR_yvzYFMcU,1682
faiss/contrib/torch/quantization.py,sha256=9Y1usws9z-fP47NOFGRoqKMLzl3gnoOWM9IUbrN9mns,2892
faiss/contrib/torch_utils.py,sha256=nIb1-MrQe6uXu98ISgBFQq_MAe09E2cxH96spMZbhQ4,27590
faiss/contrib/vecs_io.py,sha256=mct87U7mZ2DMgl8HUkHZtwRiAhNk4dHLPfwHyKPiK4U,1443
faiss/extra_wrappers.py,sha256=GPqcaqmH4fvSgxO7aexwcPawNhKIMlJcwnWzZgffVHE,21151
faiss/gpu_wrappers.py,sha256=OReZ0N39JFV-xZHPI5whqd9BXcJk9ciyZwe7tanddTA,9489
faiss/loader.py,sha256=Lcod5wLhQt5sGAOAxMOIN5NKiDjnioD6_3UbRq_pcqE,5880
faiss/python_callbacks.h,sha256=07S0kar8j3vfs06egrhUIuyPvhSMECx6t_dGZtLpN7Q,2663
faiss/setup.py,sha256=OysBNdWZooziVlHyMcAg_3FKfqxfMbQz_vL4tpDJv3w,4945
faiss/swigfaiss.i,sha256=gZp2li8u24vN8Oj_HTcypBt7uAjzigKHUXncEoS23Tg,37321
faiss/swigfaiss.py,sha256=2Jr1vUBvdwZ7lE4gXcfjqBSLg5in6DZhuZk3sMdshr4,551093
faiss/swigfaiss_avx2.py,sha256=Vp485exsqGA1J1NBUCvr4U7RID_Ho2LLjVJmFigBbP0,573463
faiss_cpu-1.11.0.post1.dist-info/DELVEWHEEL,sha256=8V_bRzVHOUZuqyqeRCitoHv4oowDgkLacNPMO_s8jps,415
faiss_cpu-1.11.0.post1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
faiss_cpu-1.11.0.post1.dist-info/METADATA,sha256=VX2dWiBnDF5CXg951r47EDswjxm8eOod7ylB6WSFt7Y,5104
faiss_cpu-1.11.0.post1.dist-info/RECORD,,
faiss_cpu-1.11.0.post1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
faiss_cpu-1.11.0.post1.dist-info/WHEEL,sha256=JLOMsP7F5qtkAkINx5UnzbFguf8CqZeraV8o04b0I8I,101
faiss_cpu-1.11.0.post1.dist-info/licenses/LICENSE,sha256=SJxdqdQ2exFo_NAY7cECLSMQJiwecF3aA-fbVYHJTC4,1092
faiss_cpu-1.11.0.post1.dist-info/top_level.txt,sha256=nr2S-1YAhqAuAlFJukntDvRGXfI1KmwnUNs_iFlNiig,6
faiss_cpu.libs/flang-d38962844214aa9b06fc3989f9adae5b.dll,sha256=DqRjd8xOnwYBNhPBTZoOuqJVBV8TxxKsSenG9IKvdWk,1679360
faiss_cpu.libs/flangrti-5bbaf6aff159e72f9b015d5bc31c7584.dll,sha256=_K8aG6sqpzbWW2bhYtllkVT1fyBBps-iCcARo5CyZUo,50688
faiss_cpu.libs/libomp-23a56489607d34c06a920746ad984011.dll,sha256=z0O154uGZMc0NmrZm756Nn_lDThE8mfJIPHmKA08Vr4,612864
faiss_cpu.libs/libomp140.x86_64-3c9617ffad8b1610b27252d364ba580c.dll,sha256=mrHLeH5SsqNhM4mcAcHbH4dgZ9FHHNIk6thfQKgVK5k,634936
faiss_cpu.libs/msvcp140-50f58d1dad626b3f517fc106cd05ee4a.dll,sha256=uZ6yikcTERE_XEEJyzxGPznP2b2zsH9wYgTe3dtFFqE,575568
faiss_cpu.libs/openblas-e5d6a39dd93158b9c6576c0d5034b958.dll,sha256=Q3pA49xMhr9aN2JLAy0Urpho_VjyZwMMNdVEfrZDrhE,28463104
