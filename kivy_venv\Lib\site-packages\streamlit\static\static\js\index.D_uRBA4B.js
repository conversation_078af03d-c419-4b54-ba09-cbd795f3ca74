import{n as g,r as S,aD as E,C as p,j as i,aC as y,l as L}from"./index.C1z8KpLA.js";import{w as W,E as b}from"./withFullScreenWrapper.Ov13692o.js";import{S as u,T as M}from"./Toolbar.D9RUZv9G.js";const O=g("div",{target:"evl31sl0"})(({theme:e})=>({display:"flex",flexDirection:"row",flexWrap:"wrap",rowGap:e.spacing.lg,maxWidth:"100%",width:"fit-content"})),T=g("div",{target:"evl31sl1"})(({theme:e})=>({display:"flex",flexDirection:"column",alignItems:"stretch",width:"auto",flexGrow:0,">img":{borderRadius:e.radii.default}})),F=g("div",{target:"evl31sl2"})(({theme:e})=>({textAlign:"center",marginTop:e.spacing.xs,wordWrap:"break-word",padding:e.spacing.threeXS})),j=L.getLogger("ImageList");var x;(function(e){e[e.OriginalWidth=-1]="OriginalWidth",e[e.ColumnWidth=-2]="ColumnWidth",e[e.AutoWidth=-3]="AutoWidth",e[e.MinImageOrContainer=-4]="MinImageOrContainer",e[e.MaxImageOrContainer=-5]="MaxImageOrContainer"})(x||(x={}));function A({element:e,endpoints:c,disableFullscreenMode:C}){const{expanded:o,width:f,height:s,expand:w,collapse:I}=E(b),m=f||0;let n;const r=e.width;if([-1,-3,-4].includes(r))n=void 0;else if([-2,-5].includes(r))n=m;else if(r>0)n=r;else throw Error(`Invalid image width: ${r}`);const t={};s&&o?(t.maxHeight=s,t.objectFit="contain",t.width="100%"):(t.width=n??"100%",t.maxWidth="100%");const h=l=>{const a=l.currentTarget.src;j.error(`Client Error: Image source error - ${a}`),c.sendClientErrorToHost("Image","Image source failed to load","onerror triggered",a)};return p(u,{width:m,height:s,useContainerWidth:o,topCentered:!0,children:[i(M,{target:u,isFullScreen:o,onExpand:w,onCollapse:I,disableFullscreenMode:C}),i(O,{className:"stImage","data-testid":"stImage",children:e.imgs.map((l,a)=>{const d=l;return p(T,{"data-testid":"stImageContainer",children:[i("img",{style:t,src:c.buildMediaURL(d.url),alt:a.toString(),onError:h}),d.caption&&i(F,{"data-testid":"stImageCaption",style:t,children:i(y,{source:d.caption,allowHTML:!1,isCaption:!0,isLabel:!0})})]},a)})})]})}const D=W(A),$=S.memo(D);export{$ as default};
