import{b_ as de,cv as ce,cw as pe,cx as fe,cy as ge,cz as ye,cc as me,bw as ve,bR as be,cA as he,cd as Se,cB as Oe,d as K,cC as Ce,n as Ie,r as J,M as U,z as Pe,C as $e,j as z,bs as _e,bG as Te,bt as Ee,bb as Re,bu as we,bZ as De}from"./index.C1z8KpLA.js";import{a as xe}from"./useBasicWidgetState.zXY9CjFS.js";import{T as Ae}from"./timepicker.w4XhAenH.js";import"./FormClearHelper.B67tgll0.js";import"./possibleConstructorReturn.nNhsvgRd.js";import"./createSuper.B4oGDYRm.js";var We=de.Consumer;const Be=Object.freeze(Object.defineProperty({__proto__:null,ThemeConsumer:We,ThemeProvider:ce,createThemedStyled:pe,createThemedUseStyletron:fe,createThemedWithStyle:ge,expandBorderStyles:ye,hexToRgb:me,styled:ve,useStyletron:be,withStyle:he,withWrapper:Se},Symbol.toStringTag,{value:"Module"}));var C={};const Y=Oe(Be);var w={},X;function Ne(){if(X)return w;X=1,Object.defineProperty(w,"__esModule",{value:!0}),w.STATE_CHANGE_TYPE=w.SIZE=w.ENHANCER_POSITION=w.CUSTOM_INPUT_TYPE=w.ADJOINED=void 0;var g={change:"change"};w.STATE_CHANGE_TYPE=g;var c={textarea:"textarea"};w.CUSTOM_INPUT_TYPE=c;var h={none:"none",left:"left",right:"right",both:"both"};w.ADJOINED=h;var m={mini:"mini",default:"default",compact:"compact",large:"large"};w.SIZE=m;var b={start:"start",end:"end"};return w.ENHANCER_POSITION=b,w}var q={},A={},k={},Q;function Me(){if(Q)return k;Q=1,Object.defineProperty(k,"__esModule",{value:!0}),k.default=c;function g(m){"@babel/helpers - typeof";return g=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(b){return typeof b}:function(b){return b&&typeof Symbol=="function"&&b.constructor===Symbol&&b!==Symbol.prototype?"symbol":typeof b},g(m)}function c(m){m=m||{};for(var b=arguments.length<=1?0:arguments.length-1,v,y,f=0;f<b;f++){v=(f+1<1||arguments.length<=f+1?void 0:arguments[f+1])||{};for(var S in v)g(v[S])!==void 0&&(y=v[S],h(y)?m[S]=c(m[S]||Array.isArray(y)&&[]||{},y):m[S]=y)}return m}function h(m){return Array.isArray(m)||{}.toString.call(m)=="[object Object]"}return k}var ee;function ae(){if(ee)return A;ee=1,Object.defineProperty(A,"__esModule",{value:!0}),A.getOverride=T,A.getOverrideProps=_,A.getOverrides=L,A.mergeConfigurationOverrides=M,A.mergeOverride=x,A.mergeOverrides=N,A.toObjectOverride=D,A.useOverrides=F;var g=v(K()),c=Ce(),h=m(Me());function m(i){return i&&i.__esModule?i:{default:i}}function b(i){if(typeof WeakMap!="function")return null;var t=new WeakMap,u=new WeakMap;return(b=function(r){return r?u:t})(i)}function v(i,t){if(i&&i.__esModule)return i;if(i===null||P(i)!=="object"&&typeof i!="function")return{default:i};var u=b(t);if(u&&u.has(i))return u.get(i);var s={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in i)if(a!=="default"&&Object.prototype.hasOwnProperty.call(i,a)){var l=r?Object.getOwnPropertyDescriptor(i,a):null;l&&(l.get||l.set)?Object.defineProperty(s,a,l):s[a]=i[a]}return s.default=i,u&&u.set(i,s),s}function y(){return y=Object.assign?Object.assign.bind():function(i){for(var t=1;t<arguments.length;t++){var u=arguments[t];for(var s in u)Object.prototype.hasOwnProperty.call(u,s)&&(i[s]=u[s])}return i},y.apply(this,arguments)}function f(i,t){var u=Object.keys(i);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(i);t&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(i,r).enumerable})),u.push.apply(u,s)}return u}function S(i){for(var t=1;t<arguments.length;t++){var u=arguments[t]!=null?arguments[t]:{};t%2?f(Object(u),!0).forEach(function(s){$(i,s,u[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(u)):f(Object(u)).forEach(function(s){Object.defineProperty(i,s,Object.getOwnPropertyDescriptor(u,s))})}return i}function $(i,t,u){return t in i?Object.defineProperty(i,t,{value:u,enumerable:!0,configurable:!0,writable:!0}):i[t]=u,i}function P(i){"@babel/helpers - typeof";return P=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},P(i)}function T(i){return(0,c.isValidElementType)(i)?i:i&&P(i)==="object"?i.component:i}function _(i){return i&&P(i)==="object"?P(i.props)==="object"?S(S({},i.props),{},{$style:i.style}):{$style:i.style}:{}}function D(i){return(0,c.isValidElementType)(i)?{component:i}:i||{}}function L(i,t){var u=T(i)||t;if(i&&P(i)==="object"&&typeof i.props=="function"){var s=g.forwardRef(function(a,l){var n=i.props(a),e=_(S(S({},i),{},{props:n}));return g.createElement(u,y({ref:l},e))});return s.displayName=u.displayName,[s,{}]}var r=_(i);return[u,r]}function N(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},u=Object.assign({},i,t),s=Object.keys(u);return s.reduce(function(r,a){return r[a]=x(D(i[a]),D(t[a])),r},{})}function x(i,t){var u=S(S({},i),t);return i.props&&t.props&&(u.props=M(i.props,t.props)),i.style&&t.style&&(u.style=M(i.style,t.style)),u}function M(i,t){return P(i)==="object"&&P(t)==="object"?(0,h.default)({},i,t):function(){return(0,h.default)({},typeof i=="function"?i.apply(void 0,arguments):i,typeof t=="function"?t.apply(void 0,arguments):t)}}function F(i){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return g.useMemo(function(){return Object.keys(i).reduce(function(u,s){return u[s]=L(t[s],i[s]),u},{})},[t])}return A}var j={},Z={},te;function Le(){if(te)return Z;te=1,Object.defineProperty(Z,"__esModule",{value:!0}),Z.Svg=void 0,Z.getSvgStyles=c;var g=Y;function c(m){var b=m.$theme,v=m.$size,y=m.$color,f=b.sizing.scale600;v&&(b.sizing[v]?f=b.sizing[v]:typeof v=="number"?f="".concat(v,"px"):f=v);var S="currentColor";return y&&(b.colors[y]?S=b.colors[y]:S=y),{display:"inline-block",fill:S,color:S,height:f,width:f}}var h=(0,g.styled)("svg",c);return Z.Svg=h,h.displayName="Svg",h.displayName="Svg",Z}var H={},re;function Fe(){if(re)return H;re=1,Object.defineProperty(H,"__esModule",{value:!0}),H.default=g;function g(c){var h={};for(var m in c)m[0]!=="$"&&(h[m]=c[m]);return h}return H}var ne;function ze(){if(ne)return j;ne=1;function g(r){"@babel/helpers - typeof";return g=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(a){return typeof a}:function(a){return a&&typeof Symbol=="function"&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},g(r)}Object.defineProperty(j,"__esModule",{value:!0}),j.default=void 0;var c=S(K()),h=ae(),m=Le(),b=y(Fe()),v=["children","title","size","color","overrides"];function y(r){return r&&r.__esModule?r:{default:r}}function f(r){if(typeof WeakMap!="function")return null;var a=new WeakMap,l=new WeakMap;return(f=function(e){return e?l:a})(r)}function S(r,a){if(r&&r.__esModule)return r;if(r===null||g(r)!=="object"&&typeof r!="function")return{default:r};var l=f(a);if(l&&l.has(r))return l.get(r);var n={},e=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in r)if(o!=="default"&&Object.prototype.hasOwnProperty.call(r,o)){var p=e?Object.getOwnPropertyDescriptor(r,o):null;p&&(p.get||p.set)?Object.defineProperty(n,o,p):n[o]=r[o]}return n.default=r,l&&l.set(r,n),n}function $(){return $=Object.assign?Object.assign.bind():function(r){for(var a=1;a<arguments.length;a++){var l=arguments[a];for(var n in l)Object.prototype.hasOwnProperty.call(l,n)&&(r[n]=l[n])}return r},$.apply(this,arguments)}function P(r,a){var l=Object.keys(r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(r);a&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})),l.push.apply(l,n)}return l}function T(r){for(var a=1;a<arguments.length;a++){var l=arguments[a]!=null?arguments[a]:{};a%2?P(Object(l),!0).forEach(function(n){_(r,n,l[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(l)):P(Object(l)).forEach(function(n){Object.defineProperty(r,n,Object.getOwnPropertyDescriptor(l,n))})}return r}function _(r,a,l){return a in r?Object.defineProperty(r,a,{value:l,enumerable:!0,configurable:!0,writable:!0}):r[a]=l,r}function D(r,a){return F(r)||M(r,a)||N(r,a)||L()}function L(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function N(r,a){if(r){if(typeof r=="string")return x(r,a);var l=Object.prototype.toString.call(r).slice(8,-1);if(l==="Object"&&r.constructor&&(l=r.constructor.name),l==="Map"||l==="Set")return Array.from(r);if(l==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(l))return x(r,a)}}function x(r,a){(a==null||a>r.length)&&(a=r.length);for(var l=0,n=new Array(a);l<a;l++)n[l]=r[l];return n}function M(r,a){var l=r==null?null:typeof Symbol<"u"&&r[Symbol.iterator]||r["@@iterator"];if(l!=null){var n=[],e=!0,o=!1,p,d;try{for(l=l.call(r);!(e=(p=l.next()).done)&&(n.push(p.value),!(a&&n.length===a));e=!0);}catch(O){o=!0,d=O}finally{try{!e&&l.return!=null&&l.return()}finally{if(o)throw d}}return n}}function F(r){if(Array.isArray(r))return r}function i(r,a){if(r==null)return{};var l=t(r,a),n,e;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(r);for(e=0;e<o.length;e++)n=o[e],!(a.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(r,n)&&(l[n]=r[n])}return l}function t(r,a){if(r==null)return{};var l={},n=Object.keys(r),e,o;for(o=0;o<n.length;o++)e=n[o],!(a.indexOf(e)>=0)&&(l[e]=r[e]);return l}var u=function(a,l){var n=a.children,e=a.title,o=a.size,p=a.color,d=a.overrides,O=d===void 0?{}:d,I=i(a,v),R=(0,h.getOverrides)(O.Svg,m.Svg),W=D(R,2),E=W[0],B=W[1],V=E.__STYLETRON__?T(T({title:e,$color:p,$size:o},I),B):T(T({title:e,color:p,size:o},(0,b.default)(I)),(0,b.default)(B));return c.createElement(E,$({"data-baseweb":"icon",ref:l},V),e?c.createElement("title",null,e):null,n)},s=c.forwardRef(u);return j.default=s,j}var oe;function Ze(){if(oe)return q;oe=1;function g(t){"@babel/helpers - typeof";return g=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(u){return typeof u}:function(u){return u&&typeof Symbol=="function"&&u.constructor===Symbol&&u!==Symbol.prototype?"symbol":typeof u},g(t)}Object.defineProperty(q,"__esModule",{value:!0}),q.default=void 0;var c=S(K()),h=Y,m=ae(),b=y(ze()),v=["title","size","color","overrides"];function y(t){return t&&t.__esModule?t:{default:t}}function f(t){if(typeof WeakMap!="function")return null;var u=new WeakMap,s=new WeakMap;return(f=function(a){return a?s:u})(t)}function S(t,u){if(t&&t.__esModule)return t;if(t===null||g(t)!=="object"&&typeof t!="function")return{default:t};var s=f(u);if(s&&s.has(t))return s.get(t);var r={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in t)if(l!=="default"&&Object.prototype.hasOwnProperty.call(t,l)){var n=a?Object.getOwnPropertyDescriptor(t,l):null;n&&(n.get||n.set)?Object.defineProperty(r,l,n):r[l]=t[l]}return r.default=t,s&&s.set(t,r),r}function $(){return $=Object.assign?Object.assign.bind():function(t){for(var u=1;u<arguments.length;u++){var s=arguments[u];for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(t[r]=s[r])}return t},$.apply(this,arguments)}function P(t,u){if(t==null)return{};var s=T(t,u),r,a;if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(t);for(a=0;a<l.length;a++)r=l[a],!(u.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(s[r]=t[r])}return s}function T(t,u){if(t==null)return{};var s={},r=Object.keys(t),a,l;for(l=0;l<r.length;l++)a=r[l],!(u.indexOf(a)>=0)&&(s[a]=t[a]);return s}function _(t,u){return M(t)||x(t,u)||L(t,u)||D()}function D(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function L(t,u){if(t){if(typeof t=="string")return N(t,u);var s=Object.prototype.toString.call(t).slice(8,-1);if(s==="Object"&&t.constructor&&(s=t.constructor.name),s==="Map"||s==="Set")return Array.from(t);if(s==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s))return N(t,u)}}function N(t,u){(u==null||u>t.length)&&(u=t.length);for(var s=0,r=new Array(u);s<u;s++)r[s]=t[s];return r}function x(t,u){var s=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(s!=null){var r=[],a=!0,l=!1,n,e;try{for(s=s.call(t);!(a=(n=s.next()).done)&&(r.push(n.value),!(u&&r.length===u));a=!0);}catch(o){l=!0,e=o}finally{try{!a&&s.return!=null&&s.return()}finally{if(l)throw e}}return r}}function M(t){if(Array.isArray(t))return t}function F(t,u){var s=(0,h.useStyletron)(),r=_(s,2),a=r[1],l=t.title,n=l===void 0?"Delete Alt":l,e=t.size,o=t.color,p=t.overrides,d=p===void 0?{}:p,O=P(t,v),I=(0,m.mergeOverride)({component:a.icons&&a.icons.DeleteAlt?a.icons.DeleteAlt:null},d&&d.Svg?(0,m.toObjectOverride)(d.Svg):{});return c.createElement(b.default,$({viewBox:"0 0 24 24",ref:u,title:n,size:e,color:o,overrides:{Svg:I}},O),c.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 20C16.4183 20 20 16.4183 20 12C20 7.58173 16.4183 4 12 4C7.58173 4 4 7.58173 4 12C4 16.4183 7.58173 20 12 20ZM10.0303 8.96967C9.73743 8.67679 9.26257 8.67679 8.96967 8.96967C8.67676 9.26257 8.67676 9.73743 8.96967 10.0303L10.9393 12L8.96967 13.9697C8.67676 14.2626 8.67676 14.7374 8.96967 15.0303C9.26257 15.3232 9.73743 15.3232 10.0303 15.0303L12 13.0607L13.9697 15.0303C14.2626 15.3232 14.7374 15.3232 15.0303 15.0303C15.3232 14.7374 15.3232 14.2626 15.0303 13.9697L13.0607 12L15.0303 10.0303C15.3232 9.73743 15.3232 9.26257 15.0303 8.96967C14.7374 8.67679 14.2626 8.67679 13.9697 8.96967L12 10.9393L10.0303 8.96967Z"}))}var i=c.forwardRef(F);return q.default=i,q}var ie;function qe(){if(ie)return C;ie=1,Object.defineProperty(C,"__esModule",{value:!0}),C.getRootStyles=C.getInputStyles=C.getInputContainerStyles=C.StyledMaskToggleButton=C.StyledClearIconContainer=C.StyledClearIcon=C.Root=C.InputEnhancer=C.InputContainer=C.Input=void 0;var g=Y,c=Ne(),h=m(Ze());function m(n){return n&&n.__esModule?n:{default:n}}function b(n,e){var o=Object.keys(n);if(Object.getOwnPropertySymbols){var p=Object.getOwnPropertySymbols(n);e&&(p=p.filter(function(d){return Object.getOwnPropertyDescriptor(n,d).enumerable})),o.push.apply(o,p)}return o}function v(n){for(var e=1;e<arguments.length;e++){var o=arguments[e]!=null?arguments[e]:{};e%2?b(Object(o),!0).forEach(function(p){y(n,p,o[p])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(o)):b(Object(o)).forEach(function(p){Object.defineProperty(n,p,Object.getOwnPropertyDescriptor(o,p))})}return n}function y(n,e,o){return e in n?Object.defineProperty(n,e,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[e]=o,n}var f=(0,g.styled)("button",function(n){var e,o=n.$theme,p=n.$size,d=n.$isFocusVisible,O=(e={},y(e,c.SIZE.mini,o.sizing.scale400),y(e,c.SIZE.compact,o.sizing.scale400),y(e,c.SIZE.default,o.sizing.scale300),y(e,c.SIZE.large,o.sizing.scale200),e)[p];return{display:"flex",alignItems:"center",borderTopStyle:"none",borderBottomStyle:"none",borderLeftStyle:"none",borderRightStyle:"none",background:"none",paddingLeft:O,paddingRight:O,outline:d?"solid 3px ".concat(o.colors.accent):"none",color:o.colors.contentPrimary}});C.StyledMaskToggleButton=f,f.displayName="StyledMaskToggleButton",f.displayName="StyledMaskToggleButton";var S=(0,g.styled)("div",function(n){var e,o=n.$alignTop,p=o===void 0?!1:o,d=n.$size,O=n.$theme,I=(e={},y(e,c.SIZE.mini,O.sizing.scale200),y(e,c.SIZE.compact,O.sizing.scale200),y(e,c.SIZE.default,O.sizing.scale100),y(e,c.SIZE.large,O.sizing.scale0),e)[d];return{display:"flex",alignItems:p?"flex-start":"center",paddingLeft:I,paddingRight:I,paddingTop:p?O.sizing.scale500:"0px",color:O.colors.contentPrimary}});C.StyledClearIconContainer=S,S.displayName="StyledClearIconContainer",S.displayName="StyledClearIconContainer";var $=(0,g.styled)(h.default,function(n){var e=n.$theme,o=n.$isFocusVisible;return{cursor:"pointer",outline:o?"solid 3px ".concat(e.colors.accent):"none"}});C.StyledClearIcon=$,$.displayName="StyledClearIcon",$.displayName="StyledClearIcon";function P(n,e){var o;return(o={},y(o,c.SIZE.mini,{paddingTop:e.scale100,paddingBottom:e.scale100,paddingLeft:e.scale550,paddingRight:e.scale550}),y(o,c.SIZE.compact,{paddingTop:e.scale200,paddingBottom:e.scale200,paddingLeft:e.scale550,paddingRight:e.scale550}),y(o,c.SIZE.default,{paddingTop:e.scale400,paddingBottom:e.scale400,paddingLeft:e.scale550,paddingRight:e.scale550}),y(o,c.SIZE.large,{paddingTop:e.scale550,paddingBottom:e.scale550,paddingLeft:e.scale550,paddingRight:e.scale550}),o)[n]}function T(n,e,o,p,d){var O=n===c.ADJOINED.both||n===c.ADJOINED.left&&p!=="rtl"||n===c.ADJOINED.right&&p==="rtl"||d&&p==="rtl",I=n===c.ADJOINED.both||n===c.ADJOINED.right&&p!=="rtl"||n===c.ADJOINED.left&&p==="rtl"||d&&p!=="rtl";return{paddingLeft:O?o.scale550:"0px",paddingRight:I?o.scale550:"0px"}}function _(n,e){var o;return(o={},y(o,c.SIZE.mini,e.font100),y(o,c.SIZE.compact,e.font200),y(o,c.SIZE.default,e.font300),y(o,c.SIZE.large,e.font400),o)[n]}function D(n,e,o){var p=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1,d=arguments.length>4?arguments[4]:void 0;return n?{borderLeftColor:d.inputFillDisabled,borderRightColor:d.inputFillDisabled,borderTopColor:d.inputFillDisabled,borderBottomColor:d.inputFillDisabled,backgroundColor:d.inputFillDisabled}:e?{borderLeftColor:d.borderSelected,borderRightColor:d.borderSelected,borderTopColor:d.borderSelected,borderBottomColor:d.borderSelected,backgroundColor:d.inputFillActive}:o?{borderLeftColor:d.inputBorderError,borderRightColor:d.inputBorderError,borderTopColor:d.inputBorderError,borderBottomColor:d.inputBorderError,backgroundColor:d.inputFillError}:p?{borderLeftColor:d.inputBorderPositive,borderRightColor:d.inputBorderPositive,borderTopColor:d.inputBorderPositive,borderBottomColor:d.inputBorderPositive,backgroundColor:d.inputFillPositive}:{borderLeftColor:d.inputBorder,borderRightColor:d.inputBorder,borderTopColor:d.inputBorder,borderBottomColor:d.inputBorder,backgroundColor:d.inputFill}}function L(n,e){var o=e.inputBorderRadius;return n===c.SIZE.mini&&(o=e.inputBorderRadiusMini),{borderTopLeftRadius:o,borderBottomLeftRadius:o,borderTopRightRadius:o,borderBottomRightRadius:o}}var N=function(e){var o=e.$isFocused,p=e.$adjoined,d=e.$error,O=e.$disabled,I=e.$positive,R=e.$size,W=e.$theme,E=e.$theme,B=E.borders,V=E.colors,le=E.sizing,ue=E.typography,G=E.animation,se=e.$hasIconTrailing;return v(v(v(v({boxSizing:"border-box",display:"flex",overflow:"hidden",width:"100%",borderLeftWidth:"2px",borderRightWidth:"2px",borderTopWidth:"2px",borderBottomWidth:"2px",borderLeftStyle:"solid",borderRightStyle:"solid",borderTopStyle:"solid",borderBottomStyle:"solid",transitionProperty:"border",transitionDuration:G.timing200,transitionTimingFunction:G.easeOutCurve},L(R,B)),_(R,ue)),D(O,o,d,I,V)),T(p,R,le,W.direction,se))};C.getRootStyles=N;var x=(0,g.styled)("div",N);C.Root=x,x.displayName="Root",x.displayName="Root";function M(n,e){var o;return(o={},y(o,c.SIZE.mini,{paddingRight:e.scale400,paddingLeft:e.scale400}),y(o,c.SIZE.compact,{paddingRight:e.scale400,paddingLeft:e.scale400}),y(o,c.SIZE.default,{paddingRight:e.scale300,paddingLeft:e.scale300}),y(o,c.SIZE.large,{paddingRight:e.scale200,paddingLeft:e.scale200}),o)[n]}function F(n,e,o,p,d){return n?{color:d.inputEnhancerTextDisabled,backgroundColor:d.inputFillDisabled}:e?{color:d.contentPrimary,backgroundColor:d.inputFillActive}:o?{color:d.contentPrimary,backgroundColor:d.inputFillError}:p?{color:d.contentPrimary,backgroundColor:d.inputFillPositive}:{color:d.contentPrimary,backgroundColor:d.inputFill}}var i=(0,g.styled)("div",function(n){var e=n.$size,o=n.$disabled,p=n.$isFocused,d=n.$error,O=n.$positive,I=n.$theme,R=I.colors,W=I.sizing,E=I.typography,B=I.animation;return v(v(v({display:"flex",alignItems:"center",justifyContent:"center",transitionProperty:"color, background-color",transitionDuration:B.timing200,transitionTimingFunction:B.easeOutCurve},_(e,E)),M(e,W)),F(o,p,d,O,R))});C.InputEnhancer=i,i.displayName="InputEnhancer",i.displayName="InputEnhancer";function t(n,e,o,p,d){return n?{color:d.inputTextDisabled,backgroundColor:d.inputFillDisabled}:e?{color:d.contentPrimary,backgroundColor:d.inputFillActive}:o?{color:d.contentPrimary,backgroundColor:d.inputFillError}:p?{color:d.contentPrimary,backgroundColor:d.inputFillPositive}:{color:d.contentPrimary,backgroundColor:d.inputFill}}var u=function(e){var o=e.$isFocused,p=e.$error,d=e.$disabled,O=e.$positive,I=e.$size,R=e.$theme,W=R.colors,E=R.typography,B=R.animation;return v(v({display:"flex",width:"100%",transitionProperty:"background-color",transitionDuration:B.timing200,transitionTimingFunction:B.easeOutCurve},_(I,E)),t(d,o,p,O,W))};C.getInputContainerStyles=u;var s=(0,g.styled)("div",u);C.InputContainer=s,s.displayName="InputContainer",s.displayName="InputContainer";function r(n,e,o,p){return n?{color:p.inputTextDisabled,"-webkit-text-fill-color":p.inputTextDisabled,caretColor:p.contentPrimary,"::placeholder":{color:p.inputPlaceholderDisabled}}:{color:p.contentPrimary,caretColor:p.contentPrimary,"::placeholder":{color:p.inputPlaceholder}}}var a=function(e){var o=e.$disabled,p=e.$isFocused,d=e.$error,O=e.$size,I=e.$theme,R=I.colors,W=I.sizing,E=I.typography;return v(v(v({boxSizing:"border-box",backgroundColor:"transparent",borderLeftWidth:0,borderRightWidth:0,borderTopWidth:0,borderBottomWidth:0,borderLeftStyle:"none",borderRightStyle:"none",borderTopStyle:"none",borderBottomStyle:"none",outline:"none",width:"100%",minWidth:0,maxWidth:"100%",cursor:o?"not-allowed":"text",margin:"0",paddingTop:"0",paddingBottom:"0",paddingLeft:"0",paddingRight:"0"},_(O,E)),P(O,W)),r(o,p,d,R))};C.getInputStyles=a;var l=(0,g.styled)("input",a);return C.Input=l,l.displayName="Input",l.displayName="Input",C}var je=qe();const ke=Ie("div",{target:"e1ybf61d0"})({position:"absolute",top:"50%",right:"2.05em"});function He({disabled:g,element:c,widgetMgr:h,fragmentId:m}){var T;const[b,v]=xe({getStateFromWidgetMgr:Ve,getDefaultStateFromProto:Ue,getCurrStateFromProto:Je,updateWidgetMgrState:Ke,element:c,widgetMgr:h,fragmentId:m}),y=U(c.default)&&!g,f=Pe(),S={Select:{props:{disabled:g,overrides:{ControlContainer:{style:{height:f.sizes.minElementHeight,borderLeftWidth:f.sizes.borderWidth,borderRightWidth:f.sizes.borderWidth,borderTopWidth:f.sizes.borderWidth,borderBottomWidth:f.sizes.borderWidth}},IconsContainer:{style:()=>({paddingRight:f.spacing.sm})},ValueContainer:{style:()=>({lineHeight:f.lineHeights.inputWidget,paddingRight:f.spacing.sm,paddingLeft:f.spacing.md,paddingBottom:f.spacing.sm,paddingTop:f.spacing.sm})},SingleValue:{props:{"data-testid":"stTimeInputTimeDisplay"}},Dropdown:{style:()=>({paddingTop:f.spacing.none,paddingBottom:f.spacing.none,boxShadow:"none",maxHeight:f.sizes.maxDropdownHeight})},DropdownListItem:{style:()=>({paddingRight:f.spacing.lg,paddingLeft:f.spacing.lg,paddingTop:f.spacing.sm,paddingBottom:f.spacing.sm})},Popover:{props:{overrides:{Body:{style:()=>({marginTop:f.spacing.px})}}}},SelectArrow:{component:De,props:{overrides:{Svg:{style:()=>({width:f.iconSizes.xl,height:f.iconSizes.xl})}}}}}}}},$=J.useCallback(_=>{const D=_===null?null:Ye(_);v({value:D,fromUi:!0})},[v]),P=J.useCallback(()=>{$(null)},[$]);return $e("div",{className:"stTimeInput","data-testid":"stTimeInput",children:[z(we,{label:c.label,disabled:g,labelVisibility:_e((T=c.labelVisibility)==null?void 0:T.value),children:c.help&&z(Te,{children:z(Ee,{content:c.help,placement:Re.TOP_RIGHT})})}),z(Ae,{format:"24",step:c.step?Number(c.step):900,value:U(b)?void 0:Ge(b),onChange:$,overrides:S,nullable:y,creatable:!0,"aria-label":c.label}),y&&!U(b)&&z(ke,{onClick:P,"data-testid":"stTimeInputClearButton",children:z(je.StyledClearIcon,{overrides:{Svg:{style:{color:f.colors.darkGray,padding:f.spacing.threeXS,height:f.sizes.clearIconSize,width:f.sizes.clearIconSize,":hover":{fill:f.colors.bodyText}}}},$isFocusVisible:!1})})]})}function Ve(g,c){return g.getStringValue(c)??null}function Ue(g){return g.default??null}function Je(g){return g.value??null}function Ke(g,c,h,m){c.setStringValue(g,h.value,{fromUi:h.fromUi},m)}function Ye(g){const c=g.getHours().toString().padStart(2,"0"),h=g.getMinutes().toString().padStart(2,"0");return`${c}:${h}`}function Ge(g){if(g===null)return null;const[c,h]=g.split(":").map(Number),m=new Date;return m.setHours(c),m.setMinutes(h),m}const ot=J.memo(He);export{ot as default};
