import sys
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ._ysizemode import <PERSON><PERSON><PERSON>modeV<PERSON><PERSON><PERSON>
    from ._yref import <PERSON>re<PERSON><PERSON><PERSON><PERSON><PERSON>
    from ._yanchor import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    from ._y1shift import Y1ShiftValida<PERSON>
    from ._y1 import Y1V<PERSON><PERSON><PERSON>
    from ._y0shift import Y0ShiftV<PERSON>da<PERSON>
    from ._y0 import Y0V<PERSON><PERSON><PERSON>
    from ._xsizemode import XsizemodeVali<PERSON>tor
    from ._xref import XrefV<PERSON>da<PERSON>
    from ._xanchor import XanchorValida<PERSON>
    from ._x1shift import X1ShiftValidator
    from ._x1 import X1Validator
    from ._x0shift import X0ShiftValidator
    from ._x0 import X0Valida<PERSON>
    from ._visible import VisibleValidator
    from ._type import TypeValidator
    from ._templateitemname import TemplateitemnameValidator
    from ._showlegend import ShowlegendValidator
    from ._path import PathValidator
    from ._opacity import OpacityValidator
    from ._name import NameValidator
    from ._line import LineValida<PERSON>
    from ._legendwidth import <PERSON>widthVali<PERSON><PERSON>
    from ._legendrank import <PERSON>rankValidator
    from ._legendgrouptitle import LegendgrouptitleValidator
    from ._legendgroup import LegendgroupValidator
    from ._legend import LegendValidator
    from ._layer import LayerValidator
    from ._label import LabelValidator
    from ._fillrule import FillruleValidator
    from ._fillcolor import FillcolorValidator
    from ._editable import EditableValidator
else:
    from _plotly_utils.importers import relative_import

    __all__, __getattr__, __dir__ = relative_import(
        __name__,
        [],
        [
            "._ysizemode.YsizemodeValidator",
            "._yref.YrefValidator",
            "._yanchor.YanchorValidator",
            "._y1shift.Y1ShiftValidator",
            "._y1.Y1Validator",
            "._y0shift.Y0ShiftValidator",
            "._y0.Y0Validator",
            "._xsizemode.XsizemodeValidator",
            "._xref.XrefValidator",
            "._xanchor.XanchorValidator",
            "._x1shift.X1ShiftValidator",
            "._x1.X1Validator",
            "._x0shift.X0ShiftValidator",
            "._x0.X0Validator",
            "._visible.VisibleValidator",
            "._type.TypeValidator",
            "._templateitemname.TemplateitemnameValidator",
            "._showlegend.ShowlegendValidator",
            "._path.PathValidator",
            "._opacity.OpacityValidator",
            "._name.NameValidator",
            "._line.LineValidator",
            "._legendwidth.LegendwidthValidator",
            "._legendrank.LegendrankValidator",
            "._legendgrouptitle.LegendgrouptitleValidator",
            "._legendgroup.LegendgroupValidator",
            "._legend.LegendValidator",
            "._layer.LayerValidator",
            "._label.LabelValidator",
            "._fillrule.FillruleValidator",
            "._fillcolor.FillcolorValidator",
            "._editable.EditableValidator",
        ],
    )
