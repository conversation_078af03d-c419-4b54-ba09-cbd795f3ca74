#!/usr/bin/env python3
"""
Test script to verify the territory display logic for showing multiple issues under same territories.
"""

def test_territory_display():
    """Test the territory display logic for showing multiple issues under same territories."""
    
    # Test case: Same territories (BR,CL,MX) with multiple different issues
    print("Test Case: Same territories with multiple different issues")
    season_episode_data = "Missing Season - Offer: {9}(BR,CL,MX); Last episode missing: {S10-(2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22)}(BR,CL,MX)"
    
    print(f"Original data: {season_episode_data}")
    print()
    
    # Apply the new territory combination grouping logic
    import re
    territory_combination_to_issues = {}
    
    # Process the data to group by territory combination
    entries = season_episode_data.split(';')
    for entry in entries:
        entry = entry.strip()
        if not entry:
            continue
            
        # Extract territory if present at the end of the entry
        territory_match = re.search(r'\(([^)]+)\)$', entry)
        territories = []
        issue_content = entry
        
        if territory_match:
            # Get territories from the match
            territories = [t.strip() for t in territory_match.group(1).split(',')]
            # Remove territory part to get the issue content
            issue_content = entry[:territory_match.start()].strip()
        else:
            territories = ["General"]
        
        # Create territory combination key (sorted for consistency)
        territory_key = ",".join(sorted(territories))
        
        # Group by territory combination
        if territory_key not in territory_combination_to_issues:
            territory_combination_to_issues[territory_key] = []
        
        # Add this issue to the territory combination (avoid duplicates)
        if issue_content not in territory_combination_to_issues[territory_key]:
            territory_combination_to_issues[territory_key].append(issue_content)
    
    print("Grouped by territory combination:")
    for territory_key, issues in sorted(territory_combination_to_issues.items()):
        print(f"Territory: {territory_key}")
        for issue_content in issues:
            # Try to extract header and value from issue content
            header_match = re.search(r'^([^:]+):\s*(.+)$', issue_content)
            if header_match:
                header = header_match.group(1).strip()
                value = header_match.group(2).strip()
                print(f"  {header}:")
                print(f"    {value} ({territory_key})")
            else:
                print(f"  {issue_content} ({territory_key})")
        print()
    
    # Test case 2: Mixed territories with different issues
    print("Test Case 2: Mixed territories with different issues")
    season_episode_data = "Missing Season - Offer: {9}(BR,CL,MX); Last episode missing: {S10-(2,3,4,5)}(CO); Missing Season - Offer: {1-6}(LT,LV,MT)"
    
    print(f"Original data: {season_episode_data}")
    print()
    
    territory_combination_to_issues = {}
    
    # Process the data to group by territory combination
    entries = season_episode_data.split(';')
    for entry in entries:
        entry = entry.strip()
        if not entry:
            continue
            
        # Extract territory if present at the end of the entry
        territory_match = re.search(r'\(([^)]+)\)$', entry)
        territories = []
        issue_content = entry
        
        if territory_match:
            # Get territories from the match
            territories = [t.strip() for t in territory_match.group(1).split(',')]
            # Remove territory part to get the issue content
            issue_content = entry[:territory_match.start()].strip()
        else:
            territories = ["General"]
        
        # Create territory combination key (sorted for consistency)
        territory_key = ",".join(sorted(territories))
        
        # Group by territory combination
        if territory_key not in territory_combination_to_issues:
            territory_combination_to_issues[territory_key] = []
        
        # Add this issue to the territory combination (avoid duplicates)
        if issue_content not in territory_combination_to_issues[territory_key]:
            territory_combination_to_issues[territory_key].append(issue_content)
    
    print("Grouped by territory combination:")
    for territory_key, issues in sorted(territory_combination_to_issues.items()):
        print(f"Territory: {territory_key}")
        for issue_content in issues:
            # Try to extract header and value from issue content
            header_match = re.search(r'^([^:]+):\s*(.+)$', issue_content)
            if header_match:
                header = header_match.group(1).strip()
                value = header_match.group(2).strip()
                print(f"  {header}:")
                print(f"    {value} ({territory_key})")
            else:
                print(f"  {issue_content} ({territory_key})")
        print()
    
    # Test case 3: Same territories with same issue (should still work)
    print("Test Case 3: Same territories with same issue")
    season_episode_data = "Missing Season - Offer: {1-6}(LT); Missing Season - Offer: {1-6}(LV); Missing Season - Offer: {1-6}(MT)"
    
    print(f"Original data: {season_episode_data}")
    print()
    
    territory_combination_to_issues = {}
    
    # Process the data to group by territory combination
    entries = season_episode_data.split(';')
    for entry in entries:
        entry = entry.strip()
        if not entry:
            continue
            
        # Extract territory if present at the end of the entry
        territory_match = re.search(r'\(([^)]+)\)$', entry)
        territories = []
        issue_content = entry
        
        if territory_match:
            # Get territories from the match
            territories = [t.strip() for t in territory_match.group(1).split(',')]
            # Remove territory part to get the issue content
            issue_content = entry[:territory_match.start()].strip()
        else:
            territories = ["General"]
        
        # Create territory combination key (sorted for consistency)
        territory_key = ",".join(sorted(territories))
        
        # Group by territory combination
        if territory_key not in territory_combination_to_issues:
            territory_combination_to_issues[territory_key] = []
        
        # Add this issue to the territory combination (avoid duplicates)
        if issue_content not in territory_combination_to_issues[territory_key]:
            territory_combination_to_issues[territory_key].append(issue_content)
    
    print("Grouped by territory combination:")
    for territory_key, issues in sorted(territory_combination_to_issues.items()):
        print(f"Territory: {territory_key}")
        for issue_content in issues:
            # Try to extract header and value from issue content
            header_match = re.search(r'^([^:]+):\s*(.+)$', issue_content)
            if header_match:
                header = header_match.group(1).strip()
                value = header_match.group(2).strip()
                print(f"  {header}:")
                print(f"    {value} ({territory_key})")
            else:
                print(f"  {issue_content} ({territory_key})")
        print()

if __name__ == "__main__":
    test_territory_display()
