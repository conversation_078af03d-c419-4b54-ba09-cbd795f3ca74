import tkinter as tk
from tkinter import filedialog, messagebox
import pandas as pd
import pyodbc
import os

class ExcelToAccessImporter:
    def __init__(self, root):
        self.root = root
        self.root.title("Excel to Access Importer")
        self.root.geometry("400x200")
        
        # Configure the main window
        self.root.configure(bg="#f0f0f0")
        
        # Create a frame for the button
        frame = tk.Frame(root, bg="#f0f0f0")
        frame.pack(expand=True)
        
        # Create the Import Task button
        self.import_button = tk.Button(
            frame, 
            text="Import Task", 
            command=self.import_excel,
            bg="#4CAF50",
            fg="white",
            font=("Arial", 12, "bold"),
            padx=20,
            pady=10,
            relief=tk.RAISED,
            borderwidth=2
        )
        self.import_button.pack(pady=20)
        
        # Expected Excel headers
        self.expected_headers = [
            "QC Date", "User ID", "AHT", "Series GTI", "Series Name", 
            "Line of business", "Territory", "Partner", "Impressions", 
            "Season/Episode", "Input Date", "Product Episode Missing", 
            "Product Season Missing", "Offer Episode Missing", 
            "Offer Season Missing", "Last Episode Missing", "Wrong Content Playing"
        ]
        
        # Database details
        self.db_path = r"\\ant\dept-in\Digiflex\DF-Cataloghealth\Quality Products\Tickets\TT Details Database.accdb"
        self.table_name = "Task Allocation"
    
    def import_excel(self):
        """Handle the Excel import process"""
        try:
            # Open file dialog to select Excel file
            file_path = filedialog.askopenfilename(
                title="Select Excel File",
                filetypes=[("Excel files", "*.xlsx *.xls")]
            )
            
            if not file_path:
                return  # User canceled the file dialog
            
            # Read the Excel file
            df = pd.read_excel(file_path)
            
            # Validate headers
            missing_headers = [header for header in self.expected_headers if header not in df.columns]
            if missing_headers:
                messagebox.showerror(
                    "Header Error", 
                    f"The following required headers are missing: {', '.join(missing_headers)}"
                )
                return
            
            # Connect to the Access database and import data
            self.import_to_access(df)
            
        except Exception as e:
            messagebox.showerror("Error", f"An error occurred: {str(e)}")
    
    def import_to_access(self, df):
        """Import data from DataFrame to Access database"""
        try:
            # Check if database file exists
            if not os.path.exists(self.db_path):
                messagebox.showerror("Database Error", f"Database file not found: {self.db_path}")
                return
            
            # Connect to the Access database
            conn_str = f"Driver={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={self.db_path};"
            conn = pyodbc.connect(conn_str)
            cursor = conn.cursor()
            
            # Prepare column names and placeholders for SQL query
            columns = ", ".join(self.expected_headers)
            placeholders = ", ".join(["?"] * len(self.expected_headers))
            
            # Insert data row by row
            rows_inserted = 0
            for _, row in df.iterrows():
                values = [row[header] for header in self.expected_headers]
                
                # Insert the row into the database
                cursor.execute(
                    f"INSERT INTO [{self.table_name}] ({columns}) VALUES ({placeholders})",
                    values
                )
                rows_inserted += 1
            
            # Commit the changes and close the connection
            conn.commit()
            conn.close()
            
            messagebox.showinfo("Success", f"Successfully imported {rows_inserted} rows into the database.")
            
        except pyodbc.Error as e:
            messagebox.showerror("Database Error", f"Database error: {str(e)}")
        except Exception as e:
            messagebox.showerror("Error", f"An error occurred during import: {str(e)}")

def main():
    root = tk.Tk()
    app = ExcelToAccessImporter(root)
    root.mainloop()

if __name__ == "__main__":
    main()