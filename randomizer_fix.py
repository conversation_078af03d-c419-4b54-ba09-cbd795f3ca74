def randomizer_randomize_tasks(self, instance):
    """Randomize tasks among users."""
    if self.randomizer_date_filtered_df is None or self.randomizer_date_filtered_df.empty:
        self.show_error("Error", "No filtered data available.")
        return
    
    try:
        # Check if S.no column exists
        if 'S.no' not in self.randomizer_date_filtered_df.columns:
            # Try to find similar column names
            possible_columns = [col for col in self.randomizer_date_filtered_df.columns 
                               if col.lower().replace(' ', '').replace('.', '') == 'sno']
            
            if possible_columns:
                # Use the first matching column
                self.randomizer_date_filtered_df.rename(columns={possible_columns[0]: 'S.no'}, inplace=True)
                print(f"Renamed column '{possible_columns[0]}' to 'S.no'")
            else:
                self.show_error("Error", "Required column 'S.no' not found in the data.")
                return
        
        # Get selected time range
        selected_range = self.total_time_dropdown.text
        
        # Filter by time range if applicable
        if selected_range != "All" and 'Total Time' in self.randomizer_date_filtered_df.columns:
            # Parse the range
            if selected_range.startswith("<"):
                threshold = float(selected_range[1:])
                time_filtered = self.randomizer_date_filtered_df[self.randomizer_date_filtered_df['Total Time'] < threshold]
            elif selected_range.startswith(">"):
                threshold = float(selected_range[1:])
                time_filtered = self.randomizer_date_filtered_df[self.randomizer_date_filtered_df['Total Time'] > threshold]
            elif "-" in selected_range:
                min_val, max_val = map(float, selected_range.split("-"))
                time_filtered = self.randomizer_date_filtered_df[
                    (self.randomizer_date_filtered_df['Total Time'] >= min_val) & 
                    (self.randomizer_date_filtered_df['Total Time'] < max_val)
                ]
            else:
                time_filtered = self.randomizer_date_filtered_df
        
            if not time_filtered.empty:
                selected_sno = time_filtered['S.no'].unique()
                time_filtered_df = self.randomizer_date_filtered_df[self.randomizer_date_filtered_df['S.no'].isin(selected_sno)]
            else:
                time_filtered_df = pd.DataFrame()
        else:
            time_filtered_df = self.randomizer_date_filtered_df
        
        if time_filtered_df.empty:
            self.show_error("Error", "No data found for the selected time range.")
            return
        
        # Get unique dates
        unique_dates = time_filtered_df['QC Date'].dt.date.unique()
        
        if len(unique_dates) == 0:
            self.show_error("Error", "No dates found in the filtered data.")
            return
        
        all_samples = []
        
        # Process each date
        for date in unique_dates:
            # Get data for this date
            date_data = time_filtered_df[time_filtered_df['QC Date'].dt.date == date]
            
            # Get unique User IDs for this date
            unique_users = date_data['User ID'].unique()
            
            # Process each User ID for this date
            for user_id in unique_users:
                # Get data for this user on this date
                user_data = date_data[date_data['User ID'] == user_id]
                
                # Get unique S.no values for this user on this date
                unique_sno = user_data['S.no'].unique()
                total_titles = len(unique_sno)
                
                # Calculate 10% of total titles (minimum 1)
                sample_size = max(1, math.ceil(0.1 * total_titles))
                
                # Sample 10% of unique S.no values
                if sample_size < total_titles:
                    sampled_sno = np.random.choice(unique_sno, size=sample_size, replace=False)
                else:
                    sampled_sno = unique_sno
                
                # Get all rows for the sampled S.no values
                sampled_data = user_data[user_data['S.no'].isin(sampled_sno)]
                
                # Add to all samples
                all_samples.append(sampled_data)
                
                # Log sampling details
                print(f"User {user_id} on {date}: Total titles={total_titles}, Sampled={len(sampled_sno)}")
        
        # Combine all samples
        if all_samples:
            self.randomizer_final_export = pd.concat(all_samples, ignore_index=True)
            
            # Store user title counts for display
            self.user_title_counts = {}
            for user_id in self.randomizer_final_export['User ID'].unique():
                user_data = time_filtered_df[time_filtered_df['User ID'] == user_id]
                user_total_titles = len(user_data['S.no'].unique())
                
                user_sampled = self.randomizer_final_export[self.randomizer_final_export['User ID'] == user_id]
                user_sampled_titles = len(user_sampled['S.no'].unique())
                
                self.user_title_counts[user_id] = {
                    'total': user_total_titles,
                    'sampled': user_sampled_titles,
                    'percentage': round(user_sampled_titles / user_total_titles * 100, 1) if user_total_titles > 0 else 0
                }
            
            # Enable export button
            if hasattr(self, 'export_button'):
                self.export_button.disabled = False
            
            # Display results in the results grid
            self.update_results_display()
        else:
            self.show_error("Error", "No data found for the selected criteria.")
    except Exception as e:
        import traceback
        traceback.print_exc()
        self.show_error("Error", f"Error randomizing tasks: {str(e)}")