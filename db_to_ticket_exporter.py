import tkinter as tk
from tkinter import messagebox
import pyodbc
import getpass
import datetime
import os

class DBToTicketExporter:
    def __init__(self, root):
        self.root = root
        self.root.title("Database to Ticket Exporter")
        self.root.geometry("400x200")
        
        # Configure the main window
        self.root.configure(bg="#f0f0f0")
        
        # Create a frame for the button
        frame = tk.Frame(root, bg="#f0f0f0")
        frame.pack(expand=True)
        
        # Create the Export button
        self.export_button = tk.Button(
            frame, 
            text="Export Task", 
            command=self.export_from_db,
            bg="#4CAF50",
            fg="white",
            font=("Arial", 12, "bold"),
            padx=20,
            pady=10,
            relief=tk.RAISED,
            borderwidth=2
        )
        self.export_button.pack(pady=20)
        
        # Database details
        self.db_path = r"\\ant\dept-in\Digiflex\DF-Cataloghealth\Quality Products\Tickets\TT Details Database.accdb"
        self.table_name = "TT Template"
    
    def export_from_db(self):
        """Handle the database export process"""
        try:
            # Get current user ID
            user_id = getpass.getuser()
            
            # Check if database file exists
            if not os.path.exists(self.db_path):
                messagebox.showerror("Database Error", f"Database file not found: {self.db_path}")
                return
            
            # Connect to the Access database
            conn_str = f"Driver={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={self.db_path};"
            conn = pyodbc.connect(conn_str)
            cursor = conn.cursor()
            
            # Query to get the most recent data for the current user with non-empty RCA
            query = f"""
            SELECT TOP 1 * FROM [{self.table_name}]
            WHERE [User ID] = ? AND [RCA] IS NOT NULL AND [RCA] <> ''
            ORDER BY [Date] DESC, [End Time] DESC
            """
            
            cursor.execute(query, (user_id,))
            row = cursor.fetchone()
            
            if not row:
                messagebox.showinfo("No Data", f"No recent data found for user {user_id} with RCA information.")
                conn.close()
                return
            
            # Get column names
            columns = [column[0] for column in cursor.description]
            
            # Create a dictionary of the row data
            data = dict(zip(columns, row))
            
            # Close the database connection
            conn.close()
            
            # Import the TVSeriesForm class to create a ticket
            from importlib import import_module
            try:
                tv_series_module = import_module("TV Series TT Tool")
                app = tv_series_module.TVSeriesForm()
                
                # Pre-fill the form with data from the database
                app.entries = {}  # Initialize entries dictionary
                app.task_entries = {}  # Initialize task entries dictionary
                
                # Start the app with the data
                app.prefill_from_db(data)
                app.run()
                
                messagebox.showinfo("Success", "Data exported and ticket form opened successfully.")
            except Exception as e:
                messagebox.showerror("Module Error", f"Failed to load TV Series TT Tool: {str(e)}")
        
        except pyodbc.Error as e:
            messagebox.showerror("Database Error", f"Database error: {str(e)}")
        except Exception as e:
            messagebox.showerror("Error", f"An error occurred during export: {str(e)}")

def main():
    root = tk.Tk()
    app = DBToTicketExporter(root)
    root.mainloop()

if __name__ == "__main__":
    main()