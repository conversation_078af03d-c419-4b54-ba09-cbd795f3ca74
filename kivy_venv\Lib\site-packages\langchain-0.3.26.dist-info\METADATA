Metadata-Version: 2.1
Name: langchain
Version: 0.3.26
Summary: Building applications with LLMs through composability
License: MIT
Project-URL: Source Code, https://github.com/langchain-ai/langchain/tree/master/libs/langchain
Project-URL: Release Notes, https://github.com/langchain-ai/langchain/releases?q=tag%3A%22langchain%3D%3D0%22&expanded=true
Project-URL: repository, https://github.com/langchain-ai/langchain
Requires-Python: >=3.9
Requires-Dist: langchain-core<1.0.0,>=0.3.66
Requires-Dist: langchain-text-splitters<1.0.0,>=0.3.8
Requires-Dist: langsmith>=0.1.17
Requires-Dist: pydantic<3.0.0,>=2.7.4
Requires-Dist: SQLAlchemy<3,>=1.4
Requires-Dist: requests<3,>=2
Requires-Dist: PyYAML>=5.3
Requires-Dist: async-timeout<5.0.0,>=4.0.0; python_version < "3.11"
Provides-Extra: community
Requires-Dist: langchain-community; extra == "community"
Provides-Extra: anthropic
Requires-Dist: langchain-anthropic; extra == "anthropic"
Provides-Extra: openai
Requires-Dist: langchain-openai; extra == "openai"
Provides-Extra: azure-ai
Requires-Dist: langchain-azure-ai; extra == "azure-ai"
Provides-Extra: cohere
Requires-Dist: langchain-cohere; extra == "cohere"
Provides-Extra: google-vertexai
Requires-Dist: langchain-google-vertexai; extra == "google-vertexai"
Provides-Extra: google-genai
Requires-Dist: langchain-google-genai; extra == "google-genai"
Provides-Extra: fireworks
Requires-Dist: langchain-fireworks; extra == "fireworks"
Provides-Extra: ollama
Requires-Dist: langchain-ollama; extra == "ollama"
Provides-Extra: together
Requires-Dist: langchain-together; extra == "together"
Provides-Extra: mistralai
Requires-Dist: langchain-mistralai; extra == "mistralai"
Provides-Extra: huggingface
Requires-Dist: langchain-huggingface; extra == "huggingface"
Provides-Extra: groq
Requires-Dist: langchain-groq; extra == "groq"
Provides-Extra: aws
Requires-Dist: langchain-aws; extra == "aws"
Provides-Extra: deepseek
Requires-Dist: langchain-deepseek; extra == "deepseek"
Provides-Extra: xai
Requires-Dist: langchain-xai; extra == "xai"
Provides-Extra: perplexity
Requires-Dist: langchain-perplexity; extra == "perplexity"
Description-Content-Type: text/markdown

# 🦜️🔗 LangChain

⚡ Building applications with LLMs through composability ⚡

[![Release Notes](https://img.shields.io/github/release/langchain-ai/langchain)](https://github.com/langchain-ai/langchain/releases)
[![lint](https://github.com/langchain-ai/langchain/actions/workflows/lint.yml/badge.svg)](https://github.com/langchain-ai/langchain/actions/workflows/lint.yml)
[![test](https://github.com/langchain-ai/langchain/actions/workflows/test.yml/badge.svg)](https://github.com/langchain-ai/langchain/actions/workflows/test.yml)
[![Downloads](https://static.pepy.tech/badge/langchain/month)](https://pepy.tech/project/langchain)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Twitter](https://img.shields.io/twitter/url/https/twitter.com/langchainai.svg?style=social&label=Follow%20%40LangChainAI)](https://twitter.com/langchainai)
[![Open in Dev Containers](https://img.shields.io/static/v1?label=Dev%20Containers&message=Open&color=blue&logo=visualstudiocode)](https://vscode.dev/redirect?url=vscode://ms-vscode-remote.remote-containers/cloneInVolume?url=https://github.com/langchain-ai/langchain)
[![Open in GitHub Codespaces](https://github.com/codespaces/badge.svg)](https://codespaces.new/langchain-ai/langchain)
[![GitHub star chart](https://img.shields.io/github/stars/langchain-ai/langchain?style=social)](https://star-history.com/#langchain-ai/langchain)
[![Dependency Status](https://img.shields.io/librariesio/github/langchain-ai/langchain)](https://libraries.io/github/langchain-ai/langchain)
[![Open Issues](https://img.shields.io/github/issues-raw/langchain-ai/langchain)](https://github.com/langchain-ai/langchain/issues)


Looking for the JS/TS version? Check out [LangChain.js](https://github.com/langchain-ai/langchainjs).

To help you ship LangChain apps to production faster, check out [LangSmith](https://smith.langchain.com). 
[LangSmith](https://smith.langchain.com) is a unified developer platform for building, testing, and monitoring LLM applications. 
Fill out [this form](https://www.langchain.com/contact-sales) to speak with our sales team.

## Quick Install

`pip install langchain`
or
`pip install langsmith && conda install langchain -c conda-forge`

## 🤔 What is this?

Large language models (LLMs) are emerging as a transformative technology, enabling developers to build applications that they previously could not. However, using these LLMs in isolation is often insufficient for creating a truly powerful app - the real power comes when you can combine them with other sources of computation or knowledge.

This library aims to assist in the development of those types of applications. Common examples of these applications include:

**❓ Question answering with RAG**

- [Documentation](https://python.langchain.com/docs/use_cases/question_answering/)
- End-to-end Example: [Chat LangChain](https://chat.langchain.com) and [repo](https://github.com/langchain-ai/chat-langchain)

**🧱 Extracting structured output**

- [Documentation](https://python.langchain.com/docs/use_cases/extraction/)
- End-to-end Example: [SQL Llama2 Template](https://github.com/langchain-ai/langchain-extract/)

**🤖 Chatbots**

- [Documentation](https://python.langchain.com/docs/use_cases/chatbots)
- End-to-end Example: [Web LangChain (web researcher chatbot)](https://weblangchain.vercel.app) and [repo](https://github.com/langchain-ai/weblangchain)

## 📖 Documentation

Please see [here](https://python.langchain.com) for full documentation on:

- Getting started (installation, setting up the environment, simple examples)
- How-To examples (demos, integrations, helper functions)
- Reference (full API docs)
- Resources (high-level explanation of core concepts)

## 🚀 What can this help with?

There are five main areas that LangChain is designed to help with.
These are, in increasing order of complexity:

**📃 Models and Prompts:**

This includes prompt management, prompt optimization, a generic interface for all LLMs, and common utilities for working with chat models and LLMs.

**🔗 Chains:**

Chains go beyond a single LLM call and involve sequences of calls (whether to an LLM or a different utility). LangChain provides a standard interface for chains, lots of integrations with other tools, and end-to-end chains for common applications.

**📚 Retrieval Augmented Generation:**

Retrieval Augmented Generation involves specific types of chains that first interact with an external data source to fetch data for use in the generation step. Examples include summarization of long pieces of text and question/answering over specific data sources.

**🤖 Agents:**

Agents involve an LLM making decisions about which Actions to take, taking that Action, seeing an Observation, and repeating that until done. LangChain provides a standard interface for agents, a selection of agents to choose from, and examples of end-to-end agents.

**🧐 Evaluation:**

[BETA] Generative models are notoriously hard to evaluate with traditional metrics. One new way of evaluating them is using language models themselves to do the evaluation. LangChain provides some prompts/chains for assisting in this.

For more information on these concepts, please see our [full documentation](https://python.langchain.com).

## 💁 Contributing

As an open-source project in a rapidly developing field, we are extremely open to contributions, whether it be in the form of a new feature, improved infrastructure, or better documentation.

For detailed information on how to contribute, see the [Contributing Guide](https://python.langchain.com/docs/contributing/).
