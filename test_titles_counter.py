#!/usr/bin/env python3
"""
Test script to verify the titles processed counter works correctly
and only increments once per GTI completion.
"""

def test_titles_counter_logic():
    """Test the titles counter logic without GUI components"""
    
    print("Testing Titles Processed Counter Logic")
    print("="*50)
    
    # Simulate the counter and flags
    titles_processed = 0
    
    def simulate_submit_data(form_index, total_forms, is_last_form):
        """Simulate the submit_data method logic"""
        nonlocal titles_processed
        
        # Set the _prevent_auto_move flag (True for all forms except the last one)
        prevent_auto_move = not is_last_form
        
        print(f"  Form {form_index + 1}/{total_forms}: prevent_auto_move = {prevent_auto_move}")
        
        # This is the logic from submit_data() method
        if not prevent_auto_move:
            titles_processed += 1
            print(f"    ✅ Counter incremented! Titles Processed: {titles_processed}")
        else:
            print(f"    ⏸️  Counter NOT incremented (intermediate form)")
        
        return titles_processed
    
    def simulate_confirm_preview(all_forms_data):
        """Simulate the confirm_preview method logic (FIXED VERSION)"""
        print(f"\n🔍 Confirming preview for {len(all_forms_data)} forms...")
        
        # Submit each form
        for i, form_data in enumerate(all_forms_data):
            is_last_form = (i == len(all_forms_data) - 1)
            simulate_submit_data(i, len(all_forms_data), is_last_form)
        
        # OLD LOGIC (REMOVED): titles_processed += 1  # This was causing double counting
        # NEW LOGIC: No additional increment here - submit_data() handles it
        
        print(f"\n✅ Final Titles Processed: {titles_processed}")
        return titles_processed
    
    # Test Case 1: Single form (Main form only)
    print("\n📝 Test Case 1: Single Form (Main Form Only)")
    print("-" * 40)
    titles_processed = 0  # Reset counter
    
    single_form_data = [{"RCA": "Test", "Territory": "US"}]
    final_count = simulate_confirm_preview(single_form_data)
    
    expected = 1
    result = "✅ PASS" if final_count == expected else "❌ FAIL"
    print(f"Expected: {expected}, Got: {final_count} - {result}")
    
    # Test Case 2: Multiple forms (Main + 2 Additional)
    print("\n📝 Test Case 2: Multiple Forms (Main + 2 Additional)")
    print("-" * 40)
    titles_processed = 0  # Reset counter
    
    multiple_forms_data = [
        {"RCA": "Main form", "Territory": "US"},
        {"RCA": "Additional 1", "Territory": "UK"},
        {"RCA": "Additional 2", "Territory": "CA"}
    ]
    final_count = simulate_confirm_preview(multiple_forms_data)
    
    expected = 1  # Should still be 1 because it's one GTI with multiple forms
    result = "✅ PASS" if final_count == expected else "❌ FAIL"
    print(f"Expected: {expected}, Got: {final_count} - {result}")
    
    # Test Case 3: Multiple GTI completions
    print("\n📝 Test Case 3: Multiple GTI Completions")
    print("-" * 40)
    titles_processed = 0  # Reset counter
    
    # First GTI (single form)
    print("First GTI:")
    first_gti_data = [{"RCA": "GTI 1", "Territory": "US"}]
    simulate_confirm_preview(first_gti_data)
    
    # Second GTI (multiple forms)
    print("\nSecond GTI:")
    second_gti_data = [
        {"RCA": "GTI 2 Main", "Territory": "UK"},
        {"RCA": "GTI 2 Additional", "Territory": "CA"}
    ]
    simulate_confirm_preview(second_gti_data)
    
    expected = 2  # Two GTIs completed
    result = "✅ PASS" if titles_processed == expected else "❌ FAIL"
    print(f"\nFinal count - Expected: {expected}, Got: {titles_processed} - {result}")
    
    print("\n" + "="*50)
    print("📊 SUMMARY")
    print("="*50)
    print("✅ Fixed Issue: Removed duplicate counter increment from confirm_preview()")
    print("✅ Counter Logic: Only increments in submit_data() when _prevent_auto_move is False")
    print("✅ Behavior: One increment per GTI completion, regardless of number of forms")
    print("✅ Multiple Forms: All forms for one GTI count as one title processed")
    
    return True

if __name__ == "__main__":
    test_titles_counter_logic()
