# -*- coding: utf-8 -*-
import sys
import os
import json
import pickle
import atexit

# Ensure UTF-8 encoding for all text operations
if sys.version_info[0] >= 3:
    try:
        import io
        if hasattr(sys.stdout, 'buffer') and sys.stdout.buffer:
            sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        if hasattr(sys.stderr, 'buffer') and sys.stderr.buffer:
            sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
    except (AttributeError, OSError):
        pass  # Skip if already configured or not available

from kivymd.app import MDApp
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.label import MDLabel
from kivymd.uix.textfield import MDTextField
from kivymd.uix.button import MDRaisedButton, MDFlatButton
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.dialog import MDDialog
from kivymd.uix.card import MDCard
from kivy.metrics import dp
from kivy.core.window import Window
from kivy.core.text import LabelBase
from kivy.effects.scroll import ScrollEffect
import pyodbc
import datetime
import getpass
import pandas as pd
from tkinter import filedialog
import webbrowser
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager
import time
import unicodedata
import pyperclip
import pyperclip

# Hardcoded blurb-to-issue mapping
blurbs_dict = {
    "Season Missing": "Season is missing on the EDP/Detail Page",
    "Missing Offers/Avails": "Offers/Avails are missing in EDP/CRMS",
    "Episode Missing": "Episode is missing in EDP/Detail Page",
    "Expired Offers/Avails": "Offers/Avails have expired in EDP/CRMS",
    "Merge Issue": "Title requires merging in the catalog",
    "Unmerge Issue": "Incorrectly merged across vendors or Seasons merged incorrectly, need separate metadata pages",
    "Tech Issue": "Technical issue observed, further investigation needed",
    "Offers Takendown": "Offers have been taken down in EDP",
    "VCID Error": "Episode is missing on the site due to a VCID error",
    "Pricing Issue": "Play/Buy button is missing on the detail page due to a pricing issue",
    "Orphaned Season/Episode": "The Season/Episode are not aligned within the Season/Episode hierarchy",
    "Offer Creations": "Live avails exist, but the offer hasn't been created in EDP",
    "Play Button Missing": "Play/Buy button is missing on the Detail Page",
}

# RCA options mapping
rca_dict = {
    "Future Start Date (Prime)" : "Episode X is missing/unavailable (play button missing) due to a future avail start-date. Please confirm whether this is intended. If not, please request the partner for corrected avail re-delivery.",
    "Expired Prime Avails & Offers" : "Episode X is missing/unavailable due to expired avail. Please confirm whether this is intended. If not, please request a partner for corrected avail re-delivery.",
    "Missing Prime Avails & Offers (No Deal ID)" : "Episode X is missing/unavailable due to no or missing avail. Please confirm whether this is intended. If not, please request the partner for corrected avail re-delivery.",
    "Missing Prime Avails & Offers (With Deal ID)" : " The title is missing prime avails. Looked at Acquire to verify the <dealID> and found that, i.  is still in window, ii.  has the missing title and impacted territories as part of the deal",
    "Future Start Date (TVOD or Channel)" : "Episode X is missing/unavailable due to a future avail start-date. Please confirm whether this is intended. If not, please request partner for corrected avail re-delivery.",
    "Expired TVOD/Channel Avails & Offers" : "Episode X is missing/unavailable due to expired avail. Please confirm whether this is intended. If not, please request partner for corrected avail re-delivery.",
    "Missing TVOD/Channel Avails & Offers" : "Episode X is missing/unavailable due to no or missing avail. Please confirm whether this is intended. If not, please request a partner for corrected avail re-delivery.",
    "Season Missing (Prime)" : "Season X is missing from EDP due to no avail. Please confirm whether this is intended. If not, please request partner for avail re-delivery.",
    "Episode Missing (Prime)" : "Episode X is missing from EDP due to no avail. Please confirm whether this is intended. If not, please request partner for avail re-delivery.",
    "Season Missing (TVOD or Channel)" : "Season X is missing from EDP due to no avail. Please confirm whether this is intended. If not, please request partner for avail re-delivery.",
    "Episode Missing (TVOD or Channel)" : "Episode X is missing from EDP due to no avail. Please confirm whether this is intended. If not, please request partner for avail re-delivery.",
    "Incorrect Sequence" : "Episode X appears to be missing because the episode sequence in avails does not match with episode sequence in metadata. Please confirm which episode sequence is correct and update accordingly.",
    "Merge Issue (1C issue)" : "There are two versions of this title suggesting likely a 1C issue. Version 1 with (insert URN from EDP page 1) and Version 2 with (insert URN from EDP page2). Version 2 is not live and is garnering customer impressions meaning customers are exposed to the wrong version. Requesting 1C team to execute a merge.",
    "Non-Actionable Right Scope" : "Episode X is missing/unavailable due to a non-actionable right scope. Please review if the scope status needs to be updated to make the title live to customers.",
    "Offer Issue (PVD Workflow)" : "This title has in-window avails however offers are not created. We’ve validated that 'contributing user' is 'pvd-workflow'. Please review whether offers can be created so that this title is made available to customers.",
    "Offer Issue (Non - PVD Workflow)" : "This title has in-window avails however offers are not created. We’ve validated that this offer is not tied to split-licensing or PVD or season-offer/pass issues. Please review whether offers need to be created so that this title is made available to customers.",
    "Offer Issue (Split License)" : "This title has in-window avails, however offers are not created. We’ve validated that this offer is tied to split-licensing. Please review whether offers need to be created so that this title is made available to customers.",
    "Offer Issue (Season Page)" : "Season has in-window avails however season-offers / season pass is not live. Please review whether offers need to be created so that season pass/offer is made available to customers.",
    "Offer Issue (Season Pass )" : "This season has in-window avails however season-offers / season pass is not approved. Please review whether offer can be approved so that season pass/offer is made available to customers.",
    "Offer Issue (‘Not Approved’ or ‘Taken Down’)" : "This title has in-window avails however offers are not approved. We’ve validated that 'contributing user' is not 'pvd-workflow' and release_status = 'released' on CST. Please review whether offers need to be approved so that this title is made available to customers.",
    "Mezz Missing" : "This title has in-window avails however offers are not approved. This is due to following failing rules <insert failing rules>. Please take actions to fulfill failing rules so that this title is published to customers.",
    "Offer Takedown (No Duplicate)" : "This title has in-window avails, however offers are taken-down. Please validate if the take-down can be removed so that title is made available to customers.",
    "Offer Takedown (Duplicate)" : "This title has offers taken-down. However, there is another duplicate episode with live offers <insert episode details>. Since there is a live duplicate, requesting team to confirm if they can perform a tombstone or title take-down on the taken-down title to remove the unavailable duplicate from detail page. If take-down/tombstone is advised please route the ticket to appropriate CTI.",
    "TVOD Pricing Issue" : "This title has in-window avails and offers are created/approved. However, this is a TVOD offer and pricing is 0$ or n/a. Please review there is an issue due to pricing that is resulting in the title not being live/available to customers on storefront.",
    "Merge Issue (Duplicate Episode/Season)" : "There are multiple duplicate episodes <note duplicate episode sequence> in the season <note season sequence>. Merge team to review whether these duplicates can be merged.",
    "Page Not Found Error" : "When accessing Season X <insert season sequence> detail page via EDP we observe a page not found error. Please review if this can be corrected so that Season X is made available to customers.",
    "ASIN Leads To Different Page" : "When accessing Season X <insert season sequence> detail page via EDP it takes us to a detail page for season Y <insert season sequence>. Please review if this can be corrected so that Season X is made available to customers.",
    "Episode Missing Play Button" : "Episode X is missing play button on PV site. While checking on EDP and CST we have live offers and avails. Please check why this episode missing play button on site and helps us to resolve this issue.",
    "Episode Missing In PV Site" : "Episode X is missing on PV site. While checking on EDP and CST we have live offers and avails. Please check why this episode missing on site and helps us to resolve this issue.",
    "Episode Missing In EDP" : "Episode X is missing on EDP and PV site due to no metadata and avails. Please confirm whether this is intended. If not, please request partner for metadata and avail delivery.",
    "Season Missing Play Button" : "Season X is missing play button on PV site. While checking on EDP and CST we have live offers and avails. Please check why this season missing play button on site and helps us to resolve this issue.",
    "Season Missing In PV site" : "Season X is missing on PV site. While checking on EDP and CST we have live offers and avails. Please check why this season missing on site and helps us to resolve this issue.",
    "Season Missing In EDP" : "Season X is missing on EDP and PV site due to no metadata and avails. Please confirm whether this is intended. If not, please request partner for metadata and avail delivery.",
    "Episode/Season Missing Due To Repurposing" : "Episode/Season X is missing on PV site. While checking on EDP  we have live offers and avails but the publishing status is 'not ready' in CST due to <insert failing rules>. Please take actions to fulfill failing rules so that this title is published to customers."

}

# Access DB path
access_db_path = r"\\ant\dept-in\Digiflex\DF-Cataloghealth\Quality Products\Tickets\TT Details Database.accdb"

# Field headers
fields = [
    "RCA", "Territory", "Offer Type", "Partner", "Season/Episode Number", 
    "EDP Link", "EDP Link 2", "D2C Link", "D2C Link 2",
    "Detail Page Link", "Detail Page Link 2", "Avails Link", "Avails Link 2", "DEAL Link", "WMV Link", "Screenshot", "Issue"
]

class TVSeriesForm(MDApp):
    def __init__(self):
        super().__init__()
        self.entries = {}
        self.selected_issues = []
        self.selected_rca_items = []
        self.offer_type_menu = None
        self.cti_menus = []
        self.cti_buttons = []
        self.cti_values = ["", "", ""]  # Category, Type, Item
        self.excel_data = []
        self.task_data = []
        self.task_dialog = None
        self.user_dropdown = None
        self.task_entries = {}
        self.edp_link_url = ""
        self.current_gti_index = 0
        self.grouped_gti_data = []
        self.current_gti_raw_data = []
        self.lob_menu = None
        self.code_expansion_menu = None
        self.action_menu = None
        self.resolver_menu = None
        self.start_time = None
        self.end_time = None
        self.break_start_time = None  # datetime.time object for tracking breaks internally
        self.break_end_time = None    # datetime.time object for tracking breaks internally
        self.total_break_time = 0.0   # Total break time in minutes (float)
        self.break_duration_db = 0.0   # Break duration saved to database
        self.issue_name = ""  # Store the button name separately
        self.form_counter = 1  # Track number of forms
        self.additional_forms = []  # Store additional form data
        self.active_form = 'main'  # Track which form is currently active
        self.cumulative_time = 0.0  # Store the cumulative total time for display
        self.titles_processed = 0  # Counter for completed/submitted titles
        self.autosave_path = os.path.join(os.path.expanduser("~"), ".tv_series_tool_autosave.pkl")
        
        # Season/Episode data for View Details functionality
        self.current_season_episode_data = ""
        self.current_territory_data = ""
        
        # Configure font for Unicode support
        self.setup_unicode_support()

        # Initialize cursor settings
        self.setup_cursor_management()

        # Register auto-save on exit
        atexit.register(self.auto_save_data)

        # Try to load previous session data
        self.try_load_autosave()
    
    def setup_unicode_support(self):
        """Setup Unicode font support for international characters"""
        try:
            # Register fonts that support Japanese characters
            unicode_fonts = [
                'C:/Windows/Fonts/msgothic.ttc',  # MS Gothic - supports Japanese
                'C:/Windows/Fonts/msmincho.ttc',  # MS Mincho - supports Japanese
                'C:/Windows/Fonts/meiryo.ttc',    # Meiryo - supports Japanese
                'C:/Windows/Fonts/arial.ttf',     # Arial Unicode
                'C:/Windows/Fonts/calibri.ttf',   # Calibri
                'C:/Windows/Fonts/segoeui.ttf'    # Segoe UI
            ]
            
            font_registered = False
            for font_path in unicode_fonts:
                if os.path.exists(font_path):
                    LabelBase.register(name='UnicodeFont', fn_regular=font_path)
                    print(f"Registered Unicode font: {font_path}")
                    font_registered = True
                    break
            
            if not font_registered:
                print("Warning: No Unicode-compatible font found")
                        
        except Exception as e:
            print(f"Font registration warning: {e}")

    def setup_cursor_management(self):
        """Setup cursor management to prevent cursor disappearing"""
        try:
            # Force cursor to be visible
            from kivy.core.window import Window
            Window.show_cursor = True

            # Set default cursor
            try:
                Window.set_system_cursor('arrow')
            except:
                # Fallback if system cursor setting fails
                pass

            print("Cursor management initialized")
        except Exception as e:
            print(f"Cursor management warning: {e}")

    def normalize_unicode_text(self, text):
        """Normalize Unicode text to handle international characters properly"""
        if not text:
            return ""
        try:
            # Convert to string first
            text_str = str(text)
            # Normalize Unicode text to NFC form for consistent character representation
            normalized = unicodedata.normalize('NFC', text_str)
            # Ensure proper encoding for web forms
            encoded = normalized.encode('utf-8').decode('utf-8')
            return encoded
        except Exception as e:
            print(f"Unicode normalization error: {e}")
            # Fallback to basic string conversion
            try:
                return str(text).encode('utf-8', errors='replace').decode('utf-8')
            except:
                return str(text)

    def keyboard_handler(self, window, key, *args):
        # Prevent Escape key from closing the application
        if key == 27:  # 27 is the keycode for Escape key
            return True  # Return True to indicate the key was handled
        return False  # Let other keys pass through

    def on_cursor_enter(self, window, *args):
        """Handle cursor entering the window"""
        Window.show_cursor = True
        Window.set_system_cursor('arrow')
        return True

    def on_cursor_leave(self, window, *args):
        """Handle cursor leaving the window"""
        Window.show_cursor = True
        return True

    def on_mouse_motion(self, window, pos, *args):
        """Handle mouse motion to ensure cursor remains visible"""
        Window.show_cursor = True
        return False  # Allow other handlers to process the motion event

    def check_cursor_visibility(self, dt):
        """Periodic check to ensure cursor remains visible"""
        try:
            Window.show_cursor = True
            # Optionally reset cursor type if needed
            if hasattr(Window, 'set_system_cursor'):
                try:
                    Window.set_system_cursor('arrow')
                except:
                    pass
        except Exception as e:
            print(f"Cursor visibility check error: {e}")
        return True  # Continue scheduling
        
    def build(self):
        Window.maximize()
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"

        # Ensure cursor remains visible
        Window.show_cursor = True
        Window.set_system_cursor('arrow')

        # Bind keyboard to prevent Escape key from closing the app
        Window.bind(on_keyboard=self.keyboard_handler)

        # Bind mouse events to ensure cursor visibility
        Window.bind(on_cursor_enter=self.on_cursor_enter)
        Window.bind(on_cursor_leave=self.on_cursor_leave)
        Window.bind(on_motion=self.on_mouse_motion)

        # Schedule periodic cursor check
        from kivy.clock import Clock
        Clock.schedule_interval(self.check_cursor_visibility, 1.0)  # Check every second
        
        # Main layout
        main_layout = MDBoxLayout(orientation="vertical", spacing=dp(10), padding=dp(20), md_bg_color="dodgerblue")
        
        # Header with title and buttons with gaps
        header = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(60))
        title = MDLabel(text="TV Series Task Form:", theme_text_color="Primary", 
                       font_style="H5", size_hint_x=0.20, bold=True)
        
        # Total Time display with formatted value
        self.time_display = MDLabel(text="Total Time: 0.00 min", theme_text_color="Secondary",
                                  font_style="Subtitle1", size_hint_x=0.10, bold=True)
        
        # Titles Processed display
        self.titles_display = MDLabel(text="Titles Processed: 0", theme_text_color="Secondary",
                                   font_style="Subtitle1", size_hint_x=0.10, bold=True)
        
        import_btn = MDRaisedButton(text="Import", size_hint_x=0.07,
                                   on_release=self.import_excel)
        gap1 = MDLabel(text="", size_hint_x=0.01)
        
        task_btn = MDRaisedButton(text="Task", size_hint_x=0.07,
                                 on_release=self.open_task_dialog)
        gap2 = MDLabel(text="", size_hint_x=0.01)
        
        # Add Get Task button
        get_task_btn = MDRaisedButton(text="Get Task", size_hint_x=0.07, md_bg_color="purple",
                                    on_release=self.get_next_task)
        gap3 = MDLabel(text="", size_hint_x=0.01)
        
        break_btn = MDRaisedButton(text="Pause", size_hint_x=0.07, md_bg_color="orange",
                                  on_release=self.set_break_mode)
        gap4 = MDLabel(text="", size_hint_x=0.01)
        
        active_btn = MDRaisedButton(text="Play", size_hint_x=0.07, md_bg_color="green",
                                   on_release=self.set_active_mode)
        gap5 = MDLabel(text="", size_hint_x=0.01)
        
        submit_btn = MDRaisedButton(text="Submit", size_hint_x=0.07, 
                                   on_release=self.submit_task)
        
        header.add_widget(title)
        header.add_widget(self.time_display)
        header.add_widget(self.titles_display)
        header.add_widget(import_btn)
        header.add_widget(gap1)
        header.add_widget(task_btn)
        header.add_widget(gap2)
        header.add_widget(get_task_btn)
        header.add_widget(gap3)
        header.add_widget(break_btn)
        header.add_widget(gap4)
        header.add_widget(active_btn)
        header.add_widget(gap5)
        header.add_widget(submit_btn)
        main_layout.add_widget(header)
        
        # Scrollable content with proper configuration
        scroll = MDScrollView(
            do_scroll_x=False,
            do_scroll_y=True,
            scroll_type=['content'],
            bar_width=dp(10),
            effect_cls=ScrollEffect  # Use basic scroll effect instead of damped
        )
        content = MDBoxLayout(orientation="vertical", spacing=dp(15), 
                             adaptive_height=True, padding=dp(10),
                             size_hint_y=None)
        content.bind(minimum_height=content.setter('height'))
        
        # Form fields
        form_card = MDCard(padding=dp(20), spacing=dp(10), adaptive_height=True)
        form_layout = MDBoxLayout(orientation="vertical", spacing=dp(15), adaptive_height=True)
        
        for field in fields:
            field_layout = MDBoxLayout(orientation="horizontal", spacing=dp(10), 
                                     adaptive_height=True, size_hint_y=None)
            
            label = MDLabel(text=field, size_hint_x=0.3, theme_text_color="Primary",
                           font_style="Subtitle1", )
            
            if field == "Offer Type":
                # Dropdown for Offer Type
                field_layout.add_widget(label)
                # Add spacer to center the button
                spacer_left = MDLabel(text="", size_hint_x=0.1)
                field_layout.add_widget(spacer_left)
                offer_field = MDRaisedButton(text="Select Offer Type", size_hint_x=0.3,
                                           on_release=self.open_offer_menu_click)
                self.entries[field] = offer_field
                field_layout.add_widget(offer_field)
                # Add spacer to balance the layout
                spacer_right = MDLabel(text="", size_hint_x=0.3)
                field_layout.add_widget(spacer_right)
            else:
                # All text fields are now multiline with Unicode support
                field_layout.add_widget(label)
                text_field = MDTextField(text="", multiline=True, size_hint_x=0.7,
                                       max_text_length=4000, font_name='UnicodeFont')
                text_field.height = dp(120)
                text_field.bind(on_key_down=self.on_key_down)
                text_field.bind(text=self.on_text_change)  # Add Unicode handling
                text_field.bind(focus=self.on_text_field_focus)  # Ensure cursor visibility on focus
                if field == "RCA":
                    text_field.bind(text=self.on_rca_text_change)
                elif field == "Season/Episode Number":
                    text_field.bind(on_text_validate=self.format_season_episode)
                    text_field.bind(focus=self.on_season_episode_focus)
                self.entries[field] = text_field
                field_layout.add_widget(text_field)
            
            field_layout.height = dp(120)  # All fields are now multiline
            form_layout.add_widget(field_layout)
        
        # Task Section (First)
        task_card = MDCard(padding=dp(20), spacing=dp(10), adaptive_height=True)
        task_layout = MDBoxLayout(orientation="vertical", spacing=dp(15), adaptive_height=True)
        
        task_title = MDLabel(text="Task", theme_text_color="Primary", font_style="H6")
        task_layout.add_widget(task_title)
        
        # Task fields (excluding comment fields)
        task_fields = [
            "AHT", "Series GTI", "Series Name", "Line of business", "Territory (T)", 
            "Partner (P)", "Impressions", "Season/Episode", "Input Date", 
            "Product Episode Missing", "Product Season Missing", "Offer Episode Missing", 
            "Offer Season Missing", "Last Episode Missing", "Wrong Content Playing", "EDP Link"
        ]
        
        self.task_entries = {}
        for field in task_fields:
            field_layout = MDBoxLayout(orientation="horizontal", spacing=dp(10), 
                                     adaptive_height=True, size_hint_y=None)
            
            label = MDLabel(text=field, size_hint_x=0.3, theme_text_color="Primary",
                           font_style="Subtitle1", )
            field_layout.add_widget(label)
            
            if field == "EDP Link":
                # Clickable button for EDP Link
                # Add spacer to center the button
                spacer_left = MDLabel(text="", size_hint_x=0.1)
                field_layout.add_widget(spacer_left)
                link_field = MDRaisedButton(text="Link", size_hint_x=0.3,
                                          on_release=self.open_edp_link)
                self.task_entries[field] = link_field
                field_layout.add_widget(link_field)
                # Add spacer to balance the layout
                spacer_right = MDLabel(text="", size_hint_x=0.3)
                field_layout.add_widget(spacer_right)
            elif field == "Line of business":
                # Dropdown for Line of business
                # Add spacer to center the button
                spacer_left = MDLabel(text="", size_hint_x=0.1)
                field_layout.add_widget(spacer_left)
                lob_field = MDRaisedButton(text="Select Line of Business", size_hint_x=0.3,
                                         on_release=self.open_lob_menu)
                self.task_entries[field] = lob_field
                field_layout.add_widget(lob_field)
                # Add spacer to balance the layout
                spacer_right = MDLabel(text="", size_hint_x=0.3)
                field_layout.add_widget(spacer_right)
            elif field == "Season/Episode":
                # Create a horizontal layout for text field and View Details button
                se_layout = MDBoxLayout(orientation="horizontal", spacing=dp(5), size_hint_x=0.7,
                                      adaptive_height=True, size_hint_y=None)

                # Text field for Season/Episode data with adaptive height
                text_field = MDTextField(text="", multiline=True, size_hint_x=0.8,
                                       max_text_length=2000, font_name='UnicodeFont',
                                       size_hint_y=None)
                text_field.bind(minimum_height=text_field.setter('height'))
                text_field.height = dp(60)  # Minimum height
                text_field.bind(text=self.on_text_change)  # Add Unicode handling
                text_field.bind(focus=self.on_text_field_focus)  # Ensure cursor visibility on focus
                text_field.bind(text=self.on_season_episode_text_change)  # Handle height adjustment
                self.task_entries[field] = text_field

                # View Details button with fixed height
                view_btn = MDRaisedButton(text="View Details", size_hint_x=0.2,
                                        size_hint_y=None, height=dp(40),
                                        on_release=self.view_season_episode_details)

                # Add widgets to layout
                se_layout.add_widget(text_field)
                se_layout.add_widget(view_btn)
                field_layout.add_widget(se_layout)
            else:
                # Regular text field
                text_field = MDTextField(text="", multiline=True, size_hint_x=0.7,
                                       max_text_length=2000, font_name='UnicodeFont')
                text_field.height = dp(60)
                text_field.bind(text=self.on_text_change)  # Add Unicode handling
                text_field.bind(focus=self.on_text_field_focus)  # Ensure cursor visibility on focus
                self.task_entries[field] = text_field
                field_layout.add_widget(text_field)

            # Set adaptive height for Season/Episode field, fixed height for others
            if field == "Season/Episode":
                field_layout.adaptive_height = True
                field_layout.size_hint_y = None
                field_layout.bind(minimum_height=field_layout.setter('height'))
            else:
                field_layout.height = dp(60)
            task_layout.add_widget(field_layout)
        
        task_card.add_widget(task_layout)
        content.add_widget(task_card)
        
        # Comment Section
        comment_card = MDCard(padding=dp(20), spacing=dp(10), adaptive_height=True)
        comment_layout = MDBoxLayout(orientation="vertical", spacing=dp(15), adaptive_height=True)
        
        comment_title = MDLabel(text="Comment Section", theme_text_color="Primary", font_style="H6", )
        comment_layout.add_widget(comment_title)
        
        # Comment fields
        comment_fields = [
            "Code Expansion", "Parity Check", "POM Alias", "Issue Detail", "Action", 
            "Resolver Group", "Additional Comments", "Prime", "TVOD", "Channel"
        ]
        
        for field in comment_fields:
            field_layout = MDBoxLayout(orientation="horizontal", spacing=dp(10), 
                                     adaptive_height=True, size_hint_y=None)
            
            label = MDLabel(text=field, size_hint_x=0.3, theme_text_color="Primary",
                           font_style="Subtitle1", )
            field_layout.add_widget(label)
            
            if field == "Code Expansion":
                # Dropdown for Code Expansion
                # Add spacer to center the button
                spacer_left = MDLabel(text="", size_hint_x=0.1)
                field_layout.add_widget(spacer_left)
                code_field = MDRaisedButton(text="Select Code Expansion", size_hint_x=0.3,
                                           on_release=self.open_code_expansion_menu)
                self.task_entries[field] = code_field
                field_layout.add_widget(code_field)
                # Add spacer to balance the layout
                spacer_right = MDLabel(text="", size_hint_x=0.3)
                field_layout.add_widget(spacer_right)
            elif field == "Action":
                # Dropdown for Action
                # Add spacer to center the button
                spacer_left = MDLabel(text="", size_hint_x=0.1)
                field_layout.add_widget(spacer_left)
                action_field = MDRaisedButton(text="Select Action", size_hint_x=0.3,
                                            on_release=self.open_action_menu)
                self.task_entries[field] = action_field
                field_layout.add_widget(action_field)
                # Add spacer to balance the layout
                spacer_right = MDLabel(text="", size_hint_x=0.3)
                field_layout.add_widget(spacer_right)
            elif field == "Resolver Group":
                # Dropdown for Resolver Group
                # Add spacer to center the button
                spacer_left = MDLabel(text="", size_hint_x=0.1)
                field_layout.add_widget(spacer_left)
                resolver_field = MDRaisedButton(text="Select Resolver Group", size_hint_x=0.3,
                                              on_release=self.open_resolver_menu)
                self.task_entries[field] = resolver_field
                field_layout.add_widget(resolver_field)
                # Add spacer to balance the layout
                spacer_right = MDLabel(text="", size_hint_x=0.3)
                field_layout.add_widget(spacer_right)
            else:
                text_field = MDTextField(text="", multiline=True, size_hint_x=0.7,
                                       max_text_length=2000, font_name='UnicodeFont')
                text_field.height = dp(60)
                text_field.bind(text=self.on_text_change)  # Add Unicode handling
                text_field.bind(focus=self.on_text_field_focus)  # Ensure cursor visibility on focus
                self.task_entries[field] = text_field
                field_layout.add_widget(text_field)
            
            field_layout.height = dp(60)
            comment_layout.add_widget(field_layout)
        
        comment_card.add_widget(comment_layout)
        content.add_widget(comment_card)
        
        # RCA Section (Second)
        rca_card = MDCard(padding=dp(20), spacing=dp(10), adaptive_height=True)
        rca_layout = MDBoxLayout(orientation="vertical", spacing=dp(10), adaptive_height=True)
        
        rca_title = MDLabel(text="RCA", theme_text_color="Primary", font_style="H6", )
        rca_layout.add_widget(rca_title)
        
        # RCA buttons grid
        rca_grid = MDGridLayout(cols=4, spacing=dp(10), adaptive_height=True)
        
        for rca_button, rca_text in rca_dict.items():
            btn = MDFlatButton(text=rca_button, on_release=self.create_rca_callback(rca_text))
            btn.text_color = self.theme_cls.primary_color
            rca_grid.add_widget(btn)
        
        # Reset RCA button
        reset_rca_btn = MDRaisedButton(text="Reset RCA Selection", md_bg_color="red",
                                      on_release=self.reset_rca_selection)
        
        rca_layout.add_widget(rca_grid)
        rca_layout.add_widget(reset_rca_btn)
        rca_card.add_widget(rca_layout)
        content.add_widget(rca_card)
        
        # Add + and Remove - buttons at the end of main form
        add_btn_layout = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(60), spacing=dp(10))
        add_btn_layout.add_widget(MDLabel(text="", size_hint_x=0.6))  # Spacer
        add_btn = MDRaisedButton(text="Add +", size_hint_x=0.2, md_bg_color="green", on_release=self.add_new_form)
        remove_btn = MDRaisedButton(text="Remove -", size_hint_x=0.2, md_bg_color="red", on_release=self.remove_form)
        add_btn_layout.add_widget(add_btn)
        add_btn_layout.add_widget(remove_btn)
        form_layout.add_widget(add_btn_layout)
        
        # Fields Section (Third)
        form_card.add_widget(form_layout)
        content.add_widget(form_card)
        
        # Issue Blurbs Section (Fourth)
        blurbs_card = MDCard(padding=dp(20), spacing=dp(10), adaptive_height=True)
        blurbs_layout = MDBoxLayout(orientation="vertical", spacing=dp(10), adaptive_height=True)
        
        blurbs_title = MDLabel(text="Issue Blurbs", theme_text_color="Primary", font_style="H6", )
        blurbs_layout.add_widget(blurbs_title)
        
        # Blurb buttons grid
        blurbs_grid = MDGridLayout(cols=7, spacing=dp(10), adaptive_height=True)
        
        for blurb, issue in blurbs_dict.items():
            btn = MDFlatButton(text=blurb, on_release=lambda x, i=issue, b=blurb: self.set_issue(i, b))
            btn.text_color = self.theme_cls.primary_color
            blurbs_grid.add_widget(btn)
        
        # Reset button
        reset_btn = MDRaisedButton(text="Reset Selection", md_bg_color="red",
                                  on_release=self.reset_issue_selection)
        
        blurbs_layout.add_widget(blurbs_grid)
        blurbs_layout.add_widget(reset_btn)
        blurbs_card.add_widget(blurbs_layout)
        content.add_widget(blurbs_card)
        
        # CTI Section (Fifth)
        cti_card = MDCard(padding=dp(20), spacing=dp(10), adaptive_height=True)
        cti_layout = MDBoxLayout(orientation="vertical", spacing=dp(20), adaptive_height=True)
        
        cti_title = MDLabel(text="CTI", theme_text_color="Primary", font_style="H6", )
        cti_layout.add_widget(cti_title)
        
        cti_row = MDBoxLayout(orientation="horizontal", spacing=dp(20), 
                             adaptive_height=True, size_hint_y=None, height=dp(60))
        
        # CTI Dropdowns
        cti_values = ["Digital Video", "Digiflex", "TV Series Integrity"]
        for i, value in enumerate(cti_values):
            cti_btn = MDRaisedButton(text=value, size_hint_x=0.3,
                                   on_release=lambda x, idx=i: self.open_cti_menu(x, idx))
            self.cti_buttons.append(cti_btn)
            cti_row.add_widget(cti_btn)
        
        cti_layout.add_widget(cti_row)
        cti_card.add_widget(cti_layout)
        content.add_widget(cti_card)
        
        scroll.add_widget(content)
        main_layout.add_widget(scroll)
        
        return main_layout

    def on_text_change(self, instance, text):
        """Handle text changes with Unicode normalization"""
        try:
            normalized_text = self.normalize_unicode_text(text)
            if normalized_text != text:
                instance.text = normalized_text

            # Auto-save after significant text changes
            # Use a simple counter to avoid saving too frequently
            if not hasattr(self, '_text_change_counter'):
                self._text_change_counter = 0

            self._text_change_counter += 1
            if self._text_change_counter >= 20:  # Save every 20 text changes
                self.auto_save_data()
                self._text_change_counter = 0
        except Exception as e:
            print(f"Text normalization error: {e}")

    def on_season_episode_text_change(self, instance, text):
        """Handle Season/Episode text changes and adjust height dynamically"""
        try:
            if not text:
                # Reset to minimum height if no text
                instance.height = dp(60)
                print("Season/Episode field reset to minimum height")
                return

            # Calculate required height based on text content
            lines = text.count('\n') + 1

            # Also consider text wrapping - estimate wrapped lines
            # Assume average character width and field width
            chars_per_line = 80  # Approximate characters per line in the text field
            for line in text.split('\n'):
                if len(line) > chars_per_line:
                    # Add extra lines for text wrapping
                    wrapped_lines = (len(line) // chars_per_line)
                    lines += wrapped_lines

            # Estimate height: base height + additional height per line
            base_height = dp(60)
            line_height = dp(18)
            max_height = dp(300)  # Maximum height to prevent excessive expansion
            min_height = dp(60)   # Minimum height

            # Calculate new height
            calculated_height = base_height + (lines - 1) * line_height
            new_height = max(min_height, min(calculated_height, max_height))

            # Update the text field height
            old_height = instance.height
            instance.height = new_height
            print(f"Season/Episode field height adjusted: {old_height} -> {new_height} (lines: {lines})")

        except Exception as e:
            print(f"Season/Episode height adjustment error: {e}")
    
    def on_rca_text_change(self, instance, text):
        # Normalize Unicode text first
        normalized_text = self.normalize_unicode_text(text)
        if normalized_text != text:
            print(f"RCA text normalization: '{text}' -> '{normalized_text}'")
            instance.text = normalized_text

        # Debug: Print when RCA text changes
        print(f"RCA text changed to: '{text}'")

    def open_offer_menu_click(self, instance):
        if not hasattr(self, 'selected_offers'):
            self.selected_offers = set()
        
        menu_items = [
            {"text": "Prime", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type("Prime")},
            {"text": "TVOD", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type("TVOD")},
            {"text": "Channels", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type("Channels")},
            {"text": "FVOD", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type("FVOD")},
            {"text": "AVOD", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type("AVOD")},
            {"text": "POEST", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type("POEST")},
            {"text": "SVOD", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type("SVOD")},
        ]
        self.offer_type_menu = MDDropdownMenu(caller=instance, items=menu_items)
        self.offer_type_menu.open()

    def toggle_offer_type(self, value):
        if value in self.selected_offers:
            self.selected_offers.remove(value)
        else:
            self.selected_offers.add(value)
        
        # Update button text with selected offers
        if self.selected_offers:
            self.entries["Offer Type"].text = ", ".join(sorted(self.selected_offers))
        else:
            self.entries["Offer Type"].text = "Select Offer Type"

    def open_cti_menu(self, instance, index):
        cti_options = ["Digital Video", "Digiflex", "TV Series Integrity"]
        # Show only the specific option for each dropdown
        specific_option = cti_options[index]
        menu_items = [{"text": specific_option, "viewclass": "OneLineListItem", 
                      "on_release": lambda: self.set_cti_value(instance, specific_option, index)}]
        menu = MDDropdownMenu(caller=instance, items=menu_items)
        if len(self.cti_menus) <= index:
            self.cti_menus.extend([None] * (index + 1 - len(self.cti_menus)))
        self.cti_menus[index] = menu
        menu.open()

    def set_cti_value(self, button, value, index):
        button.text = value
        self.cti_values[index] = value
        self.cti_menus[index].dismiss()

    def format_season_episode(self, instance):
        import re
        text = instance.text
        # Pattern to match S##-(episode_numbers) format
        formatted_text = re.sub(r'(S\d+)-(\([^)]+\))', r'\1-E\2', text)
        if formatted_text != text:
            instance.text = formatted_text

    def on_text_field_focus(self, instance, focus):
        """Handle text field focus to ensure cursor visibility"""
        if focus:
            # Ensure cursor is visible when text field gets focus
            Window.show_cursor = True
            try:
                Window.set_system_cursor('ibeam')  # Text cursor
            except:
                Window.set_system_cursor('arrow')  # Fallback to arrow
        else:
            # Reset to arrow cursor when losing focus
            try:
                Window.set_system_cursor('arrow')
            except:
                pass

    def on_season_episode_focus(self, instance, focus):
        # Call the general focus handler first
        self.on_text_field_focus(instance, focus)

        if not focus:  # When field loses focus
            self.format_season_episode(instance)
    
    def on_key_down(self, instance, keycode, text, modifiers):
        if keycode == 9:  # Tab key
            field_names = [f for f in fields if f != "Offer Type"]
            current_field = None
            for field, entry in self.entries.items():
                if entry == instance:
                    current_field = field
                    break
            if current_field and current_field in field_names:
                current_index = field_names.index(current_field)
                next_index = (current_index + 1) % len(field_names)
                next_field = self.entries[field_names[next_index]]
                next_field.focus = True
            return True
        return False

    def submit_data(self, data):
        user_id = getpass.getuser()
        today = datetime.date.today()
        week_num = today.isocalendar()[1]

        try:
            conn_str = (
                r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
                fr'DBQ={access_db_path};'
            )
            conn = pyodbc.connect(conn_str)
            cursor = conn.cursor()

            insert_query = f"""
            INSERT INTO [TT Template] (
                [RCA], [Territory], [Offer Type], [Partner], [Series GTI],
                [Series Name], [Season/Episode Number], [EDP Link], [EDP Link 2], [D2C Link], [D2C Link 2],
                [Detail Page Link], [Detail Page Link 2], [Avails Link], [Avails Link 2], [DEAL Link], [WMV Link], [Screenshot], [Issue], [Issue Name],
                [Category (C)], [Type (T)], [Item (I)],
                [Date], [Week], [User ID], [Start Time], [End Time], [Total Time],
                [AHT], [Season/Episode], [Territory (T)], [Partner (P)], [Line of business], [Code Expansion], [Parity Check], [POM Alias], [Issue Detail], [Action], [Resolver Group], [Additional Comments], [Prime], [TVOD], [Channel], [Impressions],
                [Break Total Time]
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """  # Removed Break Start Time and Break End Time fields

            self.end_time = datetime.datetime.now().time()
            
            # Calculate Total Time in minutes excluding break time
            if self.start_time:
                start_datetime = datetime.datetime.combine(today, self.start_time)
                end_datetime = datetime.datetime.combine(today, self.end_time)
                
                # Handle case where task spans midnight
                if end_datetime < start_datetime:
                    end_datetime = end_datetime + datetime.timedelta(days=1)
                
                gross_time = (end_datetime - start_datetime).total_seconds() / 60
                
                # Add any current break time if still in break mode
                if self.break_start_time:
                    current_break_time = datetime.datetime.now().time()
                    break_start_dt = datetime.datetime.combine(today, self.break_start_time)
                    current_break_dt = datetime.datetime.combine(today, current_break_time)
                    
                    # Handle case where current break spans midnight
                    if current_break_dt < break_start_dt:
                        current_break_dt = current_break_dt + datetime.timedelta(days=1)
                    
                    current_break_duration = (current_break_dt - break_start_dt).total_seconds() / 60
                    self.total_break_time += current_break_duration
                    # Reset break start time since we've accounted for it
                    self.break_start_time = None
                
                # Net time is gross time minus break time
                net_time = gross_time - self.total_break_time
                if net_time < 0:
                    net_time = 0  # Ensure we don't have negative time
                
                total_time = round(net_time, 2)
            else:
                # If no start time set, use current time as both start and end
                self.start_time = self.end_time
                total_time = 0.0
            
            # Get Series GTI and Series Name from Task section (with fallback to empty if not available)
            series_gti = self.task_entries.get("Series GTI", MDTextField()).text if hasattr(self, 'task_entries') and "Series GTI" in self.task_entries else ""
            series_name = self.task_entries.get("Series Name", MDTextField()).text if hasattr(self, 'task_entries') and "Series Name" in self.task_entries else ""
            
            # Create data list with proper field mapping and data type handling
            def get_field_value(field_name):
                if field_name == "Series GTI":
                    return series_gti or ""
                elif field_name == "Series Name":
                    return series_name or ""
                elif field_name == "Offer Type":
                    # Handle dropdown button text
                    offer_text = data.get(field_name, "")
                    return offer_text if offer_text != "Select Offer Type" else ""
                elif field_name == "Issue Name":
                    # Handle Issue Name - check if this is main form data or additional form
                    if hasattr(self, 'current_form_data') and self.current_form_data:
                        # For additional forms, get issue name from form's internal storage
                        return str(self.current_form_data.get('_issue_name', ""))
                    else:
                        # For main form
                        return str(self.issue_name or "")
                elif field_name in data:
                    return data[field_name] or ""
                else:
                    return ""
            
            # Build data values list in correct order with proper string conversion
            field_order = ["RCA", "Territory", "Offer Type", "Partner", "Series GTI", "Series Name", "Season/Episode Number", "EDP Link", "EDP Link 2", "D2C Link", "D2C Link 2", "Detail Page Link", "Detail Page Link 2", "Avails Link", "Avails Link 2", "DEAL Link", "WMV Link", "Screenshot", "Issue"]
            data_values = [str(get_field_value(field)) for field in field_order]
            
            # Issue Name is handled in the get_field_value function above
            
            data_values.append(get_field_value("Issue Name"))
            
            # Get combined data for grouped fields from current GTI (if available)
            combined_data = {}
            if hasattr(self, 'current_gti_raw_data') and self.current_gti_index < len(self.current_gti_raw_data):
                raw_data = self.current_gti_raw_data[self.current_gti_index]
                
                # Combine all unique values for grouped fields
                for field_name, excel_field in [("Season/Episode", "Season/Episode"), ("Territory (T)", "Territory"), ("Partner (P)", "Partner"), ("Line of business", "Line of business")]:
                    unique_values = []
                    for row in raw_data:
                        value = row.get(excel_field, '')
                        if pd.notna(value) and str(value).strip():
                            value = str(value).strip()
                            if value not in unique_values:
                                unique_values.append(value)
                    combined_data[field_name] = ', '.join(unique_values)
            
            # Get Task Section data with combined values for grouped fields (with fallbacks)
            def get_task_field_value(field_name, default_text=""):
                if hasattr(self, 'task_entries') and field_name in self.task_entries:
                    return self.task_entries[field_name].text
                return default_text
            
            # Get Impressions data from ALL rows in the current title, not just the selected line of business
            highest_impression = 0
            if hasattr(self, 'current_gti_raw_data') and self.current_gti_index < len(self.current_gti_raw_data):
                # Get all raw data for the current title
                all_title_rows = self.current_gti_raw_data[self.current_gti_index]
                
                # Extract all impression values from all rows in the current title
                for row in all_title_rows:
                    impressions_value = row.get('Impressions', '')
                    if pd.notna(impressions_value) and str(impressions_value).strip():
                        # Process each impression value
                        impression_values = [val.strip() for val in str(impressions_value).split(',')]
                        for val in impression_values:
                            try:
                                # Convert to integer if possible
                                num_val = int(val)
                                highest_impression = max(highest_impression, num_val)
                            except (ValueError, TypeError):
                                # Skip values that can't be converted to integers
                                pass
            
            # Fallback to current field value if no raw data available
            if highest_impression == 0:
                impressions_text = get_task_field_value("Impressions")
                if impressions_text:
                    impression_values = [val.strip() for val in impressions_text.split(',')]
                    for val in impression_values:
                        try:
                            num_val = int(val)
                            highest_impression = max(highest_impression, num_val)
                        except (ValueError, TypeError):
                            pass
            
            # Use the highest impression value or 0 if none found
            impressions_value = str(highest_impression) if highest_impression > 0 else ""
            
            task_data = [
                get_task_field_value("AHT"),
                combined_data.get("Season/Episode", get_task_field_value("Season/Episode")),
                combined_data.get("Territory (T)", get_task_field_value("Territory (T)")),
                combined_data.get("Partner (P)", get_task_field_value("Partner (P)")),
                combined_data.get("Line of business", get_task_field_value("Line of business", "Select Line of Business")),
                get_task_field_value("Code Expansion", "Select Code Expansion"),
                get_task_field_value("Parity Check"),
                get_task_field_value("POM Alias"),
                get_task_field_value("Issue Detail"),
                get_task_field_value("Action", "Select Action"),
                get_task_field_value("Resolver Group", "Select Resolver Group"),
                get_task_field_value("Additional Comments")
            ]
            
            # Get comment section data for Prime, TVOD, Channel
            comment_data = [
                get_task_field_value("Prime"),
                get_task_field_value("TVOD"),
                get_task_field_value("Channel")
            ]
            
            # Ensure proper data types for database insertion
            final_data = []
            final_data.extend(data_values)
            final_data.extend([str(self.cti_values[0]), str(self.cti_values[1]), str(self.cti_values[2])])
            final_data.extend([today, week_num, str(user_id), self.start_time, self.end_time, total_time])
            final_data.extend([str(x) for x in task_data])
            final_data.extend([str(x) for x in comment_data])
            # Add Impressions data to the final data list
            final_data.append(str(impressions_value))
            
            # Add only break total time to the final data list
            break_duration_value = getattr(self, 'break_duration_db', 0.0)
            formatted_break_duration = f"{break_duration_value:.2f}"  # Format as string with 2 decimal places
            
            # Only add Break Total Time to database
            final_data.append(formatted_break_duration)  # Break Total Time as string with 2 decimal places
            
            cursor.execute(insert_query, final_data)
            conn.commit()
            conn.close()
            
            # Add the current task's time to the cumulative total
            self.cumulative_time += total_time
            
            # Update the time display with the cumulative total
            self.time_display.text = f"Total Time: {self.cumulative_time:.2f} min"
            
            # Increment titles processed counter and update display
            # Only increment if this is the main form or the last additional form
            if not getattr(self, '_prevent_auto_move', False):
                self.titles_processed += 1
                self.titles_display.text = f"Titles Processed: {self.titles_processed}"
            
            # Success dialog
            dialog = MDDialog(title="Success", text="Data submitted successfully.")
            dialog.open()

            # Only clear sections after the last form submission
            if not getattr(self, '_prevent_auto_move', False):
                self.clear_all_sections()
            
            # Reset time tracking variables
            self.start_time = None
            self.end_time = None
            self.break_start_time = None
            self.break_end_time = None
            self.total_break_time = 0.0
            
            # Reset break time database variable
            self.break_duration_db = 0.0
            
            # No longer automatically move to next task - user must click "Get Task" button
            if getattr(self, '_prevent_auto_move', False):
                self._prevent_auto_move = False

        except Exception as e:
            # Error dialog
            dialog = MDDialog(title="Database Error", text=f"Failed to write to database.\n{str(e)}")
            dialog.open()

    def preview_data(self, instance):
        data = {field: self.entries[field].text for field in fields}
        
        # Add Series GTI, Series Name from Task section
        series_gti = self.task_entries.get("Series GTI", MDTextField()).text
        series_name = self.task_entries.get("Series Name", MDTextField()).text
        
        # Get Impressions from ALL rows in the current title for preview
        highest_impression = 0
        
        # Check if we have raw data for the current title
        if hasattr(self, 'current_gti_raw_data') and self.current_gti_index < len(self.current_gti_raw_data):
            # Get all raw data for the current title
            all_title_rows = self.current_gti_raw_data[self.current_gti_index]
            
            # Extract all impression values from all rows in the current title
            for row in all_title_rows:
                impressions_value = row.get('Impressions', '')
                if pd.notna(impressions_value) and str(impressions_value).strip():
                    # Process each impression value
                    impression_values = [val.strip() for val in str(impressions_value).split(',')]
                    for val in impression_values:
                        try:
                            # Convert to integer if possible
                            num_val = int(val)
                            highest_impression = max(highest_impression, num_val)
                        except (ValueError, TypeError):
                            # Skip values that can't be converted to integers
                            pass
        
        # Fallback to current field value if no raw data available
        if highest_impression == 0:
            impressions_text = self.task_entries.get("Impressions", MDTextField()).text
            if impressions_text:
                impression_values = [val.strip() for val in impressions_text.split(',')]
                for val in impression_values:
                    try:
                        num_val = int(val)
                        highest_impression = max(highest_impression, num_val)
                    except (ValueError, TypeError):
                        pass
                        
        impressions = str(highest_impression) if highest_impression > 0 else ""
        
        # Create complete data for preview including Series GTI, Series Name, and Impressions
        complete_data = data.copy()
        complete_data["Series GTI"] = series_gti
        complete_data["Series Name"] = series_name
        complete_data["Impressions"] = impressions

        # Check only main form fields, task fields are optional
        if any(v.strip() == "" for v in data.values()):
            dialog = MDDialog(title="Missing Data", text="Please fill in all main form fields before submitting.")
            dialog.open()
            return

        # Create preview dialog with complete data including Issue Name and Impressions
        preview_fields = ["RCA", "Territory", "Offer Type", "Partner", "Series GTI", "Series Name", "Season/Episode Number", "EDP Link", "EDP Link 2", "D2C Link", "D2C Link 2", "Detail Page Link", "Detail Page Link 2", "Avails Link", "Avails Link 2", "DEAL Link", "WMV Link", "Screenshot", "Issue", "Issue Name", "Impressions"]
        complete_data["Issue Name"] = self.issue_name
        preview_text = "\n".join([f"{field}: {complete_data.get(field, '')}" for field in preview_fields])
        
        def confirm_submit(instance):
            preview_dialog.dismiss()
            self.submit_data(data)
        
        preview_dialog = MDDialog(
            title="Preview Data",
            text=preview_text,
            buttons=[
                MDFlatButton(text="Cancel", on_release=lambda x: preview_dialog.dismiss()),
                MDRaisedButton(text="Confirm", on_release=confirm_submit)
            ]
        )
        preview_dialog.open()

    def set_issue(self, issue_text, button_name):
        """Set issue for active form"""
        if self.active_form == 'main':
            if len(self.selected_issues) < 2:
                self.selected_issues.append(issue_text)
                if len(self.selected_issues) == 1:
                    self.issue_name = button_name
            
            issue_field = self.entries["Issue"]
            if len(self.selected_issues) == 1:
                issue_field.text = self.selected_issues[0]
            elif len(self.selected_issues) == 2:
                issue_field.text = f"{self.selected_issues[0]} due to {self.selected_issues[1]}"
            issue_field.focus = True
            issue_field.focus = False
        else:
            # Update active additional form
            if len(self.active_form['_issue_items']) < 2:
                self.active_form['_issue_items'].append(issue_text)
                if len(self.active_form['_issue_items']) == 1:
                    self.active_form['_issue_name'] = button_name
            
            if len(self.active_form['_issue_items']) == 1:
                self.active_form["Issue"].text = self.active_form['_issue_items'][0]
            elif len(self.active_form['_issue_items']) == 2:
                self.active_form["Issue"].text = f"{self.active_form['_issue_items'][0]} due to {self.active_form['_issue_items'][1]}"

    def reset_issue_selection(self, instance):
        """Reset active form's issue selection"""
        if self.active_form == 'main':
            self.selected_issues = []
            self.issue_name = ""
            self.entries["Issue"].text = ""
        else:
            self.active_form['_issue_items'] = []
            self.active_form['_issue_name'] = ""
            self.active_form["Issue"].text = ""
    
    def create_rca_callback(self, rca_text):
        """Create a callback function for RCA button"""
        return lambda x: self.set_rca(rca_text)
    
    def set_rca(self, rca_text):
        """Add RCA text to the active form's RCA field"""
        if self.active_form == 'main':
            # Check if this RCA text is already in the list to avoid duplicates
            if rca_text not in self.selected_rca_items:
                # Append the new RCA text to the list
                self.selected_rca_items.append(rca_text)
                rca_field = self.entries["RCA"]

                # Temporarily unbind the text change event to prevent interference
                rca_field.unbind(text=self.on_rca_text_change)

                # Format all RCA items with issue numbers
                formatted_text = "\n\n".join([f"Issue {i+1}: {item}" for i, item in enumerate(self.selected_rca_items)])
                rca_field.text = formatted_text

                # Re-bind the text change event
                rca_field.bind(text=self.on_rca_text_change)

                print(f"Added RCA item {len(self.selected_rca_items)}: {rca_text}")
                print(f"Current RCA items: {self.selected_rca_items}")
        else:
            # Update active additional form
            # Check if this RCA text is already in the list to avoid duplicates
            if rca_text not in self.active_form['_rca_items']:
                # Append the new RCA text to the form's RCA items list
                self.active_form['_rca_items'].append(rca_text)

                # Format all RCA items with issue numbers
                formatted_text = "\n\n".join([f"Issue {i+1}: {item}" for i, item in enumerate(self.active_form['_rca_items'])])
                self.active_form["RCA"].text = formatted_text

                print(f"Added RCA item {len(self.active_form['_rca_items'])} to additional form: {rca_text}")
                print(f"Current additional form RCA items: {self.active_form['_rca_items']}")
    
    def reset_rca_selection(self, instance):
        """Reset active form's RCA field and selection list"""
        if self.active_form == 'main':
            self.selected_rca_items = []
            self.entries["RCA"].text = ""
        else:
            self.active_form['_rca_items'] = []
            self.active_form["RCA"].text = ""

    def import_excel(self, instance):
        try:
            file_path = filedialog.askopenfilename(
                title="Select Excel File",
                filetypes=[("Excel files", "*.xlsx *.xls")]
            )
            if file_path:
                # Read Excel with proper encoding support
                df = pd.read_excel(file_path, engine='openpyxl')
                # Normalize Unicode in all text columns
                for col in df.select_dtypes(include=['object']).columns:
                    df[col] = df[col].apply(lambda x: self.normalize_unicode_text(x) if pd.notna(x) else x)
                # Keep NaN as NaN, don't convert to 'NA' string
                self.excel_data = df.to_dict('records')
                dialog = MDDialog(title="Success", text=f"Imported {len(self.excel_data)} records.")
                dialog.open()
                self.auto_fill_tickets()
        except Exception as e:
            dialog = MDDialog(title="Import Error", text=f"Failed to import Excel file.\n{str(e)}")
            dialog.open()

    def auto_fill_tickets(self):
        if not self.excel_data:
            return
        
        try:
            service = Service(ChromeDriverManager().install())
            chrome_options = webdriver.ChromeOptions()
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            # # Add UTF-8 encoding support for international characters
            # chrome_options.add_argument("--lang=en-US")
            # chrome_options.add_argument("--accept-lang=en-US,en,ja,*")
            # chrome_options.add_argument("--disable-web-security")
            # chrome_options.add_argument("--disable-features=VizDisplayCompositor")
            
            driver = webdriver.Chrome(service=service, options=chrome_options)
            driver.set_page_load_timeout(60)  # Increased timeout for Maxis API
            driver.implicitly_wait(10)  # Increased implicit wait

            # Execute script to hide automation indicators
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # # Execute script to hide automation indicators and set UTF-8 encoding
            # driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            # driver.execute_script("document.charset = 'UTF-8';")
            # driver.execute_script("document.characterSet = 'UTF-8';")
            
            # Wait after browser is opened
            time.sleep(3)
            
            for i, row in enumerate(self.excel_data):
                row_start_time = datetime.datetime.now().time()  # Track start time for each row
                try:
                    print(f"Processing row {i+1}...")
                    
                    # Retry logic for URL loading to handle Maxis API errors
                    url_loaded = False
                    for url_attempt in range(3):
                        try:
                            print(f"Loading URL (attempt {url_attempt+1})...")
                            
                            # Clear any existing cookies/cache that might cause Maxis issues
                            if url_attempt > 0:
                                driver.delete_all_cookies()
                                driver.execute_script("window.localStorage.clear();")
                                driver.execute_script("window.sessionStorage.clear();")
                            
                            driver.get("https://t.corp.amazon.com/create/templates/54f1b84d-9ede-4987-8de1-c23b35601ec2")
                            
                            # Wait for page to load completely with error checking
                            WebDriverWait(driver, 30).until(
                                lambda d: d.execute_script("return document.readyState") == "complete"
                            )
                            
                            # Check if page loaded without Maxis error
                            page_source = driver.page_source.lower()
                            if "maxis" in page_source and "error" in page_source:
                                raise Exception("Maxis API error detected in page")
                            
                            time.sleep(5)  # Additional wait for full page load
                            url_loaded = True
                            print("Page loaded successfully")
                            break
                        except Exception as url_error:
                            print(f"URL load attempt {url_attempt+1} failed: {url_error}")
                            if url_attempt < 2:
                                print("Retrying URL load...")
                                time.sleep(5)
                            else:
                                print(f"Failed to load URL after 3 attempts for row {i+1}")
                    
                    if not url_loaded:
                        print(f"Skipping row {i+1} due to URL load failure")
                        continue
                    
                    print("Finding title field...")
                    
                    # Wait longer for title field to handle slow loading
                    title_field = WebDriverWait(driver, 30).until(
                        EC.element_to_be_clickable((By.XPATH, '//*[@id="ticket-title"]'))
                    )
                    print("Title field found")
                    
                    def get_clean_value(key):
                        import pandas as pd
                        value = row.get(key)
                        if pd.isna(value) or str(value).strip() in ['NA', 'nan', '']:
                            return ''
                        # Normalize Unicode first with enhanced handling
                        clean_value = self.normalize_unicode_text(str(value))
                        # Clean the value by removing unwanted characters and duplicates
                        clean_value = clean_value.replace('\r', '').replace('\n', ' ').replace('_x000D_', '')
                        # Remove everything after # including duplicates
                        if '#' in clean_value:
                            clean_value = clean_value.split('#')[0]
                        # Aggressive cleanup - remove all instances of the key label
                        while True:
                            if clean_value.startswith(key + ': '):
                                clean_value = clean_value[len(key)+2:].strip()
                            elif clean_value.startswith(key + ':'):
                                clean_value = clean_value[len(key)+1:].strip()
                            elif clean_value.startswith(key + ' : '):
                                clean_value = clean_value[len(key)+3:].strip()
                            else:
                                break
                        return clean_value.strip()
                        # # Final Unicode normalization to ensure proper display
                        # return self.normalize_unicode_text(clean_value.strip())
                    
                    issue_name = get_clean_value('Issue Name') or get_clean_value('Issue') or 'Issue'
                    series_name = get_clean_value('Series Name') or 'Series'
                    partner = get_clean_value('Partner') or 'Partner'
                    title_text = f"TV Series Integrity | {issue_name} | {series_name} | {partner}"
                    
                    # Multiple attempts to fill title field
                    print(f"Title text to fill: {title_text}")
                    
                    title_filled = False
                    for attempt in range(5):  # Increased attempts
                        try:
                            print(f"Title fill attempt {attempt+1}...")
                            
                            # Method 1: Enhanced clipboard method with UTF-8 encoding
                            if attempt == 0:
                                unicode_text = self.normalize_unicode_text(title_text)
                                # Set clipboard with explicit UTF-8 encoding
                                try:
                                    import win32clipboard
                                    win32clipboard.OpenClipboard()
                                    win32clipboard.EmptyClipboard()
                                    win32clipboard.SetClipboardData(win32clipboard.CF_UNICODETEXT, unicode_text)
                                    win32clipboard.CloseClipboard()
                                except ImportError:
                                    # Fallback to pyperclip
                                    pyperclip.copy(unicode_text)
                                
                                title_field.click()
                                time.sleep(0.5)
                                title_field.send_keys(Keys.CONTROL + 'a')
                                time.sleep(0.3)
                                title_field.send_keys(Keys.CONTROL + 'v')
                                time.sleep(0.5)
                            
                            # Method 2: Direct send_keys with clear
                            elif attempt == 1:
                                title_field.click()
                                time.sleep(0.3)
                                title_field.clear()
                                time.sleep(0.3)
                                unicode_text = self.normalize_unicode_text(title_text)
                                title_field.send_keys(unicode_text)
                                time.sleep(0.5)
                            
                            # Method 3: Enhanced JavaScript approach with UTF-8 handling
                            else:
                                unicode_text = self.normalize_unicode_text(title_text)
                                # Use JavaScript to properly handle Unicode characters
                                js_script = f"""
                                var field = arguments[0];
                                var text = arguments[1];
                                field.focus();
                                field.value = '';
                                field.value = text;
                                field.dispatchEvent(new Event('input', {{ bubbles: true }}));
                                field.dispatchEvent(new Event('change', {{ bubbles: true }}));
                                """
                                driver.execute_script(js_script, title_field, unicode_text)
                                time.sleep(0.5)
                            
                            # Verify title was filled
                            current_value = title_field.get_attribute('value')
                            if current_value and current_value.strip():
                                title_filled = True
                                print(f"Title filled: {current_value[:30]}...")
                                break
                                
                        except Exception as e:
                            print(f"Title attempt {attempt+1} failed: {e}")
                    
                    if not title_filled:
                        print(f"Skipping row {i+1} - title not filled")
                        continue
                    
                    print("Title filled successfully")
                    
                    # Find description field
                    print("Finding description field...")
                    description_field = WebDriverWait(driver, 15).until(
                        EC.element_to_be_clickable((By.XPATH, '//*[@id="markdown-editor"]'))
                    )
                    print("Description field found")
                    
                    # Build detailed description
                    description_parts = []
                    description_parts.append("Hi Team,")
                    
                    description_parts.append("")  # Empty line
                    
                    if get_clean_value('Issue'): description_parts.append(f"Issue Description : {get_clean_value('Issue')}")
                    if get_clean_value('RCA'): description_parts.append(f"Root cause (RCA) : {get_clean_value('RCA')}")
                    if get_clean_value('Series Name'): description_parts.append(f"Title Name : {get_clean_value('Series Name')}")
                    if get_clean_value('Series GTI'): description_parts.append(f"Series GTI : {get_clean_value('Series GTI')}")
                    if get_clean_value('Season/Episode Number'): description_parts.append(f"Season/Episode Number: {get_clean_value('Season/Episode Number')}")
                    if get_clean_value('Offer Type'): description_parts.append(f"Offer Type : {get_clean_value('Offer Type')}")
                    
                    description_parts.append("")  # Empty line
                    if get_clean_value('Territory'): description_parts.append(f"Territory: {get_clean_value('Territory')}")
                    if get_clean_value('EDP Link'): description_parts.append(f"EDP Link: {get_clean_value('EDP Link')}")
                    if get_clean_value('EDP Link 2'): description_parts.append(f"EDP Link 2: {get_clean_value('EDP Link 2')}")
                    if get_clean_value('Detail Page Link'): description_parts.append(f"Detail Page Link: {get_clean_value('Detail Page Link')}")
                    if get_clean_value('Detail Page Link 2'): description_parts.append(f"Detail Page Link 2: {get_clean_value('Detail Page Link 2')}")
                    if get_clean_value('D2C Link'): description_parts.append(f"D2C Link: {get_clean_value('D2C Link')}")
                    if get_clean_value('D2C Link 2'): description_parts.append(f"D2C Link 2: {get_clean_value('D2C Link 2')}")
                    if get_clean_value('Avails Link'): description_parts.append(f"Avails Link: {get_clean_value('Avails Link')}")
                    if get_clean_value('Avails Link 2'): description_parts.append(f"Avails Link 2: {get_clean_value('Avails Link 2')}")
                    if get_clean_value('DEAL Link'): description_parts.append(f"DEAL Link: {get_clean_value('DEAL Link')}")
                    if get_clean_value('WMV Link'): description_parts.append(f"WMV Link: {get_clean_value('WMV Link')}")
                    
                    description_parts.append("")  # Empty line
                    
                    if get_clean_value('Screenshot'): description_parts.append(f"Screenshot/Attachment: {get_clean_value('Screenshot')}")
                    
                    description_text = "\n".join(description_parts)
                    
                    # Fill description using enhanced clipboard for better Unicode support
                    print("Filling description field...")
                    unicode_description = self.normalize_unicode_text(description_text)
                    
                    # Enhanced clipboard handling for Unicode
                    try:
                        import win32clipboard
                        win32clipboard.OpenClipboard()
                        win32clipboard.EmptyClipboard()
                        win32clipboard.SetClipboardData(win32clipboard.CF_UNICODETEXT, unicode_description)
                        win32clipboard.CloseClipboard()
                        print("Used Windows clipboard API for Unicode text")
                    except ImportError:
                        # Fallback to pyperclip
                        pyperclip.copy(unicode_description)
                        print("Used pyperclip for text copy")
                    
                    description_field.click()
                    time.sleep(0.3)
                    description_field.send_keys(Keys.CONTROL + 'a')
                    time.sleep(0.2)
                    description_field.send_keys(Keys.CONTROL + 'v')
                    time.sleep(0.5)
                    print("Description field filled successfully")
                    

                    
                    # Wait before clicking create button
                    print("Waiting 5 seconds before clicking create button...")
                    time.sleep(5)
                    
                    # Click create button using specific XPath
                    button_clicked = False
                    try:
                        print("Searching for Create button...")
                        create_button = WebDriverWait(driver, 10).until(
                            EC.element_to_be_clickable((By.XPATH, '//*[@id="sim-create"]/div/div[3]/div/div/div/div/div[2]/button'))
                        )
                        driver.execute_script("arguments[0].scrollIntoView(true);", create_button)
                        time.sleep(1)
                        driver.execute_script("arguments[0].click();", create_button)
                        button_clicked = True
                        print(f"Create button clicked successfully for row {i+1}")
                    except Exception as e:
                        print(f"Failed to click create button: {e}")
                    
                    if button_clicked:
                        print(f"Waiting for ticket {i+1} creation to complete...")
                        time.sleep(10)
                    else:
                        print(f"WARNING: Failed to click create button for row {i+1}")
                        time.sleep(3)
                    
                    print(f"Row {i+1} processed successfully")
                    
                except Exception as row_error:
                    print(f"Error processing row {i+1}: {row_error}")
                    continue
            
            driver.quit()
            print("All rows processed")
            
            try:
                driver.quit()
            except:
                pass
            dialog = MDDialog(title="Success", text="All tickets auto-filled successfully.")
            dialog.open()
            
        except Exception as e:
            try:
                driver.quit()
            except:
                pass
            dialog = MDDialog(title="Automation Error", text=f"Failed to auto-fill tickets.\n{str(e)}")
            dialog.open()
    
    def open_task_dialog(self, instance):
        """Open task dialog to load data from Access database or upload Excel"""
        # Create dialog content
        content = MDBoxLayout(orientation="vertical", spacing=dp(20), size_hint_y=None, height=dp(150))
        
        # Add buttons for both options
        db_btn = MDRaisedButton(
            text="Load from Database",
            size_hint_x=1,
            on_release=self.load_from_database
        )
        excel_btn = MDRaisedButton(
            text="Load from Excel",
            size_hint_x=1,
            on_release=self.load_from_excel
        )
        
        content.add_widget(db_btn)
        content.add_widget(excel_btn)
        
        # Create dialog
        self.task_dialog = MDDialog(
            title="Select Data Source",
            type="custom",
            content_cls=content,
            buttons=[
                MDFlatButton(text="Cancel", on_release=lambda x: self.task_dialog.dismiss())
            ]
        )
        self.task_dialog.open()
    
    def load_from_database(self, instance):
        """Load task data from Access database table 'Task Allocation'"""
        try:
            # Close the task dialog if it's open
            if hasattr(self, 'task_dialog') and self.task_dialog:
                self.task_dialog.dismiss()
            
            # Connect to Access database
            conn_str = (
                r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
                fr'DBQ={access_db_path};'
            )
            conn = pyodbc.connect(conn_str)
            cursor = conn.cursor()
            
            # Query the Task Allocation table
            cursor.execute("SELECT * FROM [Task Allocation]")
            columns = [column[0] for column in cursor.description]
            task_data = [dict(zip(columns, row)) for row in cursor.fetchall()]
            conn.close()
            
            if task_data:
                self.task_data = task_data
                
                # Get unique User IDs
                user_ids = list(set(row.get('User ID', '') for row in task_data if row.get('User ID')))
                
                if user_ids:
                    self.show_user_selection_dialog(user_ids)
                else:
                    dialog = MDDialog(title="Error", text="No 'User ID' column found in database table.")
                    dialog.open()
            else:
                dialog = MDDialog(title="Error", text="No data found in the Task Allocation table.")
                dialog.open()
                
        except Exception as e:
            dialog = MDDialog(title="Database Error", text=f"Failed to load data from database.\n{str(e)}")
            dialog.open()
    
    def load_from_excel(self, instance):
        """Load task data from Excel file"""
        # Close the task dialog if it's open
        if hasattr(self, 'task_dialog') and self.task_dialog:
            self.task_dialog.dismiss()
            
        try:
            file_path = filedialog.askopenfilename(
                title="Select Task Excel File",
                filetypes=[("Excel files", "*.xlsx *.xls")]
            )
            if file_path:
                # Read Excel file
                df = pd.read_excel(file_path, engine='openpyxl')
                self.task_data = df.to_dict('records')
                
                # Get unique User IDs
                user_ids = df['User ID'].unique().tolist() if 'User ID' in df.columns else []
                
                if user_ids:
                    self.show_user_selection_dialog(user_ids)
                else:
                    dialog = MDDialog(title="Error", text="No 'User ID' column found in Excel file.")
                    dialog.open()
        except Exception as e:
            dialog = MDDialog(title="Error", text=f"Failed to load Excel file.\n{str(e)}")
            dialog.open()
    
    def show_user_selection_dialog(self, user_ids):
        """Show dialog with User ID dropdown"""
        from kivymd.uix.menu import MDDropdownMenu
        from kivymd.uix.button import MDRaisedButton
        
        # Create dialog content
        content = MDBoxLayout(orientation="vertical", spacing=dp(20), size_hint_y=None, height=dp(100))
        
        # User ID dropdown button
        self.user_dropdown = MDRaisedButton(
            text="Select User ID",
            size_hint_x=1,
            on_release=lambda x: self.open_user_menu(user_ids)
        )
        content.add_widget(self.user_dropdown)
        
        # Create dialog
        self.task_dialog = MDDialog(
            title="Select User ID",
            type="custom",
            content_cls=content,
            buttons=[
                MDFlatButton(text="Cancel", on_release=lambda x: self.task_dialog.dismiss()),
                MDRaisedButton(text="Show Data", on_release=self.show_user_data)
            ]
        )
        self.task_dialog.open()
    
    def open_user_menu(self, user_ids):
        """Open dropdown menu for User ID selection"""
        menu_items = [
            {"text": str(user_id), "viewclass": "OneLineListItem", 
             "on_release": lambda x=user_id: self.select_user_id(x)}
            for user_id in user_ids
        ]
        self.user_menu = MDDropdownMenu(caller=self.user_dropdown, items=menu_items)
        self.user_menu.open()
    
    def select_user_id(self, user_id):
        """Select User ID and update button text"""
        self.selected_user_id = user_id
        self.user_dropdown.text = f"User ID: {user_id}"
        if hasattr(self, 'user_menu') and self.user_menu:
            self.user_menu.dismiss()
    
    def show_user_data(self, instance):
        """Group data by Series GTI and populate first group"""
        if not hasattr(self, 'selected_user_id'):
            dialog = MDDialog(title="Error", text="Please select a User ID first.")
            dialog.open()
            return
        
        # Filter data for selected user - handle both string and non-string user IDs
        user_data = []
        for row in self.task_data:
            row_user_id = row.get('User ID')
            # Convert both to strings for comparison to handle database numeric IDs
            if str(row_user_id).strip() == str(self.selected_user_id).strip():
                user_data.append(row)
        
        if user_data:
            if hasattr(self, 'task_dialog') and self.task_dialog:
                self.task_dialog.dismiss()
            self.group_data_by_gti(user_data)
            if self.grouped_gti_data:
                self.current_gti_index = 0
                self.populate_current_gti_data()
                dialog = MDDialog(title="Success", text=f"Loaded {len(self.grouped_gti_data)} GTI groups for User ID: {self.selected_user_id}")
                dialog.open()
            else:
                dialog = MDDialog(title="Error", text="No valid GTI data found.")
                dialog.open()
        else:
            dialog = MDDialog(title="Error", text="No data found for selected User ID.")
            dialog.open()
    
    def group_data_by_gti(self, user_data):
        """Group data by Series GTI and store raw data for Line of business filtering"""
        gti_groups = {}
        gti_raw_data = {}
        
        for row in user_data:
            # Handle both pandas DataFrame rows and database dictionary rows
            def get_value(field):
                value = row.get(field, '')
                # Handle both pandas NA values and None values from database
                if value is None or (hasattr(pd, 'isna') and pd.isna(value)):
                    return ''
                return str(value).strip()
            
            gti = get_value('Series GTI')
            if gti:
                if gti not in gti_groups:
                    gti_groups[gti] = {
                        'AHT': get_value('AHT'),
                        'Series GTI': gti,
                        'Series Name': get_value('Series Name'),
                        'Line of business': [],
                        'EDP Link': get_value('EDP Link'),
                        'Code Expansion': get_value('Code Expansion'),
                        'Parity Check': get_value('Parity Check'),
                        'POM Alias': get_value('POM Alias'),
                        'Issue Detail': get_value('Issue Detail'),
                        'Action': get_value('Action'),
                        'Resolver Group': get_value('Resolver Group'),
                        'Additional Comments': get_value('Additional Comments')
                    }
                    gti_raw_data[gti] = []
                
                # Store raw row data for Line of business filtering
                gti_raw_data[gti].append(row)
                
                # Collect unique Line of business values
                lob_value = get_value('Line of business')
                if lob_value and lob_value not in gti_groups[gti]['Line of business']:
                    gti_groups[gti]['Line of business'].append(lob_value)
        
        # Store processed data
        self.grouped_gti_data = []
        self.current_gti_raw_data = []
        for gti, data in gti_groups.items():
            self.grouped_gti_data.append(data)
            self.current_gti_raw_data.append(gti_raw_data[gti])
    
    def populate_current_gti_data(self):
        """Populate task fields with current GTI group data"""
        if self.current_gti_index < len(self.grouped_gti_data):
            current_data = self.grouped_gti_data[self.current_gti_index]
            
            # Set start time for this GTI and reset time tracking
            self.start_time = datetime.datetime.now().time()
            self.end_time = None
            self.break_start_time = None
            self.break_end_time = None
            self.total_break_time = 0.0
            
            # Clear RCA selection for new title
            self.selected_rca_items = []
            if "RCA" in self.entries:
                self.entries["RCA"].text = ""
            
            # Helper function to safely get values
            def get_safe_value(field):
                value = current_data.get(field, '')
                if value is None or (hasattr(pd, 'isna') and pd.isna(value)):
                    return ''
                return str(value).strip()
            
            # Populate basic fields
            basic_fields = ["AHT", "Series GTI", "Series Name", "EDP Link", 
                          "Code Expansion", "Parity Check", "POM Alias", "Issue Detail", 
                          "Action", "Resolver Group", "Additional Comments"]
            
            for field in basic_fields:
                value = get_safe_value(field)
                if field == "EDP Link":
                    if value and value.startswith(('http://', 'https://')):
                        self.edp_link_url = value
                        # Show truncated URL in button for better display
                        display_text = self.edp_link_url[:50] + "..." if len(self.edp_link_url) > 50 else self.edp_link_url
                        self.task_entries[field].text = display_text
                    else:
                        self.edp_link_url = ""
                        self.task_entries[field].text = "Link"
                else:
                    self.task_entries[field].text = value
                    # Trigger height adjustment for Season/Episode field
                    if field == "Season/Episode" and value:
                        self.on_season_episode_text_change(self.task_entries[field], value)
            
            # Reset dropdown buttons to original text
            self.task_entries["Line of business"].text = "Select Line of Business"
            if "Code Expansion" in self.task_entries:
                self.task_entries["Code Expansion"].text = "Select Code Expansion"
            if "Action" in self.task_entries:
                self.task_entries["Action"].text = "Select Action"
            if "Resolver Group" in self.task_entries:
                self.task_entries["Resolver Group"].text = "Select Resolver Group"
            
            # Clear dependent fields
            dependent_fields = ["Territory (T)", "Partner (P)", "Impressions", "Season/Episode", "Input Date", 
                              "Product Episode Missing", "Product Season Missing", "Offer Episode Missing", 
                              "Offer Season Missing", "Last Episode Missing", "Wrong Content Playing"]
            for field in dependent_fields:
                self.task_entries[field].text = ""
    
    def open_lob_menu(self, instance):
        """Open Line of business dropdown menu"""
        if self.current_gti_index < len(self.grouped_gti_data):
            current_data = self.grouped_gti_data[self.current_gti_index]
            lob_options = current_data.get('Line of business', [])
            
            if lob_options:
                menu_items = [
                    {"text": str(lob), "viewclass": "OneLineListItem", 
                     "on_release": lambda x=lob: self.select_line_of_business(x)}
                    for lob in lob_options
                ]
                try:
                    self.lob_menu = MDDropdownMenu(caller=instance, items=menu_items)
                    self.lob_menu.open()
                except Exception as e:
                    print(f"Menu error: {e}")
    
    def select_line_of_business(self, selected_lob):
        """Select Line of business and populate dependent fields with grouped data"""
        self.task_entries["Line of business"].text = selected_lob
        self.lob_menu.dismiss()
        
        # Find all matching rows for selected Line of business
        if self.current_gti_index < len(self.current_gti_raw_data):
            raw_data = self.current_gti_raw_data[self.current_gti_index]
            matching_rows = [row for row in raw_data if str(row.get('Line of business', '')).strip() == selected_lob]
            
            if matching_rows:
                # Helper function to safely get values from rows
                def get_safe_value(row, field):
                    value = row.get(field, '')
                    if value is None or (hasattr(pd, 'isna') and pd.isna(value)):
                        return ''
                    return str(value).strip()
                
                # Group and join data with commas for specific fields
                excel_to_ui_mapping = {
                    "Territory": "Territory (T)",
                    "Partner": "Partner (P)",
                    "Impressions": "Impressions",  # Ensure Impressions is properly mapped
                    "Season/Episode": "Season/Episode",
                    "Input Date": "Input Date"
                }
                grouped_data = {}
                
                for excel_field, ui_field in excel_to_ui_mapping.items():
                    if excel_field == "Season/Episode":
                        # Process data to group by issue content and combine territories
                        issue_to_territories = {}

                        for row in matching_rows:
                            se_value = get_safe_value(row, 'Season/Episode')
                            terr_value = get_safe_value(row, 'Territory')

                            if not se_value or not terr_value:
                                continue

                            # Get territories for this row
                            territories = [t.strip() for t in terr_value.split(',') if t.strip()]
                            if not territories:
                                continue

                            # Split the Season/Episode data by semicolons first
                            se_entries = [e.strip() for e in se_value.split(';') if e.strip()]

                            # Process each semicolon-separated entry
                            for entry in se_entries:
                                # Extract territory if present at the end of the entry
                                import re
                                territory_pattern = r'\([A-Z]{2}(?:,[A-Z]{2})*\)$'
                                territory_match = re.search(territory_pattern, entry)

                                if territory_match:
                                    # Remove territory part to get the issue content
                                    issue_content = entry[:territory_match.start()].strip()
                                    # Get territories from the entry
                                    entry_territories = [t.strip() for t in territory_match.group(0)[1:-1].split(',')]
                                else:
                                    # No territory in the entry, use the issue content as is
                                    issue_content = entry
                                    entry_territories = territories

                                # Group by issue content
                                if issue_content not in issue_to_territories:
                                    issue_to_territories[issue_content] = set()

                                # Add all territories for this issue
                                for territory in entry_territories:
                                    issue_to_territories[issue_content].add(territory)
                        
                        # Create formatted entries with combined territories
                        all_formatted_entries = []
                        for issue_content, territories in sorted(issue_to_territories.items()):
                            # Convert territories set to sorted list and combine them
                            territory_list = sorted(list(territories))
                            combined_territories = ",".join(territory_list)

                            # Create the formatted entry with combined territories
                            formatted_entry = f"{issue_content}({combined_territories})"
                            all_formatted_entries.append(formatted_entry)

                        grouped_data[ui_field] = '; '.join(all_formatted_entries)
                    elif excel_field == "Impressions":
                        # Special handling for Impressions - preserve all values
                        all_values = []
                        for row in matching_rows:
                            value = get_safe_value(row, excel_field)
                            if value:
                                all_values.append(value)
                        grouped_data[ui_field] = ', '.join(all_values)
                    else:
                        unique_values = []
                        for row in matching_rows:
                            value = get_safe_value(row, excel_field)
                            if value and value not in unique_values:
                                unique_values.append(value)
                        grouped_data[ui_field] = ', '.join(unique_values)
                
                # For other fields, group unique values
                other_fields = ["Product Episode Missing", "Product Season Missing", "Offer Episode Missing", 
                               "Offer Season Missing", "Last Episode Missing", "Wrong Content Playing"]
                non_groupable_fields = {}
                
                for field in other_fields:
                    unique_values = []
                    for row in matching_rows:
                        value = get_safe_value(row, field)
                        if value and value not in unique_values:
                            unique_values.append(value)
                    non_groupable_fields[field] = ', '.join(unique_values)
                
                # Populate all fields
                all_fields = {**grouped_data, **non_groupable_fields}
                for field, value in all_fields.items():
                    self.task_entries[field].text = value
                    # Trigger height adjustment for Season/Episode field
                    if field == "Season/Episode" and value:
                        self.on_season_episode_text_change(self.task_entries[field], value)
    
    def get_next_task(self, instance):
        """Get next task button handler - moves to the next task when clicked"""
        # Check if we have a GTI entered and should try to restore auto-saved data
        current_gti = self.task_entries.get("Series GTI", MDTextField()).text.strip()
        
        # If we have auto-saved data and a GTI is entered, check if it matches
        if hasattr(self, '_autosave_data') and current_gti:
            saved_gti = self._autosave_data.get('series_gti', '').strip()
            if saved_gti and saved_gti == current_gti:
                # Ask user if they want to restore the data
                def confirm_restore(instance):
                    restore_dialog.dismiss()
                    if self.restore_autosaved_data():
                        dialog = MDDialog(title="Data Restored", text="Auto-saved data has been restored successfully.")
                        dialog.open()
                
                def skip_restore(instance):
                    restore_dialog.dismiss()
                    # Delete auto-save file
                    if os.path.exists(self.autosave_path):
                        os.remove(self.autosave_path)
                    # Continue with normal task loading
                    self.move_to_next_gti()
                
                restore_dialog = MDDialog(
                    title="Restore Data",
                    text=f"Found auto-saved data for GTI: {saved_gti}. Would you like to restore it?",
                    buttons=[
                        MDFlatButton(text="No", on_release=skip_restore),
                        MDRaisedButton(text="Yes", on_release=confirm_restore)
                    ]
                )
                restore_dialog.open()
                return
        
        # If no grouped data is available, check if we need to load from database
        if not hasattr(self, 'grouped_gti_data') or not self.grouped_gti_data:
            # Ask if user wants to load data from database
            def confirm_load_db(instance):
                load_dialog.dismiss()
                self.load_from_database(None)
            
            def cancel_load(instance):
                load_dialog.dismiss()
            
            load_dialog = MDDialog(
                title="No Task Data",
                text="No task data is loaded. Would you like to load data from the database?",
                buttons=[
                    MDFlatButton(text="Cancel", on_release=cancel_load),
                    MDRaisedButton(text="Load from Database", on_release=confirm_load_db)
                ]
            )
            load_dialog.open()
            return
        
        # If no auto-saved data or GTI doesn't match, proceed normally
        self.move_to_next_gti()
    
    def move_to_next_gti(self):
        """Move to next GTI group and populate data"""
        if hasattr(self, 'grouped_gti_data') and self.grouped_gti_data:
            # Clear sections before moving to next title
            self.clear_all_sections()
            
            # Keep the cumulative total time and titles processed displayed
            self.time_display.text = f"Total Time: {self.cumulative_time:.2f} min"
            self.titles_display.text = f"Titles Processed: {self.titles_processed}"
            
            self.current_gti_index += 1
            if self.current_gti_index < len(self.grouped_gti_data):
                self.populate_current_gti_data()
                current_gti = self.grouped_gti_data[self.current_gti_index].get('Series GTI', '')
                dialog = MDDialog(title="Next Title", text=f"Moved to next title: {current_gti} ({self.current_gti_index + 1}/{len(self.grouped_gti_data)})")
                dialog.open()
            else:
                dialog = MDDialog(title="Completed", text="All titles have been processed!")
                dialog.open()
                # Reset for next batch
                self.current_gti_index = 0
                self.grouped_gti_data = []
    
    def open_code_expansion_menu(self, instance):
        """Open Code Expansion dropdown menu"""
        code_options = [
            "FP - False Positive", "SM - Season missing", "SM-P - Season missing Partner intent",
            "SMSS - Season Missing Special Season", "SMSP - Season missing Sub Part",
            "EMOM - Episode missing offer missing", "EMOM-A - Episodes missing offer missing - Avails missing",
            "EMOM-R - Episodes missing offer missing - Rights missing", "EMOM-1C - Episodes missing offer missing - 1C issue",
            "EMOM-D - Episodes missing offer missing - Duplicates", "EMOM-T - Episodes missing offer missing - Takedown",
            "EMOM-SI - Episodes missing offer missing - Sequence incorrect", "EMOM-CF - Episodes missing offer missing - Conversion Failure",
            "EMOM-P - Episodes missing offer missing - Partner Intent", "EMOM-NP - Episodes missing offer missing - Not in Parity",
            "EMOM-AS - Episodes missing offer missing - Asset missing", "EMOE - Episode missing offer expired",
            "EMOE-A - Episodes missing offer expired - Avails missing", "EMOR-R - Episode missing offer expired - Rights missing",
            "EMOE-1C - Episode missing offer expired - 1C issue", "EMOE-D - Episode missing offer expired - Duplicates",
            "EMOE-T - Episode missing offer expired - Takedown", "EMOE-SI - Episode missing offer expired - Sequence incorrect",
            "EMOE-CF - Episode missing offer expired - Conversion Failure", "EMOE-P - Episode missing offer expired - Partner Intent",
            "EMOE-NP - Episode missing offer expired - Not in Parity", "EMOF - Episode missing offer in future",
            "EMVAM - Episode missing Value added material", "EMVAM-S - Episode missing Value added material - Specials",
            "EMVAM-T - Episode missing Value added material - Trailer", "EMOM-SI C - Episode missing offer missing - Sequence incorrect - Combo episodes",
            "EMOE-SA - Episode missing offer expired - Sequence Agnostic", "EMOS - Episode missing on site",
            "EMC - Episode Missing Combo Episodes", "WCP-DV - Wrong content playing - Duplicate Video",
            "WCP - Wrong content playing"
        ]
        
        menu_items = [
            {"text": code, "viewclass": "OneLineListItem", 
             "on_release": lambda x=code: self.select_code_expansion(x)}
            for code in code_options
        ]
        self.code_expansion_menu = MDDropdownMenu(caller=instance, items=menu_items)
        self.code_expansion_menu.open()
    
    def select_code_expansion(self, selected_code):
        """Select Code Expansion option"""
        self.task_entries["Code Expansion"].text = selected_code
        self.code_expansion_menu.dismiss()
    
    def open_action_menu(self, instance):
        """Open Action dropdown menu"""
        action_options = [
            "Internal Correction", "No Action Required", "Partner Intent", "Redelivery",
            "Tech solve", "Merge request", "PVD Title", "Offers issue", "POM Confirmation"
        ]
        
        menu_items = [
            {"text": action, "viewclass": "OneLineListItem", 
             "on_release": lambda x=action: self.select_action(x)}
            for action in action_options
        ]
        self.action_menu = MDDropdownMenu(caller=instance, items=menu_items)
        self.action_menu.open()
    
    def select_action(self, selected_action):
        """Select Action option"""
        self.task_entries["Action"].text = selected_action
        self.action_menu.dismiss()
    
    def open_resolver_menu(self, instance):
        """Open Resolver Group dropdown menu"""
        resolver_options = [
            "Redelivery", "POM", "PVD", "PVD offer Creation", "Merge Team", 
            "Tech team", "Offers Team", "NA"
        ]
        
        menu_items = [
            {"text": resolver, "viewclass": "OneLineListItem", 
             "on_release": lambda x=resolver: self.select_resolver(x)}
            for resolver in resolver_options
        ]
        self.resolver_menu = MDDropdownMenu(caller=instance, items=menu_items)
        self.resolver_menu.open()
    
    def select_resolver(self, selected_resolver):
        """Select Resolver Group option"""
        self.task_entries["Resolver Group"].text = selected_resolver
        self.resolver_menu.dismiss()
    
    def set_break_mode(self, instance):
        """Set break mode - track break start time"""
        # Only set break start time if not already in break mode
        if not self.break_start_time:
            self.break_start_time = datetime.datetime.now().time()
            # We don't save break start time to database anymore
            dialog = MDDialog(title="Break Mode", text="Break mode activated. Timer paused.")
            dialog.open()
    
    def set_active_mode(self, instance):
        """Set active mode - track break end time and calculate break duration"""
        if self.break_start_time:
            self.break_end_time = datetime.datetime.now().time()
            
            # Calculate break duration
            today = datetime.date.today()
            break_start_dt = datetime.datetime.combine(today, self.break_start_time)
            break_end_dt = datetime.datetime.combine(today, self.break_end_time)
            
            # Handle case where break spans midnight
            if break_end_dt < break_start_dt:
                break_end_dt = break_end_dt + datetime.timedelta(days=1)
                
            break_duration = (break_end_dt - break_start_dt).total_seconds() / 60
            self.total_break_time += break_duration
            
            # Store only the break duration for database (not start/end times)
            if not hasattr(self, 'break_duration_db'):
                self.break_duration_db = round(break_duration, 2)  # Round to 2 decimal places
            else:
                self.break_duration_db = round(self.break_duration_db + break_duration, 2)  # Keep as float until database insertion
            
            # Reset break times for next break
            self.break_start_time = None
            self.break_end_time = None
            
            dialog = MDDialog(title="Active Mode", text=f"Active mode activated. Break time: {break_duration:.2f} minutes.")
            dialog.open()
        else:
            dialog = MDDialog(title="Active Mode", text="Active mode activated. Timer running.")
            dialog.open()
    
    def open_edp_link(self, instance):
        """Open EDP link in browser using Series GTI"""
        series_gti = self.task_entries.get("Series GTI", MDTextField()).text.strip()
        if series_gti:
            edp_url = f"https://atv-optic-domain-tooling-prod-iad.iad.proxy.amazon.com/entity-detail/{series_gti}"
            import webbrowser
            webbrowser.open(edp_url)
        else:
            dialog = MDDialog(title="Info", text="No Series GTI available to generate EDP link.")
            dialog.open()
    
    def submit_task(self, instance):
        """Show preview popup before submitting task data"""
        # Collect all forms data
        all_forms_data = []

        # Main form data
        main_form_data = {field: self.entries.get(field, MDTextField()).text if hasattr(self.entries.get(field, MDTextField()), 'text') else "" for field in fields}
        if hasattr(self, 'selected_offers') and self.selected_offers:
            main_form_data["Offer Type"] = ", ".join(sorted(self.selected_offers))
        all_forms_data.append(main_form_data)

        # Additional forms data
        if self.additional_forms:
            for form_entries in self.additional_forms:
                additional_form_data = {}
                for field in fields:
                    if field == "Offer Type":
                        form_id = id(form_entries)
                        if hasattr(self, 'selected_offers_forms') and form_id in self.selected_offers_forms and self.selected_offers_forms[form_id]:
                            additional_form_data[field] = ", ".join(sorted(self.selected_offers_forms[form_id]))
                        else:
                            additional_form_data[field] = ""
                    elif field == "RCA":
                        # Get RCA from form's internal storage
                        rca_items = form_entries.get('_rca_items', [])
                        print(f"Form RCA items: {rca_items}")
                        if rca_items:
                            additional_form_data[field] = "\n\n".join([f"Issue {i+1}: {item}" for i, item in enumerate(rca_items)])
                        else:
                            # Fallback to text field if no internal storage
                            entry = form_entries.get(field, MDTextField())
                            additional_form_data[field] = entry.text if hasattr(entry, 'text') else ""
                    elif field == "Issue":
                        # Get Issue from form's internal storage
                        issue_items = form_entries.get('_issue_items', [])
                        print(f"Form Issue items: {issue_items}")
                        if len(issue_items) == 1:
                            additional_form_data[field] = issue_items[0]
                        elif len(issue_items) == 2:
                            additional_form_data[field] = f"{issue_items[0]} due to {issue_items[1]}"
                        else:
                            # Fallback to text field if no internal storage
                            entry = form_entries.get(field, MDTextField())
                            additional_form_data[field] = entry.text if hasattr(entry, 'text') else ""
                    else:
                        entry = form_entries.get(field, MDTextField())
                        additional_form_data[field] = entry.text if hasattr(entry, 'text') else ""
                all_forms_data.append(additional_form_data)

        # Show preview popup
        self.show_preview_popup(all_forms_data)

    def show_preview_popup(self, all_forms_data):
        """Show preview popup with pagination for multiple forms"""
        from kivymd.uix.dialog import MDDialog
        from kivymd.uix.boxlayout import MDBoxLayout
        from kivymd.uix.label import MDLabel
        from kivymd.uix.button import MDFlatButton, MDRaisedButton
        from kivymd.uix.scrollview import MDScrollView
        from kivy.metrics import dp

        # Store forms data for confirmation
        self.preview_forms_data = all_forms_data
        self.current_preview_page = 0

        # Create main content layout
        main_content = MDBoxLayout(orientation="vertical", spacing=dp(10), size_hint_y=None)
        main_content.bind(minimum_height=main_content.setter('height'))

        # Add title
        title_label = MDLabel(
            text="Preview - Form Data",
            font_style="H6",
            size_hint_y=None,
            height=dp(40),
            bold=True
        )
        main_content.add_widget(title_label)

        # Page indicator
        self.page_indicator = MDLabel(
            text=f"Page {self.current_preview_page + 1} of {len(all_forms_data)}",
            font_style="Subtitle1",
            size_hint_y=None,
            height=dp(30),
            halign="center"
        )
        main_content.add_widget(self.page_indicator)

        # Scrollable content area for form data
        self.preview_scroll = MDScrollView(size_hint=(1, None), height=dp(400))
        self.preview_content = MDBoxLayout(orientation="vertical", spacing=dp(5), size_hint_y=None)
        self.preview_content.bind(minimum_height=self.preview_content.setter('height'))

        # Display current form
        self.update_preview_content()

        self.preview_scroll.add_widget(self.preview_content)
        main_content.add_widget(self.preview_scroll)

        # Navigation buttons (only show if multiple forms)
        if len(all_forms_data) > 1:
            nav_layout = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(50), spacing=dp(10))

            self.prev_btn = MDFlatButton(
                text="Previous",
                size_hint_x=0.5,
                on_release=self.preview_previous_page
            )
            self.next_btn = MDFlatButton(
                text="Next",
                size_hint_x=0.5,
                on_release=self.preview_next_page
            )

            nav_layout.add_widget(self.prev_btn)
            nav_layout.add_widget(self.next_btn)
            main_content.add_widget(nav_layout)

            # Update navigation button states
            self.update_navigation_buttons()

        # Create and show the dialog
        self.preview_dialog = MDDialog(
            title="Preview",
            type="custom",
            content_cls=main_content,
            buttons=[
                MDFlatButton(text="Cancel", on_release=self.cancel_preview),
                MDRaisedButton(text="Confirm", on_release=self.confirm_preview)
            ],
            size_hint=(0.9, None),
            height=dp(600)
        )
        self.preview_dialog.open()

    def update_preview_content(self):
        """Update the preview content with current page data"""
        # Clear existing content
        self.preview_content.clear_widgets()

        if not self.preview_forms_data or self.current_preview_page >= len(self.preview_forms_data):
            return

        # Get current form data
        current_form = self.preview_forms_data[self.current_preview_page]

        # Add form type indicator
        form_type = "Main Form" if self.current_preview_page == 0 else f"Additional Form {self.current_preview_page}"
        type_label = MDLabel(
            text=form_type,
            font_style="Subtitle1",
            size_hint_y=None,
            height=dp(35),
            bold=True
        )
        self.preview_content.add_widget(type_label)

        # Display main form fields
        self.add_section_to_preview("Main Form Fields", fields, current_form)

        # Add task section data (only for main form or if task data exists)
        if self.current_preview_page == 0:
            task_data = {}
            for field, entry in self.task_entries.items():
                if hasattr(entry, 'text'):
                    task_data[field] = entry.text
                else:
                    task_data[field] = ""

            # Only show task section if there's any data
            if any(value.strip() for value in task_data.values()):
                self.add_section_to_preview("Task Section", list(task_data.keys()), task_data)

    def add_section_to_preview(self, section_title, field_list, data_dict):
        """Add a section with fields to the preview content"""
        # Add section header
        section_header = MDLabel(
            text=section_title,
            font_style="Subtitle1",
            size_hint_y=None,
            height=dp(35),
            bold=True
        )
        self.preview_content.add_widget(section_header)

        # Display section fields
        for field in field_list:
            field_value = data_dict.get(field, "")

            # Skip empty fields for cleaner display
            if not field_value or (isinstance(field_value, str) and not field_value.strip()):
                continue

            # Create field layout
            field_layout = MDBoxLayout(orientation="horizontal", size_hint_y=None, spacing=dp(10))

            # Field label
            field_label = MDLabel(
                text=f"{field}:",
                size_hint_x=0.3,
                font_style="Body1",
                bold=True
            )

            # Field value with text wrapping
            value_text = str(field_value) if field_value else "(empty)"
            value_label = MDLabel(
                text=value_text,
                size_hint_x=0.7,
                text_size=(None, None),
                halign="left",
                valign="top"
            )
            # Enable text wrapping
            value_label.bind(size=lambda instance, size: setattr(instance, 'text_size', (size[0], None)))

            # Calculate height based on content
            estimated_lines = max(1, len(value_text) // 50 + 1)
            if '\n' in value_text:
                estimated_lines = max(estimated_lines, value_text.count('\n') + 1)

            field_height = max(dp(30), dp(20 * estimated_lines))
            field_layout.height = field_height

            field_layout.add_widget(field_label)
            field_layout.add_widget(value_label)
            self.preview_content.add_widget(field_layout)

    def update_navigation_buttons(self):
        """Update navigation button states"""
        if hasattr(self, 'prev_btn'):
            self.prev_btn.disabled = (self.current_preview_page == 0)
        if hasattr(self, 'next_btn'):
            self.next_btn.disabled = (self.current_preview_page >= len(self.preview_forms_data) - 1)

    def preview_previous_page(self, instance):
        """Go to previous page in preview"""
        if self.current_preview_page > 0:
            self.current_preview_page -= 1
            self.page_indicator.text = f"Page {self.current_preview_page + 1} of {len(self.preview_forms_data)}"
            self.update_preview_content()
            self.update_navigation_buttons()

    def preview_next_page(self, instance):
        """Go to next page in preview"""
        if self.current_preview_page < len(self.preview_forms_data) - 1:
            self.current_preview_page += 1
            self.page_indicator.text = f"Page {self.current_preview_page + 1} of {len(self.preview_forms_data)}"
            self.update_preview_content()
            self.update_navigation_buttons()

    def cancel_preview(self, instance):
        """Cancel preview and close dialog"""
        self.preview_dialog.dismiss()
        # Clean up preview data
        if hasattr(self, 'preview_forms_data'):
            delattr(self, 'preview_forms_data')
        if hasattr(self, 'current_preview_page'):
            delattr(self, 'current_preview_page')

    def confirm_preview(self, instance):
        """Confirm and submit all forms"""
        self.preview_dialog.dismiss()

        # Use the original submission logic
        all_forms_data = self.preview_forms_data

        # Submit each form as separate database entries
        for i, form_data in enumerate(all_forms_data):
            # Set flag to prevent auto-move to next GTI except for last form
            self._prevent_auto_move = i < len(all_forms_data) - 1

            # Set current form data for Issue Name handling
            if i == 0:
                self.current_form_data = None  # Main form
                print(f"Submitting main form data: {form_data}")
            else:
                self.current_form_data = self.additional_forms[i-1]  # Additional form
                print(f"Submitting additional form {i} data: {form_data}")

            # Submit each form data separately to create new database rows
            self.submit_data(form_data)

        # Show confirmation for multiple forms
        if len(all_forms_data) > 1:
            dialog = MDDialog(title="Multiple Forms Submitted", text=f"Submitted {len(all_forms_data)} forms for current title.")
            dialog.open()

        # Update titles processed counter
        self.titles_processed += 1
        self.titles_display.text = f"Titles Processed: {self.titles_processed}"

        # Update cumulative time
        if self.start_time and self.end_time:
            today = datetime.date.today()
            start_datetime = datetime.datetime.combine(today, self.start_time)
            end_datetime = datetime.datetime.combine(today, self.end_time)

            # Handle case where task spans midnight
            if end_datetime < start_datetime:
                end_datetime = end_datetime + datetime.timedelta(days=1)

            gross_time = (end_datetime - start_datetime).total_seconds() / 60
            net_time = gross_time - self.total_break_time
            if net_time < 0:
                net_time = 0

            self.cumulative_time += net_time
            self.time_display.text = f"Total Time: {self.cumulative_time:.2f} min"

        # Clean up preview data
        if hasattr(self, 'preview_forms_data'):
            delattr(self, 'preview_forms_data')
        if hasattr(self, 'current_preview_page'):
            delattr(self, 'current_preview_page')

    def clear_all_sections(self):
        """Clear all sections after submission"""
        # Clear main form entries
        for field, entry in self.entries.items():
            if hasattr(entry, 'text'):
                if field == "Offer Type":
                    entry.text = "Select Offer Type"
                else:
                    entry.text = ""
        
        # Clear task entries
        for field, entry in self.task_entries.items():
            if hasattr(entry, 'text'):
                if field == "Line of business":
                    entry.text = "Select Line of Business"
                elif field == "Code Expansion":
                    entry.text = "Select Code Expansion"
                elif field == "Action":
                    entry.text = "Select Action"
                elif field == "Resolver Group":
                    entry.text = "Select Resolver Group"
                elif field == "EDP Link":
                    entry.text = "Link"
                else:
                    entry.text = ""
        
        # Clear additional forms
        for form_entries in self.additional_forms:
            for field, entry in form_entries.items():
                if field.startswith('_'):
                    if field == '_rca_items':
                        form_entries[field] = []
                    elif field == '_issue_items':
                        form_entries[field] = []
                    elif field == '_issue_name':
                        form_entries[field] = ""
                elif hasattr(entry, 'text'):
                    if field == "Offer Type":
                        entry.text = "Select Offer Type"
                    else:
                        entry.text = ""
        
        # Reset all selections
        self.selected_rca_items = []
        self.selected_issues = []
        self.issue_name = ""
        if hasattr(self, 'selected_offers'):
            self.selected_offers = set()
        if hasattr(self, 'selected_offers_forms'):
            self.selected_offers_forms = {}
        
        # Reset CTI values and buttons
        self.cti_values = ["", "", ""]
        for btn in self.cti_buttons:
            btn.text = ["Digital Video", "Digiflex", "TV Series Integrity"][self.cti_buttons.index(btn)]
        
        # Reset active form to main
        self.active_form = 'main'
    
    def clear_form(self):
        """Clear all form fields"""
        self.clear_all_sections()
    
    def add_new_form(self, instance):
        """Add a new main form with all headers and entries"""
        self.form_counter += 1
        
        # Get the content layout from the scroll view
        scroll = self.root.children[0]  # Get scroll view
        content = scroll.children[0]  # Get content layout
        
        # Create new form card
        new_form_card = MDCard(padding=dp(20), spacing=dp(10), adaptive_height=True)
        new_form_layout = MDBoxLayout(orientation="vertical", spacing=dp(15), adaptive_height=True)
        
        # Create entries dictionary for this form first
        form_entries = {}
        
        # Add form title with selection button
        form_header = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(40))
        form_title = MDLabel(text=f"Form {self.form_counter}", theme_text_color="Primary", font_style="H6", size_hint_x=0.7)
        select_btn = MDRaisedButton(text="Select", size_hint_x=0.3, md_bg_color="blue", 
                                   on_release=lambda x, f=form_entries: self.select_form(f))
        form_header.add_widget(form_title)
        form_header.add_widget(select_btn)
        new_form_layout.add_widget(form_header)
        
        # Add all fields to new form
        for field in fields:
            field_layout = MDBoxLayout(orientation="horizontal", spacing=dp(10), 
                                     adaptive_height=True, size_hint_y=None)
            
            label = MDLabel(text=field, size_hint_x=0.3, theme_text_color="Primary",
                           font_style="Subtitle1")
            
            if field == "Offer Type":
                # Dropdown for Offer Type
                field_layout.add_widget(label)
                spacer_left = MDLabel(text="", size_hint_x=0.1)
                field_layout.add_widget(spacer_left)
                offer_field = MDRaisedButton(text="Select Offer Type", size_hint_x=0.3,
                                           on_release=lambda x, f=form_entries: self.open_offer_menu_for_form(x, f))
                form_entries[field] = offer_field
                field_layout.add_widget(offer_field)
                spacer_right = MDLabel(text="", size_hint_x=0.3)
                field_layout.add_widget(spacer_right)
            else:
                # Text fields
                field_layout.add_widget(label)
                text_field = MDTextField(text="", multiline=True, size_hint_x=0.7,
                                       max_text_length=2000, font_name='UnicodeFont')
                text_field.height = dp(120)
                text_field.bind(text=self.on_text_change)
                form_entries[field] = text_field
                field_layout.add_widget(text_field)
            
            field_layout.height = dp(120)
            new_form_layout.add_widget(field_layout)
        
        # Initialize form-specific data
        form_entries['_rca_items'] = []
        form_entries['_issue_items'] = []
        form_entries['_issue_name'] = ""
        
        # Store form entries
        self.additional_forms.append(form_entries)
        
        new_form_card.add_widget(new_form_layout)
        
        # Insert new form before the RCA section (which is at index -4)
        content.add_widget(new_form_card, index=len(content.children) - 4)
        
        dialog = MDDialog(title="Form Added", text=f"Form {self.form_counter} has been added successfully.")
        dialog.open()
    
    def open_offer_menu_for_form(self, instance, form_entries):
        """Open offer menu for specific form"""
        if not hasattr(self, 'selected_offers_forms'):
            self.selected_offers_forms = {}
        
        form_id = id(form_entries)
        if form_id not in self.selected_offers_forms:
            self.selected_offers_forms[form_id] = set()
        
        menu_items = [
            {"text": "Prime", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type_for_form("Prime", instance, form_entries)},
            {"text": "TVOD", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type_for_form("TVOD", instance, form_entries)},
            {"text": "Channels", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type_for_form("Channels", instance, form_entries)},
            {"text": "FVOD", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type_for_form("FVOD", instance, form_entries)},
            {"text": "AVOD", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type_for_form("AVOD", instance, form_entries)},
            {"text": "POEST", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type_for_form("POEST", instance, form_entries)},
            {"text": "SVOD", "viewclass": "OneLineListItem", "on_release": lambda: self.toggle_offer_type_for_form("SVOD", instance, form_entries)},
        ]
        menu = MDDropdownMenu(caller=instance, items=menu_items)
        menu.open()
    
    def toggle_offer_type_for_form(self, value, button, form_entries):
        """Toggle offer type for specific form"""
        form_id = id(form_entries)
        if form_id not in self.selected_offers_forms:
            self.selected_offers_forms[form_id] = set()
        
        if value in self.selected_offers_forms[form_id]:
            self.selected_offers_forms[form_id].remove(value)
        else:
            self.selected_offers_forms[form_id].add(value)
        
        # Update button text
        if self.selected_offers_forms[form_id]:
            button.text = ", ".join(sorted(self.selected_offers_forms[form_id]))
        else:
            button.text = "Select Offer Type"
    
    def select_form(self, form_entries):
        """Select which form is currently active for RCA/Issue updates"""
        self.active_form = form_entries
        dialog = MDDialog(title="Form Selected", text="This form is now active for RCA and Issue Blurbs selection.")
        dialog.open()
    
    def remove_form(self, instance):
        """Remove the last additional form"""
        if self.additional_forms:
            # Remove from additional_forms list
            removed_form = self.additional_forms.pop()
            
            # Remove from UI
            scroll = self.root.children[0]  # Get scroll view
            content = scroll.children[0]  # Get content layout
            
            # Find and remove the last form card (excluding main sections)
            for i, child in enumerate(content.children):
                if hasattr(child, 'children') and len(child.children) > 0:
                    # Check if this is a form card by looking for form header
                    form_layout = child.children[0]
                    if hasattr(form_layout, 'children') and len(form_layout.children) > 0:
                        first_child = form_layout.children[-1]  # Last added child (first in display)
                        if hasattr(first_child, 'children') and len(first_child.children) >= 2:
                            # Check if it has a "Select" button (indicating it's an additional form)
                            for widget in first_child.children:
                                if hasattr(widget, 'text') and widget.text == "Select":
                                    content.remove_widget(child)
                                    self.form_counter -= 1
                                    dialog = MDDialog(title="Form Removed", text="Additional form has been removed successfully.")
                                    dialog.open()
                                    return
            
            dialog = MDDialog(title="No Forms", text="No additional forms to remove.")
            dialog.open()
        else:
            dialog = MDDialog(title="No Forms", text="No additional forms to remove.")
            dialog.open()
    
    def auto_save_data(self):
        """Auto-save current form data and state to prevent data loss"""
        try:
            # Get current form data
            form_data = {}
            for field in fields:
                if field == "Offer Type" and hasattr(self, 'selected_offers'):
                    form_data[field] = ", ".join(sorted(self.selected_offers)) if self.selected_offers else ""
                elif hasattr(self.entries.get(field, None), 'text'):
                    form_data[field] = self.entries[field].text
                else:
                    form_data[field] = ""
            
            # Get task data
            task_data = {}
            for field, entry in self.task_entries.items():
                if hasattr(entry, 'text'):
                    task_data[field] = entry.text
            
            # Save state data
            state_data = {
                "form_data": form_data,
                "task_data": task_data,
                "start_time": self.start_time.strftime("%H:%M:%S") if self.start_time else None,
                "series_gti": task_data.get("Series GTI", ""),
                "series_name": task_data.get("Series Name", ""),
                "selected_issues": self.selected_issues,
                "issue_name": self.issue_name,
                "selected_rca_items": self.selected_rca_items,
                "cti_values": self.cti_values,
                "cumulative_time": self.cumulative_time,
                "titles_processed": self.titles_processed
            }
            
            # Save to file
            with open(self.autosave_path, 'wb') as f:
                pickle.dump(state_data, f)
            
            print(f"Auto-saved data to {self.autosave_path}")
        except Exception as e:
            print(f"Auto-save error: {e}")
    
    def try_load_autosave(self):
        """Try to load auto-saved data if it exists"""
        try:
            if os.path.exists(self.autosave_path):
                with open(self.autosave_path, 'rb') as f:
                    state_data = pickle.load(f)
                
                # Store the data for later restoration
                self._autosave_data = state_data
                print(f"Found auto-saved data for GTI: {state_data.get('series_gti', '')}")
        except Exception as e:
            print(f"Error loading auto-save data: {e}")
    
    def parse_season_episode_data(self, text):
        """Parse Season/Episode data from text format to structured format"""
        import re
        
        # Define the headers we're looking for
        headers = [
            "Missing Episode - Offer", 
            "Missing Season - Offer", 
            "Missing Episode - Product", 
            "Missing Season - Product", 
            "Last episode missing"
        ]
        
        # Initialize result dictionary with lists for multiple values
        result = {header: [] for header in headers}
        
        # First split the text by semicolons to handle multiple entries
        entries = text.split(';')
        
        # Process each entry
        for entry in entries:
            entry = entry.strip()
            if not entry:
                continue
                
            # Check each header against this entry
            matched = False
            for header in headers:
                # Look for pattern like "Header: {value}" or "Header:{value}"
                pattern = f"({re.escape(header)}\s*:\s*(.+?))(\([^)]*\)|$)"
                match = re.search(pattern, entry, re.IGNORECASE)
                if match:
                    value = match.group(2).strip()
                    territory = match.group(3).strip()
                    
                    # Clean up the value - remove extra spaces, normalize brackets
                    value = re.sub(r'\s+', ' ', value)
                    
                    # Add territory to the value if present
                    if territory:
                        full_value = f"{value} {territory}"
                    else:
                        full_value = value
                        
                    result[header].append(full_value)
                    matched = True
                    break  # Found a match for this entry, move to next
            
            # If no header matched, check for complex patterns like "Last episode mising: {S10-(2,3)}(CO)"
            if not matched:
                # Try to match the pattern with header, value and territory
                complex_pattern = r"(.*?):\s*(\{[^}]+\})\s*(\([^)]+\))?"
                complex_match = re.search(complex_pattern, entry)
                if complex_match:
                    header_text = complex_match.group(1).strip()
                    value_text = complex_match.group(2).strip()
                    territory_text = complex_match.group(3) if complex_match.group(3) else ""
                    
                    # Find the closest matching header
                    closest_header = None
                    for h in headers:
                        if header_text.lower() in h.lower() or h.lower() in header_text.lower():
                            closest_header = h
                            break
                    
                    if closest_header:
                        full_value = f"{value_text} {territory_text}".strip()
                        result[closest_header].append(full_value)
        
        return result
    
    def format_season_episode_table(self, season_episode_data, territory_data):
        """Format Season/Episode data as a markdown table with specified headers"""
        import re
        
        # Define the table headers
        headers = [
            "Missing Episode - Offer", 
            "Missing Season - Offer", 
            "Missing Episode - Product", 
            "Missing Season - Product", 
            "Last episode missing", 
            "Territory"
        ]
        
        # Parse the season_episode_data
        parsed_data = self.parse_season_episode_data(season_episode_data)
        
        # Initialize data dictionary with empty values for each header
        data = {header: [] for header in headers}
        
        # Extract all territories from the parsed data
        all_territories = set()
        
        # Fill in the parsed data and extract territories
        for header in headers:
            if header == "Territory":
                continue  # Handle territory separately
            
            if header in parsed_data:
                for value in parsed_data[header]:
                    # Extract territory from value if present
                    territory_match = re.search(r'\(([^)]+)\)$', value)
                    if territory_match:
                        # Extract the value without territory
                        clean_value = value[:territory_match.start()].strip()
                        data[header].append(clean_value)
                        
                        # Add territories to the set
                        territories = territory_match.group(1).split(',')
                        for t in territories:
                            all_territories.add(t.strip())
                    else:
                        data[header].append(value)
        
        # Process territory data - split by commas and clean
        if territory_data:
            territories = [t.strip() for t in territory_data.split(',') if t.strip()]
            for t in territories:
                all_territories.add(t)
        
        # Add all unique territories to the data
        data["Territory"] = sorted(all_territories)
        
        # Determine the number of rows needed (max number of items in any column)
        max_rows = max(len(values) for values in data.values()) or 1
        
        # Create markdown table
        table_rows = []
        
        # Add header row
        table_rows.append("| " + " | ".join(headers) + " |")
        table_rows.append("| " + " | ".join(["---" for _ in headers]) + " |")
        
        # Add data rows
        for i in range(max_rows):
            row_data = []
            for header in headers:
                if i < len(data[header]):
                    row_data.append(data[header][i])
                else:
                    row_data.append("")
            table_rows.append("| " + " | ".join(row_data) + " |")
        
        return "\n".join(table_rows)
        
    def show_season_episode_dialog(self, season_episode_data, territory_data):
        """Show a dialog with Season/Episode data in a table format"""
        from kivymd.uix.dialog import MDDialog
        from kivymd.uix.boxlayout import MDBoxLayout
        from kivymd.uix.label import MDLabel
        from kivymd.uix.button import MDFlatButton
        from kivymd.uix.scrollview import MDScrollView
        from kivy.metrics import dp
        import re
        
        # Create a layout for the dialog content
        content = MDBoxLayout(orientation="vertical", spacing=dp(10), size_hint_y=None)
        content.bind(minimum_height=content.setter('height'))
        
        # Add a title
        title = MDLabel(text="Season/Episode Details", font_style="H6", size_hint_y=None, height=dp(40))
        content.add_widget(title)
        
        # Calculate total height needed for content
        total_height = dp(40)  # Title height
        
        # Group data by territory combination to show all issues for the same territories together
        territory_combination_to_issues = {}

        # First, process the data to group by territory combination
        entries = season_episode_data.split(';')
        for entry in entries:
            entry = entry.strip()
            if not entry:
                continue

            # Extract territory if present at the end of the entry
            territory_match = re.search(r'\(([^)]+)\)$', entry)
            territories = []
            issue_content = entry

            if territory_match:
                # Get territories from the match
                territories = [t.strip() for t in territory_match.group(1).split(',')]
                # Remove territory part to get the issue content
                issue_content = entry[:territory_match.start()].strip()
            else:
                # No territory in the entry, use the provided territory_data if available
                if territory_data:
                    territories = [t.strip() for t in territory_data.split(',')]
                else:
                    territories = ["General"]

            # Create territory combination key (sorted for consistency)
            territory_key = ",".join(sorted(territories))

            # Group by territory combination
            if territory_key not in territory_combination_to_issues:
                territory_combination_to_issues[territory_key] = []

            # Add this issue to the territory combination (avoid duplicates)
            if issue_content not in territory_combination_to_issues[territory_key]:
                territory_combination_to_issues[territory_key].append(issue_content)
        
        # If no entries were processed but we have territory_data, add empty entries for those territories
        if not territory_combination_to_issues and territory_data:
            terr_list = [t.strip() for t in territory_data.split(',')]
            territory_key = ",".join(sorted(terr_list))
            territory_combination_to_issues[territory_key] = ["No specific issue"]

        # Now display the data grouped by territory combination
        for territory_key, issues in sorted(territory_combination_to_issues.items()):
            # Use the territory_key directly as it's already sorted and combined
            combined_territories = territory_key

            # Add territory header
            territory_row = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(40))
            territory_label = MDLabel(text=f"Territory: {combined_territories}", size_hint_x=1.0, bold=True)
            territory_row.add_widget(territory_label)
            content.add_widget(territory_row)
            total_height += dp(40)

            # Display all issues for this territory combination
            for issue_content in issues:
                if issue_content:
                    # Try to extract header and value from issue content
                    header_match = re.search(r'^([^:]+):\s*(.+)$', issue_content)
                    if header_match:
                        header = header_match.group(1).strip()
                        value = header_match.group(2).strip()

                        # Add header
                        header_row = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(30))
                        spacer = MDLabel(text="", size_hint_x=0.1)
                        header_label = MDLabel(text=f"{header}:", size_hint_x=0.9, bold=True)
                        header_row.add_widget(spacer)
                        header_row.add_widget(header_label)
                        content.add_widget(header_row)
                        total_height += dp(30)

                        # Add value with combined territories
                        display_value = f"{value} ({combined_territories})"

                        # Format complex data with pipes for better readability
                        if '|' in display_value and len(display_value) > 80:
                            # Replace pipes with pipe + newline for better display
                            display_value = display_value.replace('|', '|\n')

                        # Calculate proper height for text wrapping
                        # Count actual newlines in the formatted text
                        actual_lines = display_value.count('\n') + 1

                        # Estimate characters per line based on dialog width (approximately 50-70 chars for better display)
                        chars_per_line = 50
                        estimated_lines = max(1, len(display_value) // chars_per_line + (1 if len(display_value) % chars_per_line > 0 else 0))

                        # Use the maximum of actual lines and estimated lines
                        estimated_lines = max(actual_lines, estimated_lines)

                        # Special handling for complex formatting with pipes and brackets
                        if '|' in display_value or '{' in display_value:
                            # Count pipe segments and bracket groups for better line estimation
                            pipe_segments = display_value.count('|') + 1
                            bracket_groups = display_value.count('{')
                            # Each pipe segment might need its own line for readability
                            estimated_lines = max(estimated_lines, pipe_segments, bracket_groups, len(display_value) // 35 + 1)

                        value_height = max(dp(40), dp(25 * estimated_lines))  # More generous height calculation
                        value_row = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=value_height)
                        spacer2 = MDLabel(text="", size_hint_x=0.2)

                        # Create label with text wrapping and proper font
                        value_label = MDLabel(
                            text=display_value,
                            size_hint_x=0.8,
                            text_size=(None, None),  # Allow text to determine its own size
                            halign="left",
                            valign="top",
                            font_name='UnicodeFont'
                        )
                        # Set text_size after the label is created to enable text wrapping
                        value_label.bind(size=lambda instance, size: setattr(instance, 'text_size', (size[0], None)))

                        value_row.add_widget(spacer2)
                        value_row.add_widget(value_label)
                        content.add_widget(value_row)
                        total_height += value_height
                    else:
                        # Just display the issue content with combined territories
                        display_entry = f"{issue_content} ({combined_territories})"

                        # Format complex data with pipes for better readability
                        if '|' in display_entry and len(display_entry) > 80:
                            # Replace pipes with pipe + newline for better display
                            display_entry = display_entry.replace('|', '|\n')

                        # Calculate proper height for text wrapping
                        # Count actual newlines in the formatted text
                        actual_lines = display_entry.count('\n') + 1

                        chars_per_line = 50
                        estimated_lines = max(1, len(display_entry) // chars_per_line + (1 if len(display_entry) % chars_per_line > 0 else 0))

                        # Use the maximum of actual lines and estimated lines
                        estimated_lines = max(actual_lines, estimated_lines)

                        # Special handling for complex formatting with pipes and brackets
                        if '|' in display_entry or '{' in display_entry:
                            # Count pipe segments and bracket groups for better line estimation
                            pipe_segments = display_entry.count('|') + 1
                            bracket_groups = display_entry.count('{')
                            # Each pipe segment might need its own line for readability
                            estimated_lines = max(estimated_lines, pipe_segments, bracket_groups, len(display_entry) // 35 + 1)

                        value_height = max(dp(40), dp(25 * estimated_lines))
                        entry_row = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=value_height)
                        spacer = MDLabel(text="", size_hint_x=0.1)

                        # Create label with text wrapping and proper font
                        entry_label = MDLabel(
                            text=display_entry,
                            size_hint_x=0.9,
                            text_size=(None, None),
                            halign="left",
                            valign="top",
                            font_name='UnicodeFont'
                        )
                        # Set text_size after the label is created to enable text wrapping
                        entry_label.bind(size=lambda instance, size: setattr(instance, 'text_size', (size[0], None)))

                        entry_row.add_widget(spacer)
                        entry_row.add_widget(entry_label)
                        content.add_widget(entry_row)
                        total_height += value_height
            
            # Add a separator between territories
            separator = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(10))
            content.add_widget(separator)
            total_height += dp(10)
        
        # Set content height
        content.height = total_height
        
        # Create a scroll view for the content with better sizing
        max_dialog_height = dp(700)  # Increased maximum dialog height
        scroll_height = min(max_dialog_height - dp(150), total_height + dp(20))  # Leave space for title and buttons
        scroll = MDScrollView(size_hint=(1, None), height=scroll_height)
        scroll.add_widget(content)

        # Create and show the dialog with improved sizing
        dialog = MDDialog(
            title="Season/Episode Data",
            type="custom",
            content_cls=scroll,
            buttons=[
                MDFlatButton(text="Close", on_release=lambda x: dialog.dismiss())
            ],
            size_hint=(0.9, None),  # Increased width from 0.8 to 0.9 for better text display
            height=min(max_dialog_height, total_height + dp(150))  # Adjust dialog height based on content
        )
        dialog.open()
    
    def restore_autosaved_data(self):
        """Restore data from auto-save"""
        if not hasattr(self, '_autosave_data'):
            return False
        
        try:
            state_data = self._autosave_data
            
            # Restore form data
            form_data = state_data.get("form_data", {})
            for field, value in form_data.items():
                if field in self.entries:
                    if field == "Offer Type":
                        if value and value != "Select Offer Type":
                            self.selected_offers = set(value.split(", "))
                            self.entries[field].text = value
                    elif hasattr(self.entries[field], 'text'):
                        self.entries[field].text = value
            
            # Restore task data
            task_data = state_data.get("task_data", {})
            for field, value in task_data.items():
                if field in self.task_entries and hasattr(self.task_entries[field], 'text'):
                    self.task_entries[field].text = value
                    # Trigger height adjustment for Season/Episode field
                    if field == "Season/Episode" and value:
                        self.on_season_episode_text_change(self.task_entries[field], value)
            
            # Restore other state
            self.selected_issues = state_data.get("selected_issues", [])
            self.issue_name = state_data.get("issue_name", "")
            self.selected_rca_items = state_data.get("selected_rca_items", [])
            self.cti_values = state_data.get("cti_values", ["", "", ""])
            self.cumulative_time = state_data.get("cumulative_time", 0.0)
            self.titles_processed = state_data.get("titles_processed", 0)
            
            # Update displays
            self.time_display.text = f"Total Time: {self.cumulative_time:.2f} min"
            self.titles_display.text = f"Titles Processed: {self.titles_processed}"
            
            # Restore start time if it was saved
            start_time_str = state_data.get("start_time")
            if start_time_str:
                h, m, s = map(int, start_time_str.split(':'))
                self.start_time = datetime.time(hour=h, minute=m, second=s)
            
            # Clear auto-save file after successful restoration
            if os.path.exists(self.autosave_path):
                os.remove(self.autosave_path)
            
            return True
        except Exception as e:
            print(f"Error restoring auto-saved data: {e}")
            return False
            
    def handle_view_details_click(self, season_episode_data, territory_data):
        """Handle click on View Details link in the ticket description"""
        # Show the season/episode data in a dialog
        self.show_season_episode_dialog(season_episode_data, territory_data)
        
    def view_season_episode_details(self, instance):
        """Show Season/Episode details when View Details button is clicked"""
        # Get the Season/Episode data from the task entries
        season_episode_data = self.task_entries.get("Season/Episode", MDTextField()).text
        territory_data = self.task_entries.get("Territory (T)", MDTextField()).text
        
        # Process the data to ensure it's in the right format
        if season_episode_data:
            import re
            
            # Check if the data already contains headers like "Missing Episode - Offer:"
            has_headers = any(header in season_episode_data for header in [
                "Missing Episode - Offer", 
                "Missing Season - Offer", 
                "Missing Episode - Product", 
                "Missing Season - Product", 
                "Last episode missing"
            ])
            
            if not has_headers:
                # Try to parse the complex format with territories in parentheses
                # Format: S10-(2,3,4,5)(CO); S11-(1,2,3)(US,UK)
                formatted_entries = []
                
                # Split by semicolons first
                entries = season_episode_data.split(';')
                for entry in entries:
                    entry = entry.strip()
                    if not entry:
                        continue
                    
                    # Try to match patterns like "S10-(2,3,4,5)(CO)" or "1-6(CY,EE,HR)"
                    season_pattern1 = re.compile(r'S(\d+)-\(([^)]+)\)(\([^)]+\))?')
                    season_pattern2 = re.compile(r'(\d+)-(\d+)(\([^)]+\))?')
                    
                    match1 = season_pattern1.search(entry)
                    match2 = season_pattern2.search(entry)
                    
                    if match1:  # Format: S10-(2,3,4,5)(CO)
                        season_num = match1.group(1)
                        episodes = match1.group(2)
                        territory = match1.group(3) or ''
                        formatted_entries.append(f"Last episode missing: {{S{season_num}-({episodes})}}{territory}")
                    elif match2:  # Format: 1-6(CY,EE,HR)
                        start = match2.group(1)
                        end = match2.group(2)
                        territory = match2.group(3) or ''
                        formatted_entries.append(f"Missing Season - Offer: {{{start}-{end}}}{territory}")
                    else:
                        # If no pattern matches, just add as is
                        formatted_entries.append(f"Season/Episode: {entry}")
                
                season_episode_data = "; ".join(formatted_entries)
            else:
                # If the data already has headers, make sure each entry has its territory
                # This handles cases like "Missing Season - Offer: {9}; Last episode mising: {S10-(2,3...)}" in BR
                # and the same pattern in MX
                entries = season_episode_data.split(';')
                formatted_entries = []
                
                # Check if we have territory data to associate with entries
                territories = [t.strip() for t in territory_data.split(',')] if territory_data else []
                
                # If we have territories, we need to add the territory to each entry
                if territories:
                    # Process each entry and assign territories appropriately
                    for i, entry in enumerate(entries):
                        entry = entry.strip()
                        if not entry:
                            continue

                        # Check if this entry already has a territory marker at the end
                        # Territory markers should be at the very end like "(CO)" or "(US,UK)"
                        import re
                        territory_pattern = r'\([A-Z]{2}(?:,[A-Z]{2})*\)$'
                        has_territory = bool(re.search(territory_pattern, entry))

                        if has_territory:
                            formatted_entries.append(entry)
                        else:
                            # For multiple territories, assign them cyclically to entries
                            # Or if there's only one territory, assign it to all entries
                            if len(territories) == 1:
                                # Single territory - assign to all entries
                                territory = territories[0]
                                formatted_entries.append(f"{entry}({territory})")
                            else:
                                # Multiple territories - assign each territory to corresponding entry
                                # If more entries than territories, cycle through territories
                                territory = territories[i % len(territories)]
                                formatted_entries.append(f"{entry}({territory})")

                    if formatted_entries:
                        season_episode_data = "; ".join(formatted_entries)
            
            # Show the dialog with the data
            self.show_season_episode_dialog(season_episode_data, territory_data)
        else:
            # Show a message if no data is available
            dialog = MDDialog(title="No Data", text="No Season/Episode data available to display.")
            dialog.open()

if __name__ == "__main__":
    try:
        TVSeriesForm().run()
    except KeyboardInterrupt:
        print("Application interrupted by user")
    except Exception as e:
        print(f"Application error: {e}")
