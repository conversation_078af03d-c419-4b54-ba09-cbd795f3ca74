import pyodbc
import os

# Test database connection
db_path = r"\\ant\dept-in\Digiflex\DF-Cataloghealth\Quality Products\Audits\Workflow\Workflow database.accdb"

print("Testing database connection...")
print(f"Database path: {db_path}")

# Check if file exists
if os.path.exists(db_path):
    print("✓ Database file is accessible")
else:
    print("✗ Database file is NOT accessible")
    print("Check VPN connection and network permissions")
    exit(1)

# Test connection
conn_strings = [
    f"DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={db_path};",
    f"DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={db_path};ReadOnly=0;",
    f"DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={db_path};Exclusive=0;"
]

for i, conn_str in enumerate(conn_strings):
    try:
        print(f"\nTrying connection string {i+1}...")
        conn = pyodbc.connect(conn_str, timeout=10)
        
        # Test query
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM Workflow")
        count = cursor.fetchone()[0]
        
        # Test process query
        cursor.execute("SELECT DISTINCT Process FROM Workflow")
        processes = cursor.fetchall()
        
        cursor.close()
        conn.close()
        
        print(f"✓ SUCCESS! Found {count} records and {len(processes)} processes")
        print(f"Working connection string: {conn_str}")
        break
        
    except Exception as e:
        print(f"✗ Failed: {str(e)[:100]}...")
        
print("\nTest complete.")