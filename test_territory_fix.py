#!/usr/bin/env python3
"""
Test script to verify the territory assignment fix for semicolon-separated data.
"""

def test_territory_assignment():
    """Test the territory assignment logic for semicolon-separated entries."""
    
    # Test case 1: Single territory with multiple semicolon-separated entries
    print("Test Case 1: Single territory with multiple entries")
    season_episode_data = "Missing Season - Offer: {9}; Last episode missing: {S10-(2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22)}"
    territory_data = "CO"
    
    # Split entries by semicolons
    entries = [e.strip() for e in season_episode_data.split(';') if e.strip()]
    territories = [t.strip() for t in territory_data.split(',') if t.strip()]
    
    print(f"Original data: {season_episode_data}")
    print(f"Territory: {territory_data}")
    print(f"Entries: {entries}")
    print(f"Territories: {territories}")
    
    # Apply the fixed logic
    formatted_entries = []
    for i, entry in enumerate(entries):
        entry = entry.strip()
        if not entry:
            continue
            
        # Check if this entry already has a territory marker at the end
        # Territory markers should be at the very end like "(CO)" or "(US,UK)"
        import re
        territory_pattern = r'\([A-Z]{2}(?:,[A-Z]{2})*\)$'
        has_territory = bool(re.search(territory_pattern, entry))

        if has_territory:
            formatted_entries.append(entry)
        else:
            # For single territory, assign to all entries
            if len(territories) == 1:
                territory = territories[0]
                formatted_entries.append(f"{entry}({territory})")
            else:
                # Multiple territories - assign each territory to corresponding entry
                territory = territories[i % len(territories)]
                formatted_entries.append(f"{entry}({territory})")
    
    result = "; ".join(formatted_entries)
    print(f"Result: {result}")
    print()
    
    # Test case 2: Multiple territories with multiple entries
    print("Test Case 2: Multiple territories with multiple entries")
    season_episode_data = "Missing Season - Offer: {9}; Last episode missing: {S10-(2,3,4,5)}"
    territory_data = "CO, MX"
    
    entries = [e.strip() for e in season_episode_data.split(';') if e.strip()]
    territories = [t.strip() for t in territory_data.split(',') if t.strip()]
    
    print(f"Original data: {season_episode_data}")
    print(f"Territory: {territory_data}")
    print(f"Entries: {entries}")
    print(f"Territories: {territories}")
    
    formatted_entries = []
    for i, entry in enumerate(entries):
        entry = entry.strip()
        if not entry:
            continue
            
        # Check if this entry already has a territory marker at the end
        # Territory markers should be at the very end like "(CO)" or "(US,UK)"
        import re
        territory_pattern = r'\([A-Z]{2}(?:,[A-Z]{2})*\)$'
        has_territory = bool(re.search(territory_pattern, entry))

        if has_territory:
            formatted_entries.append(entry)
        else:
            # For single territory, assign to all entries
            if len(territories) == 1:
                territory = territories[0]
                formatted_entries.append(f"{entry}({territory})")
            else:
                # Multiple territories - assign each territory to corresponding entry
                territory = territories[i % len(territories)]
                formatted_entries.append(f"{entry}({territory})")
    
    result = "; ".join(formatted_entries)
    print(f"Result: {result}")
    print()
    
    # Test case 3: Entry already has territory
    print("Test Case 3: Entry already has territory")
    season_episode_data = "Missing Season - Offer: {9}(US); Last episode missing: {S10-(2,3,4,5)}"
    territory_data = "CO"
    
    entries = [e.strip() for e in season_episode_data.split(';') if e.strip()]
    territories = [t.strip() for t in territory_data.split(',') if t.strip()]
    
    print(f"Original data: {season_episode_data}")
    print(f"Territory: {territory_data}")
    print(f"Entries: {entries}")
    print(f"Territories: {territories}")
    
    formatted_entries = []
    for i, entry in enumerate(entries):
        entry = entry.strip()
        if not entry:
            continue
            
        # Check if this entry already has a territory marker at the end
        # Territory markers should be at the very end like "(CO)" or "(US,UK)"
        import re
        territory_pattern = r'\([A-Z]{2}(?:,[A-Z]{2})*\)$'
        has_territory = bool(re.search(territory_pattern, entry))

        if has_territory:
            formatted_entries.append(entry)
        else:
            # For single territory, assign to all entries
            if len(territories) == 1:
                territory = territories[0]
                formatted_entries.append(f"{entry}({territory})")
            else:
                # Multiple territories - assign each territory to corresponding entry
                territory = territories[i % len(territories)]
                formatted_entries.append(f"{entry}({territory})")
    
    result = "; ".join(formatted_entries)
    print(f"Result: {result}")
    print()

if __name__ == "__main__":
    test_territory_assignment()
