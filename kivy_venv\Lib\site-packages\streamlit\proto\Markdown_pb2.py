# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/Markdown.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1estreamlit/proto/Markdown.proto\"\xc8\x01\n\x08Markdown\x12\x0c\n\x04\x62ody\x18\x01 \x01(\t\x12\x12\n\nallow_html\x18\x02 \x01(\x08\x12\x12\n\nis_caption\x18\x03 \x01(\x08\x12$\n\x0c\x65lement_type\x18\x04 \x01(\x0e\x32\x0e.Markdown.Type\x12\x0c\n\x04help\x18\x05 \x01(\t\"R\n\x04Type\x12\x0f\n\x0bUNSPECIFIED\x10\x00\x12\n\n\x06NATIVE\x10\x01\x12\x0b\n\x07\x43\x41PTION\x10\x02\x12\x08\n\x04\x43ODE\x10\x03\x12\t\n\x05LATEX\x10\x04\x12\x0b\n\x07\x44IVIDER\x10\x05\x42-\n\x1c\x63om.snowflake.apps.streamlitB\rMarkdownProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.Markdown_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\rMarkdownProto'
  _globals['_MARKDOWN']._serialized_start=35
  _globals['_MARKDOWN']._serialized_end=235
  _globals['_MARKDOWN_TYPE']._serialized_start=153
  _globals['_MARKDOWN_TYPE']._serialized_end=235
# @@protoc_insertion_point(module_scope)
