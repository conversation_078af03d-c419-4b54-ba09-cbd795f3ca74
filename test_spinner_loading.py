import pyodbc
from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.spinner import Spinner
from kivy.uix.button import Button
from kivy.clock import Clock
import threading

class TestSpinnerApp(App):
    def __init__(self):
        super().__init__()
        self.processes = []
        self.sub_processes = {}
        self.conn_str = (
            r"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};"
            r"DBQ=\\ant\dept-in\Digiflex\DF-Cataloghealth\Quality Products\Audits\Workflow\Workflow database.accdb;"
        )
    
    def build(self):
        layout = BoxLayout(orientation='vertical', padding=10, spacing=10)
        
        # Process spinner
        self.process_spinner = Spinner(
            text='Select Process',
            values=[],
            size_hint_y=None,
            height=40
        )
        self.process_spinner.bind(text=self.on_process_selected)
        
        # Sub-process spinner
        self.sub_process_spinner = Spinner(
            text='Select Sub Process',
            values=[],
            size_hint_y=None,
            height=40
        )
        
        # Test button
        test_button = Button(
            text='Load Data',
            size_hint_y=None,
            height=40
        )
        test_button.bind(on_press=self.load_data)
        
        layout.add_widget(self.process_spinner)
        layout.add_widget(self.sub_process_spinner)
        layout.add_widget(test_button)
        
        # Load data on startup
        Clock.schedule_once(lambda dt: self.load_data(None), 1)
        
        return layout
    
    def load_data(self, instance):
        print("Loading data...")
        try:
            conn = pyodbc.connect(self.conn_str)
            cursor = conn.cursor()
            
            # Load processes
            cursor.execute("SELECT DISTINCT Process FROM Workflow")
            processes = cursor.fetchall()
            self.processes = [row[0] for row in processes]
            
            print(f"Loaded {len(self.processes)} processes")
            
            # Update spinner
            self.process_spinner.values = self.processes
            
            cursor.close()
            conn.close()
            
            print("Data loaded successfully")
            
        except Exception as e:
            print(f"Error loading data: {e}")
            import traceback
            traceback.print_exc()
    
    def on_process_selected(self, spinner, text):
        print(f"Process selected: {text}")
        if text == 'Select Process':
            return
            
        try:
            conn = pyodbc.connect(self.conn_str)
            cursor = conn.cursor()
            
            cursor.execute("SELECT DISTINCT [Sub Process] FROM Workflow WHERE Process = ?", (text,))
            sub_processes = cursor.fetchall()
            
            if sub_processes:
                sub_process_list = [row[0] for row in sub_processes]
                self.sub_process_spinner.values = sub_process_list
                self.sub_process_spinner.text = 'Select Sub Process'
                print(f"Loaded {len(sub_process_list)} sub-processes")
            else:
                self.sub_process_spinner.values = []
                self.sub_process_spinner.text = 'No Sub Processes'
                print("No sub-processes found")
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            print(f"Error loading sub-processes: {e}")

if __name__ == '__main__':
    TestSpinnerApp().run()