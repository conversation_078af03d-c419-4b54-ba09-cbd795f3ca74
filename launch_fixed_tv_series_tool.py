#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
TV Series Task Tool Launcher
This script launches the TV Series Task Tool with error handling
"""

import os
import sys
import traceback
import subprocess
import time
import tkinter as tk
from tkinter import messagebox

def show_error_dialog(title, message):
    """Show error dialog with details"""
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    messagebox.showerror(title, message)
    root.destroy()

def main():
    """Main function to launch the TV Series Task Tool"""
    try:
        print("Starting TV Series Task Tool...")
        
        # Path to the fixed tool
        tool_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 
                                "TV Series TT Tool_fixed.py")
        
        if not os.path.exists(tool_path):
            show_error_dialog("Error", f"Tool not found at: {tool_path}")
            return
        
        # Launch the tool with Python
        process = subprocess.Popen([sys.executable, tool_path], 
                                  stdout=subprocess.PIPE, 
                                  stderr=subprocess.PIPE,
                                  text=True)
        
        # Wait for the process to complete
        stdout, stderr = process.communicate()
        
        # Check if there was an error
        if process.returncode != 0:
            error_message = f"Tool exited with error code: {process.returncode}\n\n"
            error_message += f"STDOUT:\n{stdout}\n\n"
            error_message += f"STDERR:\n{stderr}"
            
            # Write error to log file
            with open("tv_series_tool_error.log", "w", encoding="utf-8") as f:
                f.write(error_message)
            
            show_error_dialog("Tool Error", 
                             f"The tool encountered an error. See tv_series_tool_error.log for details.")
    
    except Exception as e:
        error_message = f"Failed to launch tool: {str(e)}\n\n{traceback.format_exc()}"
        with open("launcher_error.log", "w", encoding="utf-8") as f:
            f.write(error_message)
        show_error_dialog("Launcher Error", 
                         f"Failed to launch tool: {str(e)}\nSee launcher_error.log for details.")

if __name__ == "__main__":
    main()