import tkinter as tk
from tkinter import filedialog, messagebox
import pandas as pd
import pyodbc
import os
from datetime import datetime

# Path to Access DB
ACCESS_DB_PATH = r"\\ant\dept-in\Digiflex\DF-Cataloghealth\Quality Products\Tickets\TT Details Database.accdb"

# Table names
TASK_ALLOCATION_TABLE = "Task Allocation"
TT_TEMPLATE_TABLE = "TT Template"

# Expected columns in Excel file
EXPECTED_COLUMNS = [
    "QC Date", "User ID", "AHT", "Series GTI", "Series Name", "Line of business", "Territory", "Partner",
    "Impressions", "Season/Episode", "Input Date", "Product Episode Missing", "Product Season Missing",
    "Offer Episode Missing", "Offer Season Missing", "Last Episode Missing", "Wrong Content Playing"
]

def import_task():
    file_path = filedialog.askopenfilename(
        title="Select Excel File",
        filetypes=[("Excel files", "*.xlsx *.xls")]
    )

    if not file_path:
        return  # User cancelled

    try:
        # Load Excel data
        df = pd.read_excel(file_path)
        
        # Display Excel columns for debugging
        print(f"Excel columns: {list(df.columns)}")

        # Check for expected columns
        missing = [col for col in EXPECTED_COLUMNS if col not in df.columns]
        if missing:
            messagebox.showerror("Error", f"Missing columns in Excel: {', '.join(missing)}")
            return

        # Clean NaN values and convert data types
        df = df[EXPECTED_COLUMNS].fillna("")
        
        # Convert date columns to proper format
        if 'QC Date' in df.columns:
            df['QC Date'] = pd.to_datetime(df['QC Date']).dt.date
        if 'Input Date' in df.columns:
            df['Input Date'] = pd.to_datetime(df['Input Date']).dt.date
            
        # Convert numeric columns to proper format
        for col in ['AHT', 'Impressions']:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
        
        # Convert boolean columns to Yes/No strings
        for col in ['Product Episode Missing', 'Product Season Missing', 'Offer Episode Missing', 
                   'Offer Season Missing', 'Last Episode Missing', 'Wrong Content Playing']:
            if col in df.columns:
                df[col] = df[col].map({True: 'Yes', False: 'No', 1: 'Yes', 0: 'No'}).fillna('')
        
        # Show progress window
        progress_window = tk.Toplevel(root)
        progress_window.title("Importing Data")
        progress_window.geometry("300x100")
        progress_label = tk.Label(progress_window, text="Importing data, please wait...")
        progress_label.pack(pady=20)
        progress_window.update()
        
        # Connect to Access DB
        conn_str = (
            r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
            rf'DBQ={ACCESS_DB_PATH};'
        )
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        
        # Try simpler approach - insert row by row
        rows_inserted = 0
        for _, row in df.iterrows():
            try:
                values = [row[col] for col in EXPECTED_COLUMNS]
                placeholders = ','.join(['?'] * len(EXPECTED_COLUMNS))
                columns_str = ','.join([f'[{col}]' for col in EXPECTED_COLUMNS])
                sql = f"INSERT INTO [{TASK_ALLOCATION_TABLE}] ({columns_str}) VALUES ({placeholders})"
                cursor.execute(sql, values)
                conn.commit()
                rows_inserted += 1
            except Exception as row_error:
                print(f"Error inserting row: {str(row_error)}")
                print(f"Row data: {values}")
                continue
        
        cursor.close()
        conn.close()
        
        # Close progress window
        progress_window.destroy()

        if rows_inserted > 0:
            messagebox.showinfo("Success", f"Data imported successfully! {rows_inserted} rows processed.")
        else:
            messagebox.showerror("Error", "No rows were imported. Check console for details.")



    except Exception as e:
        messagebox.showerror("Error", f"Failed to import data.\n{str(e)}")

def remove_all_data():
    if not messagebox.askyesno("Confirm Delete", "Are you sure you want to delete ALL data from the table?"):
        return
    
    try:
        # Show progress window
        progress_window = tk.Toplevel(root)
        progress_window.title("Removing Data")
        progress_window.geometry("300x100")
        progress_label = tk.Label(progress_window, text="Removing all data, please wait...")
        progress_label.pack(pady=20)
        progress_window.update()
        
        # Connect to Access DB
        conn_str = (
            r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
            rf'DBQ={ACCESS_DB_PATH};'
        )
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        
        # Delete all rows
        cursor.execute(f"DELETE FROM [{TASK_ALLOCATION_TABLE}]")
        conn.commit()
        
        # Get number of rows affected
        rows_deleted = cursor.rowcount
        
        cursor.close()
        conn.close()
        
        # Close progress window
        progress_window.destroy()
        
        messagebox.showinfo("Success", f"All data removed successfully!")
        
    except Exception as e:
        messagebox.showerror("Error", f"Failed to remove data.\n{str(e)}")

def export_consolidation():
    try:
        # Show progress window
        progress_window = tk.Toplevel(root)
        progress_window.title("Exporting Data")
        progress_window.geometry("300x100")
        progress_label = tk.Label(progress_window, text="Exporting recent data, please wait...")
        progress_label.pack(pady=20)
        progress_window.update()
        
        # Connect to Access DB
        conn_str = (
            r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
            rf'DBQ={ACCESS_DB_PATH};'
        )
        conn = pyodbc.connect(conn_str)
        
        # Get the most recent date from the database
        query = f"SELECT MAX([Date]) FROM [{TT_TEMPLATE_TABLE}]"
        df_date = pd.read_sql(query, conn)
        most_recent_date = df_date.iloc[0, 0]
        
        if most_recent_date is None:
            progress_window.destroy()
            messagebox.showerror("Error", "No data found in the database.")
            return
            
        # Query to get all rows with the most recent date
        query = f"SELECT * FROM [{TT_TEMPLATE_TABLE}] WHERE [Date] = ?"
        df = pd.read_sql(query, conn, params=[most_recent_date])
        
        conn.close()
        
        # Close progress window
        progress_window.destroy()
        
        if df.empty:
            messagebox.showerror("Error", "No data found for the most recent date.")
            return
            
        # Ask user where to save the Excel file
        current_date = datetime.now().strftime("%Y%m%d")
        default_filename = f"Consolidation_{current_date}.xlsx"
        file_path = filedialog.asksaveasfilename(
            title="Save Consolidated Data",
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx")],
            initialfile=default_filename
        )
        
        if not file_path:
            return  # User cancelled
            
        # Export to Excel
        df.to_excel(file_path, index=False)
        messagebox.showinfo("Success", f"Data exported successfully!\n{len(df)} rows exported from {most_recent_date}.")
        
    except Exception as e:
        messagebox.showerror("Error", f"Failed to export data.\n{str(e)}")

def export_tt_consolidation():
    try:
        # Show progress window
        progress_window = tk.Toplevel(root)
        progress_window.title("TT Consolidation")
        progress_window.geometry("300x100")
        progress_label = tk.Label(progress_window, text="Exporting TT data, please wait...")
        progress_label.pack(pady=20)
        progress_window.update()
        
        # Connect to Access DB
        conn_str = (
            r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
            rf'DBQ={ACCESS_DB_PATH};'
        )
        conn = pyodbc.connect(conn_str)
        
        # Get the most recent date from the database
        query = f"SELECT MAX([Date]) FROM [{TT_TEMPLATE_TABLE}]"
        df_date = pd.read_sql(query, conn)
        most_recent_date = df_date.iloc[0, 0]
        
        if most_recent_date is None:
            progress_window.destroy()
            messagebox.showerror("Error", "No data found in the database.")
            return
            
        # Get current Windows username
        current_user = os.getenv('USERNAME')
        
        # Query to get rows with the most recent date for current user
        query = f"SELECT * FROM [{TT_TEMPLATE_TABLE}] WHERE [Date] = ? AND [User ID] = ?"
        df = pd.read_sql(query, conn, params=[most_recent_date, current_user])
        
        conn.close()
        
        # Close progress window
        progress_window.destroy()
        
        if df.empty:
            messagebox.showerror("Error", f"No data found for user {current_user} on the most recent date.")
            return
        
        # Filter out rows with blank RCA values
        if 'RCA' in df.columns:
            df = df[df['RCA'].notna() & (df['RCA'] != '')]
            
            if df.empty:
                messagebox.showerror("Error", "No data found with non-blank RCA values.")
                return
        else:
            messagebox.showwarning("Warning", "RCA column not found in the data. Exporting all rows.")
            
        # Ask user where to save the Excel file
        current_date = datetime.now().strftime("%Y%m%d")
        default_filename = f"TT_Consolidation_{current_user}_{current_date}.xlsx"
        file_path = filedialog.asksaveasfilename(
            title="Save TT Consolidated Data",
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx")],
            initialfile=default_filename
        )
        
        if not file_path:
            return  # User cancelled
            
        # Export to Excel
        df.to_excel(file_path, index=False)
        messagebox.showinfo("Success", f"TT data exported successfully!\n{len(df)} rows exported from {most_recent_date}.")
        
    except Exception as e:
        messagebox.showerror("Error", f"Failed to export TT data.\n{str(e)}")

# GUI Setup
root = tk.Tk()
root.title("Task Import Tool")
root.geometry("300x250")

frame = tk.Frame(root)
frame.pack(expand=True)

# Import button
import_btn = tk.Button(frame, text="Import Task", command=import_task, font=("Arial", 12), width=20)
import_btn.pack(pady=10)

# Consolidation button
consolidation_btn = tk.Button(frame, text="Consolidation", command=export_consolidation, font=("Arial", 12), width=20, bg="#6bb5ff")
consolidation_btn.pack(pady=10)

# TT Consolidation button
tt_consolidation_btn = tk.Button(frame, text="TT Consolidation", command=export_tt_consolidation, font=("Arial", 12), width=20, bg="#6bc5ff")
tt_consolidation_btn.pack(pady=10)

# Remove button
remove_btn = tk.Button(frame, text="Remove All", command=remove_all_data, font=("Arial", 12), width=20, bg="#ff6b6b")
remove_btn.pack(pady=10)

root.mainloop()
