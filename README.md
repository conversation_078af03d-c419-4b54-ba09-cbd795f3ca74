# TV Series Task Tool

This tool allows users to create tickets for TV Series tasks and export data from an Access database.

## Features

- Simple GUI with an "Import" button that exports data from the database
- Exports the most recent data with RCA information for the current user
- Pre-fills the ticket form with the exported data
- Provides feedback on export success or errors

## Database Connection

The tool connects to the following database:
```
\\ant\dept-in\Digiflex\DF-Cataloghealth\Quality Products\Tickets\TT Details Database.accdb
```

## Usage

1. Click the "Import" button
2. The tool will fetch the most recent data for the current user from the database
3. The ticket form will be pre-filled with the exported data
4. Complete any missing information and submit the ticket

## Troubleshooting

- If you get a "Database Error", verify that you have access to the database file
- Make sure the Microsoft Access Driver is installed on your system
- If no data is found, ensure you have previously submitted tickets with RCA information