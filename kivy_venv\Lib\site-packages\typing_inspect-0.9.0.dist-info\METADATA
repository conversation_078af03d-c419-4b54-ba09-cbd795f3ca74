Metadata-Version: 2.1
Name: typing-inspect
Version: 0.9.0
Summary: Runtime inspection utilities for typing module.
Home-page: https://github.com/ilev<PERSON><PERSON>i/typing_inspect
Author: <PERSON>
Author-email: <PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com
License: MIT
Keywords: typing function annotations type hints hinting checking checker typehints typehinting typechecking inspect reflection introspection
Classifier: Development Status :: 3 - Alpha
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Software Development
License-File: LICENSE
Requires-Dist: mypy-extensions (>=0.3.0)
Requires-Dist: typing-extensions (>=3.7.4)
Requires-Dist: typing (>=3.7.4) ; python_version < "3.5"

Typing Inspect
==============

The "typing_inspect" module defines experimental API for runtime
inspection of types defined in the standard "typing" module.
