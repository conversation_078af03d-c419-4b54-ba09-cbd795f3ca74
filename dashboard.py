import streamlit as st
import pandas as pd
import plotly.express as px

# Title
st.title("📊 Productivity Dashboard")

# Load or simulate data
data = pd.DataFrame({
    'Date': pd.date_range(start='2025-01-01', periods=10),
    'Productivity (%)': [70, 75, 80, 90, 85, 95, 60, 75, 88, 92]
})

# Date Filter
start_date = st.date_input("Start Date", data['Date'].min())
end_date = st.date_input("End Date", data['Date'].max())

# Filter data by date
filtered_data = data[(data['Date'] >= pd.to_datetime(start_date)) & (data['Date'] <= pd.to_datetime(end_date))]

# Show table
st.dataframe(filtered_data)

# Plot chart
fig = px.line(filtered_data, x='Date', y='Productivity (%)', title='Productivity Over Time')
st.plotly_chart(fig)
