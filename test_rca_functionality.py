#!/usr/bin/env python3
"""
Test script to verify the RCA functionality for multiple button presses.
"""

class MockTextField:
    def __init__(self):
        self.text = ""
        self._text_change_callbacks = []
    
    def bind(self, **kwargs):
        if 'text' in kwargs:
            self._text_change_callbacks.append(kwargs['text'])
    
    def unbind(self, **kwargs):
        if 'text' in kwargs:
            callback = kwargs['text']
            if callback in self._text_change_callbacks:
                self._text_change_callbacks.remove(callback)

class MockTVSeriesForm:
    def __init__(self):
        self.selected_rca_items = []
        self.active_form = 'main'
        self.entries = {"RCA": MockTextField()}
    
    def normalize_unicode_text(self, text):
        return text
    
    def on_rca_text_change(self, instance, text):
        # Normalize Unicode text first
        normalized_text = self.normalize_unicode_text(text)
        if normalized_text != text:
            print(f"RCA text normalization: '{text}' -> '{normalized_text}'")
            instance.text = normalized_text
        
        # Debug: Print when RCA text changes
        print(f"RCA text changed to: '{text}'")
    
    def set_rca(self, rca_text):
        """Add RCA text to the active form's RCA field"""
        if self.active_form == 'main':
            # Check if this RCA text is already in the list to avoid duplicates
            if rca_text not in self.selected_rca_items:
                # Append the new RCA text to the list
                self.selected_rca_items.append(rca_text)
                rca_field = self.entries["RCA"]
                
                # Temporarily unbind the text change event to prevent interference
                rca_field.unbind(text=self.on_rca_text_change)
                
                # Format all RCA items with issue numbers
                formatted_text = "\n\n".join([f"Issue {i+1}: {item}" for i, item in enumerate(self.selected_rca_items)])
                rca_field.text = formatted_text
                
                # Re-bind the text change event
                rca_field.bind(text=self.on_rca_text_change)
                
                print(f"Added RCA item {len(self.selected_rca_items)}: {rca_text}")
                print(f"Current RCA items: {self.selected_rca_items}")
                print(f"RCA field text: '{rca_field.text}'")
                print()

def test_rca_functionality():
    """Test the RCA functionality for multiple button presses."""
    
    print("Testing RCA functionality with multiple button presses")
    print("=" * 60)
    
    # Create mock form
    form = MockTVSeriesForm()
    
    # Test RCA options
    rca_options = [
        "Episode X is missing/unavailable (play button missing) due to a future avail start-date. Please confirm whether this is intended. If not, please request the partner for corrected avail re-delivery.",
        "Episode X is missing/unavailable due to expired avail. Please confirm whether this is intended. If not, please request a partner for corrected avail re-delivery.",
        "Episode X is missing/unavailable due to no or missing avail. Please confirm whether this is intended. If not, please request the partner for corrected avail re-delivery."
    ]
    
    # Simulate pressing multiple RCA buttons
    print("Simulating pressing multiple RCA buttons:")
    print()
    
    for i, rca_text in enumerate(rca_options):
        print(f"Pressing RCA button {i+1}:")
        print(f"RCA Text: {rca_text[:50]}...")
        form.set_rca(rca_text)
    
    print("Final state:")
    print(f"Total RCA items: {len(form.selected_rca_items)}")
    print(f"RCA field content:")
    print(form.entries["RCA"].text)
    print()
    
    # Test duplicate prevention
    print("Testing duplicate prevention:")
    print("Pressing the same RCA button again...")
    form.set_rca(rca_options[0])  # Try to add the first option again
    
    print(f"Total RCA items after duplicate attempt: {len(form.selected_rca_items)}")
    print("Should still be 3 items (no duplicates)")

if __name__ == "__main__":
    test_rca_functionality()
