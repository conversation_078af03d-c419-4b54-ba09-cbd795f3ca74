#!/usr/bin/env python3
"""
Test script to verify the territory grouping logic for combining territories with same issues.
"""

def test_territory_grouping():
    """Test the territory grouping logic for combining territories with same issues."""
    
    # Test case 1: Multiple territories with same issue
    print("Test Case 1: Multiple territories with same issue")
    season_episode_data = "Missing Season - Offer: {1-6}(LT); Missing Season - Offer: {1-6}(LV); Missing Season - Offer: {1-6}(MT)"
    
    print(f"Original data: {season_episode_data}")
    
    # Apply the new grouping logic
    import re
    issue_to_territories = {}
    
    # Process the data to group by issue content
    entries = season_episode_data.split(';')
    for entry in entries:
        entry = entry.strip()
        if not entry:
            continue
            
        # Extract territory if present at the end of the entry
        territory_pattern = r'\([A-Z]{2}(?:,[A-Z]{2})*\)$'
        territory_match = re.search(territory_pattern, entry)
        territories = []
        issue_content = entry
        
        if territory_match:
            # Get territories from the match
            territories = [t.strip() for t in territory_match.group(0)[1:-1].split(',')]
            # Remove territory part to get the issue content
            issue_content = entry[:territory_match.start()].strip()
        else:
            territories = ["General"]
        
        # Group by issue content
        if issue_content not in issue_to_territories:
            issue_to_territories[issue_content] = set()
        
        # Add all territories for this issue
        for territory in territories:
            issue_to_territories[issue_content].add(territory)
    
    # Create formatted entries with combined territories
    all_formatted_entries = []
    for issue_content, territories in sorted(issue_to_territories.items()):
        # Convert territories set to sorted list and combine them
        territory_list = sorted(list(territories))
        combined_territories = ",".join(territory_list)
        
        # Create the formatted entry with combined territories
        formatted_entry = f"{issue_content}({combined_territories})"
        all_formatted_entries.append(formatted_entry)
    
    result = '; '.join(all_formatted_entries)
    print(f"Result: {result}")
    print()
    
    # Test case 2: Different issues with different territories
    print("Test Case 2: Different issues with different territories")
    season_episode_data = "Missing Season - Offer: {1-6}(LT); Last episode missing: {S10-(2,3,4,5)}(CO); Missing Season - Offer: {1-6}(LV)"
    
    print(f"Original data: {season_episode_data}")
    
    issue_to_territories = {}
    
    # Process the data to group by issue content
    entries = season_episode_data.split(';')
    for entry in entries:
        entry = entry.strip()
        if not entry:
            continue
            
        # Extract territory if present at the end of the entry
        territory_pattern = r'\([A-Z]{2}(?:,[A-Z]{2})*\)$'
        territory_match = re.search(territory_pattern, entry)
        territories = []
        issue_content = entry
        
        if territory_match:
            # Get territories from the match
            territories = [t.strip() for t in territory_match.group(0)[1:-1].split(',')]
            # Remove territory part to get the issue content
            issue_content = entry[:territory_match.start()].strip()
        else:
            territories = ["General"]
        
        # Group by issue content
        if issue_content not in issue_to_territories:
            issue_to_territories[issue_content] = set()
        
        # Add all territories for this issue
        for territory in territories:
            issue_to_territories[issue_content].add(territory)
    
    # Create formatted entries with combined territories
    all_formatted_entries = []
    for issue_content, territories in sorted(issue_to_territories.items()):
        # Convert territories set to sorted list and combine them
        territory_list = sorted(list(territories))
        combined_territories = ",".join(territory_list)
        
        # Create the formatted entry with combined territories
        formatted_entry = f"{issue_content}({combined_territories})"
        all_formatted_entries.append(formatted_entry)
    
    result = '; '.join(all_formatted_entries)
    print(f"Result: {result}")
    print()
    
    # Test case 3: Mixed format with some entries having multiple territories
    print("Test Case 3: Mixed format with some entries having multiple territories")
    season_episode_data = "Missing Season - Offer: {1-6}(LT,LV); Missing Season - Offer: {1-6}(MT); Last episode missing: {S10-(2,3,4,5)}(CO)"
    
    print(f"Original data: {season_episode_data}")
    
    issue_to_territories = {}
    
    # Process the data to group by issue content
    entries = season_episode_data.split(';')
    for entry in entries:
        entry = entry.strip()
        if not entry:
            continue
            
        # Extract territory if present at the end of the entry
        territory_pattern = r'\([A-Z]{2}(?:,[A-Z]{2})*\)$'
        territory_match = re.search(territory_pattern, entry)
        territories = []
        issue_content = entry
        
        if territory_match:
            # Get territories from the match
            territories = [t.strip() for t in territory_match.group(0)[1:-1].split(',')]
            # Remove territory part to get the issue content
            issue_content = entry[:territory_match.start()].strip()
        else:
            territories = ["General"]
        
        # Group by issue content
        if issue_content not in issue_to_territories:
            issue_to_territories[issue_content] = set()
        
        # Add all territories for this issue
        for territory in territories:
            issue_to_territories[issue_content].add(territory)
    
    # Create formatted entries with combined territories
    all_formatted_entries = []
    for issue_content, territories in sorted(issue_to_territories.items()):
        # Convert territories set to sorted list and combine them
        territory_list = sorted(list(territories))
        combined_territories = ",".join(territory_list)
        
        # Create the formatted entry with combined territories
        formatted_entry = f"{issue_content}({combined_territories})"
        all_formatted_entries.append(formatted_entry)
    
    result = '; '.join(all_formatted_entries)
    print(f"Result: {result}")
    print()

if __name__ == "__main__":
    test_territory_grouping()
