import pyodbc
import filelock
import os
import time
import random
import datetime
import threading
from contextlib import contextmanager

class DatabaseManager:
    """
    A class to manage database connections with concurrency control.
    This prevents database corruption when multiple users try to save data simultaneously.
    """
    
    def __init__(self, conn_str=None):
        """Initialize the database manager with a connection string."""
        self.conn_str = conn_str
        self.lock_dir = os.path.join(os.path.expanduser("~"), ".db_locks")
        
        # Create lock directory if it doesn't exist
        if not os.path.exists(self.lock_dir):
            os.makedirs(self.lock_dir)
    
    @contextmanager
    def get_connection(self, db_path=None):
        """
        Get a database connection with proper locking to prevent concurrent access issues.
        
        Args:
            db_path: Optional path to the database file. If provided, it will override the conn_str.
            
        Yields:
            A pyodbc connection object.
        """
        # Determine the connection string to use
        if db_path:
            conn_str = f"DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={db_path};"
        else:
            conn_str = self.conn_str
            
        if not conn_str:
            raise ValueError("No connection string provided")
        
        # Extract database path from connection string for lock file
        db_path = conn_str.split("DBQ=")[1].split(";")[0]
        
        # Create a unique lock file name based on the database path
        lock_file = os.path.join(self.lock_dir, 
                                f"{os.path.basename(db_path).replace('.', '_')}.lock")
        
        # Acquire lock with timeout and exponential backoff
        lock = filelock.FileLock(lock_file)
        max_attempts = 5
        attempt = 0
        
        while attempt < max_attempts:
            try:
                # Try to acquire the lock with a timeout
                lock.acquire(timeout=2)  # 2 second timeout
                break
            except filelock.Timeout:
                attempt += 1
                if attempt >= max_attempts:
                    raise TimeoutError(f"Could not acquire lock for database {db_path} after {max_attempts} attempts")
                
                # Exponential backoff with jitter
                wait_time = (2 ** attempt) + (random.random() * 0.5)
                print(f"Database busy, retrying in {wait_time:.2f} seconds...")
                time.sleep(wait_time)
        
        conn = None
        try:
            # Try multiple connection string variations
            conn_variations = [
                conn_str,
                conn_str.replace(";", ";ReadOnly=0;"),
                conn_str.replace(";", ";Exclusive=0;"),
                conn_str.replace(";", ";ReadOnly=0;Exclusive=0;")
            ]
            
            connection_error = None
            for i, variation in enumerate(conn_variations):
                try:
                    conn = pyodbc.connect(variation, timeout=10)
                    break
                except pyodbc.Error as e:
                    connection_error = e
                    continue
            
            if conn is None:
                raise connection_error or Exception("All connection attempts failed")
            
            yield conn
            
            # Commit changes if no exceptions occurred
            conn.commit()
        except Exception as e:
            # Roll back on error
            if conn:
                try:
                    conn.rollback()
                except:
                    pass
            raise e
        finally:
            # Always close the connection and release the lock
            if conn:
                try:
                    conn.close()
                except:
                    pass
            lock.release()
    
    def execute_query(self, query, params=None, db_path=None):
        """
        Execute a query with parameters and return the results.
        
        Args:
            query: SQL query string
            params: Parameters for the query (tuple)
            db_path: Optional path to the database file
            
        Returns:
            List of results or None for non-SELECT queries
        """
        with self.get_connection(db_path) as conn:
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            # Check if the query is a SELECT query
            if query.strip().upper().startswith("SELECT"):
                return cursor.fetchall()
            return None
    
    def execute_many(self, query, params_list, db_path=None):
        """
        Execute a query with multiple sets of parameters.
        
        Args:
            query: SQL query string
            params_list: List of parameter tuples
            db_path: Optional path to the database file
        """
        with self.get_connection(db_path) as conn:
            cursor = conn.cursor()
            cursor.executemany(query, params_list)
    
    def get_tables(self, db_path=None):
        """
        Get a list of tables in the database.
        
        Args:
            db_path: Optional path to the database file
            
        Returns:
            List of table names
        """
        with self.get_connection(db_path) as conn:
            cursor = conn.cursor()
            tables = [table[2] for table in cursor.tables(tableType='TABLE')]
            return tables
    
    def find_matching_table(self, subprocess, db_path=None):
        """
        Find a table that matches the given subprocess name.
        
        Args:
            subprocess: Subprocess name to match
            db_path: Optional path to the database file
            
        Returns:
            Matched table name or None if no match found
        """
        tables = self.get_tables(db_path)
        
        # Map subprocess to table name by finding the closest match
        table_mapping = {}
        for table in tables:
            # Remove brackets and 'Audits' suffix for comparison
            clean_table = table.replace('[', '').replace(']', '')
            if 'Audits' in clean_table:
                clean_table = clean_table.replace(' Audits', '')
            table_mapping[clean_table] = table
        
        # Find the best match for the subprocess
        table_name = table_mapping.get(subprocess)

        # If no exact match, try partial matching with full table name
        if not table_name:
            # First try to find if sub_process is contained within any table name
            for key, value in table_mapping.items():
                if subprocess.lower() in key.lower():
                    table_name = value
                    break
            
            # If still not found, try if any table name is contained in sub_process
            if not table_name:
                for key, value in table_mapping.items():
                    if key.lower() in subprocess.lower():
                        table_name = value
                        break

        # Ensure table name is properly formatted with square brackets
        if table_name and not (table_name.startswith('[') and table_name.endswith(']')):
            table_name = f"[{table_name}]"
            
        return table_name

# Create a global instance for easy access
db_manager = DatabaseManager()