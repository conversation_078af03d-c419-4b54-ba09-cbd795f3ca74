import ctypes
import sys
import os

def fix_cursor_visibility():
    """Fix cursor visibility issues in Kivy applications on Windows"""
    if sys.platform == 'win32':
        # Set process as DPI aware to fix cursor scaling issues
        try:
            ctypes.windll.user32.SetProcessDPIAware()
        except:
            pass
        
        # Set cursor visibility through Windows API
        try:
            ctypes.windll.user32.ShowCursor(1)
        except:
            pass
        
        # Create environment variable to force cursor visibility
        os.environ['KIVY_CURSOR_VISIBLE'] = '1'