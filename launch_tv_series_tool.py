import os
import sys
import subprocess

def main():
    """Launch the TV Series TT Tool"""
    try:
        # Get the directory of this script
        script_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Path to the TV Series TT Tool script
        tool_path = os.path.join(script_dir, "TV Series TT Tool.py")
        
        # Check if the file exists
        if not os.path.exists(tool_path):
            print(f"Error: Could not find the tool at {tool_path}")
            input("Press Enter to exit...")
            return
        
        # Launch the tool
        print("Launching TV Series TT Tool...")
        subprocess.run([sys.executable, tool_path])
        
    except Exception as e:
        print(f"Error launching tool: {str(e)}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()