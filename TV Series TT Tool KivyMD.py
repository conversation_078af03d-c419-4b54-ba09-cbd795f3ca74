from kivymd.app import <PERSON><PERSON><PERSON>
from kivymd.uix.boxlayout import MDB<PERSON>Layout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.label import MDLabel
from kivymd.uix.textfield import MDTextField
from kivymd.uix.button import MDRaisedButton, MDFlatButton
from kivymd.uix.selectioncontrol import MDSelectionControl
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.dialog import MDDialog
from kivymd.uix.card import MDCard
from kivy.uix.widget import Widget
from kivy.metrics import dp
import pyodbc
import datetime
import getpass

# Hardcoded blurb-to-issue mapping
blurbs_dict = {
    "Season Missing": "Season is missing on the EDP/Detail Page",
    "Missing Offers/Avails": "Offers/Avails are missing in EDP/CRMS",
    "Episode Missing": "Episode is missing in EDP/Detail Page",
    "Expired Offers/Avails": "Offers/Avails have expired in EDP/CRMS",
    "Merge Issue": "Title requires merging in the catalog",
    "Unmerge Issue": "Incorrectly merged across vendors or Seasons merged incorrectly, need separate metadata pages",
    "Tech Issue": "Technical issue observed, further investigation needed",
    "Offers Takendown": "Offers have been taken down in EDP",
    "VCID Error": "Episode is missing on the site due to a VCID error",
    "Pricing Issue": "Play/Buy button is missing on the detail page due to a pricing issue",
    "Orphaned Season/Episode": "The Season/Episode are not aligned within the Season/Episode hierarchy",
    "Offer Creations": "Live avails exist, but the offer hasn't been created in EDP",
    "Play Button Missing": "Play/Buy button is missing on the Detail Page",
}

# Access DB path
access_db_path = r"\\ant\dept-in\Digiflex\DF-Cataloghealth\Quality Products\Tickets\TT Details Database.accdb"

# Field headers
fields = [
    "RCA", "Territory", "Offer Type", "Partner", "Series GTI",
    "Series Name", "Season/Episode Number", "EDP", "D2C",
    "Detail Page", "Avails", "DEAL Link", "WMV Link", "Issue"
]

class TicketEntryApp(MDApp):
    def __init__(self):
        super().__init__()
        self.entries = {}
        self.selected_issues = []
        self.offer_type_menu = None
        self.cti_menus = []

    def build(self):
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        
        # Main layout
        main_layout = MDBoxLayout(orientation="vertical", spacing=dp(10), padding=dp(20))
        
        # Header with title and submit button
        header = MDBoxLayout(orientation="horizontal", size_hint_y=None, height=dp(60))
        title = MDLabel(text="Ticket Entry Tool", theme_text_color="Primary", 
                       font_style="H5", size_hint_x=0.8)
        submit_btn = MDRaisedButton(text="Submit", size_hint_x=0.2, 
                                   on_release=self.preview_data)
        header.add_widget(title)
        header.add_widget(submit_btn)
        main_layout.add_widget(header)
        
        # Scrollable content
        scroll = MDScrollView()
        content = MDBoxLayout(orientation="vertical", spacing=dp(15), 
                             adaptive_height=True, padding=dp(10))
        
        # Form fields
        form_card = MDCard(padding=dp(20), spacing=dp(10), adaptive_height=True)
        form_layout = MDBoxLayout(orientation="vertical", spacing=dp(15), adaptive_height=True)
        
        for field in fields:
            field_layout = MDBoxLayout(orientation="horizontal", spacing=dp(10), 
                                     adaptive_height=True, size_hint_y=None)
            
            label = MDLabel(text=field, size_hint_x=0.3, theme_text_color="Primary",
                           font_style="Subtitle1")
            field_layout.add_widget(label)
            
            if field == "Offer Type":
                # Dropdown for Offer Type
                offer_field = MDTextField(text="", size_hint_x=0.7, readonly=True)
                offer_field.bind(on_focus=self.open_offer_menu)
                self.entries[field] = offer_field
                field_layout.add_widget(offer_field)
            elif field in ["RCA", "Issue"]:
                # Multiline text fields
                text_field = MDTextField(text="", multiline=True, size_hint_x=0.7,
                                       max_text_length=1000)
                text_field.height = dp(120)
                self.entries[field] = text_field
                field_layout.add_widget(text_field)
            else:
                # Regular text fields
                text_field = MDTextField(text="", size_hint_x=0.7)
                self.entries[field] = text_field
                field_layout.add_widget(text_field)
            
            field_layout.height = dp(80) if field in ["RCA", "Issue"] else dp(60)
            form_layout.add_widget(field_layout)
        
        form_card.add_widget(form_layout)
        content.add_widget(form_card)
        
        # CTI Section
        cti_card = MDCard(padding=dp(20), spacing=dp(10), adaptive_height=True)
        cti_layout = MDBoxLayout(orientation="vertical", spacing=dp(10), adaptive_height=True)
        
        cti_title = MDLabel(text="CTI", theme_text_color="Primary", font_style="H6")
        cti_layout.add_widget(cti_title)
        
        cti_row = MDBoxLayout(orientation="horizontal", spacing=dp(10), 
                             adaptive_height=True, size_hint_y=None, height=dp(60))
        
        # CTI Dropdowns
        cti_values = ["Digital Video", "Digiflex", "TV Series Integrity"]
        for i, value in enumerate(cti_values):
            cti_field = MDTextField(text=value, readonly=True, size_hint_x=0.33)
            cti_row.add_widget(cti_field)
        
        cti_layout.add_widget(cti_row)
        cti_card.add_widget(cti_layout)
        content.add_widget(cti_card)
        
        # Blurbs Section
        blurbs_card = MDCard(padding=dp(20), spacing=dp(10), adaptive_height=True)
        blurbs_layout = MDBoxLayout(orientation="vertical", spacing=dp(10), adaptive_height=True)
        
        blurbs_title = MDLabel(text="Blurbs", theme_text_color="Primary", font_style="H6")
        blurbs_layout.add_widget(blurbs_title)
        
        # Blurb buttons grid
        blurbs_grid = MDGridLayout(cols=4, spacing=dp(10), adaptive_height=True)
        
        for blurb, issue in blurbs_dict.items():
            btn = MDFlatButton(text=blurb, on_release=lambda x, i=issue: self.set_issue(i))
            btn.text_color = self.theme_cls.primary_color
            blurbs_grid.add_widget(btn)
        
        # Reset button
        reset_btn = MDRaisedButton(text="Reset Selection", md_bg_color="red",
                                  on_release=self.reset_issue_selection)
        
        blurbs_layout.add_widget(blurbs_grid)
        blurbs_layout.add_widget(reset_btn)
        blurbs_card.add_widget(blurbs_layout)
        content.add_widget(blurbs_card)
        
        scroll.add_widget(content)
        main_layout.add_widget(scroll)
        
        return main_layout

    def open_offer_menu(self, instance, focus):
        if focus:
            menu_items = [
                {"text": "", "viewclass": "OneLineListItem", "on_release": lambda: self.set_offer_type("")},
                {"text": "Prime SVOD", "viewclass": "OneLineListItem", "on_release": lambda: self.set_offer_type("Prime SVOD")},
                {"text": "3P Subs/Channel", "viewclass": "OneLineListItem", "on_release": lambda: self.set_offer_type("3P Subs/Channel")},
                {"text": "TVOD", "viewclass": "OneLineListItem", "on_release": lambda: self.set_offer_type("TVOD")},
                {"text": "AVOD", "viewclass": "OneLineListItem", "on_release": lambda: self.set_offer_type("AVOD")},
                {"text": "FVOD", "viewclass": "OneLineListItem", "on_release": lambda: self.set_offer_type("FVOD")},
                {"text": "SVOD(Linear)", "viewclass": "OneLineListItem", "on_release": lambda: self.set_offer_type("SVOD(Linear)")},
            ]
            self.offer_type_menu = MDDropdownMenu(caller=instance, items=menu_items)
            self.offer_type_menu.open()

    def set_offer_type(self, value):
        self.entries["Offer Type"].text = value
        self.offer_type_menu.dismiss()

    def set_issue(self, issue_text):
        if len(self.selected_issues) < 2:
            self.selected_issues.append(issue_text)

        if len(self.selected_issues) == 1:
            self.entries["Issue"].text = self.selected_issues[0]
        elif len(self.selected_issues) == 2:
            combined = f"{self.selected_issues[0]} due to {self.selected_issues[1]}"
            self.entries["Issue"].text = combined

    def reset_issue_selection(self, instance):
        self.selected_issues = []
        self.entries["Issue"].text = ""

    def submit_data(self, data):
        user_id = getpass.getuser()
        today = datetime.date.today()
        week_num = today.isocalendar()[1]

        try:
            conn_str = (
                r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
                fr'DBQ={access_db_path};'
            )
            conn = pyodbc.connect(conn_str)
            cursor = conn.cursor()

            insert_query = f"""
            INSERT INTO [TT Template] (
                [RCA], [Territory], [Offer Type], [Partner], [Series GTI],
                [Series Name], [Season/Episode Number], [EDP], [D2C],
                [Detail Page], [Avails], [DEAL Link], [WMV Link], [Issue],
                [Date], [Week], [User ID]
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            cursor.execute(insert_query, *[data[field] for field in fields], today, week_num, user_id)
            conn.commit()
            conn.close()
            
            # Success dialog
            dialog = MDDialog(title="Success", text="Data submitted successfully.")
            dialog.open()

            # Clear all fields for new entry
            for field, entry in self.entries.items():
                entry.text = ""
            self.reset_issue_selection(None)

        except Exception as e:
            # Error dialog
            dialog = MDDialog(title="Database Error", text=f"Failed to write to database.\n{str(e)}")
            dialog.open()

    def preview_data(self, instance):
        data = {field: self.entries[field].text for field in fields}

        if any(v.strip() == "" for v in data.values()):
            dialog = MDDialog(title="Missing Data", text="Please fill in all fields before submitting.")
            dialog.open()
            return

        # Create preview dialog
        preview_text = "\n".join([f"{field}: {data[field]}" for field in fields])
        
        def confirm_submit(instance):
            preview_dialog.dismiss()
            self.submit_data(data)
        
        preview_dialog = MDDialog(
            title="Preview Data",
            text=preview_text,
            buttons=[
                MDFlatButton(text="Cancel", on_release=lambda x: preview_dialog.dismiss()),
                MDRaisedButton(text="Confirm", on_release=confirm_submit)
            ]
        )
        preview_dialog.open()

if __name__ == "__main__":
    TicketEntryApp().run()